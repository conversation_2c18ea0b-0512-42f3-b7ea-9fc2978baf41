"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  isSiteVerified,
} from "@/components/ssrTools";

const postPaginatedPolicies = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/policies/paginated-policies`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getPolicySetInformationFormCheckPolicy = async (lang, policyNo) => {
  const isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);
  if (isWebsiteVerified) {
    let res = await fetch(
      `${process.env.API_URL}offers/${policyNo}/policies/set-information-form-check-policy`,
      {
        cache: "no-store",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getToken()}`,
          "Accept-Language": getAcceptLanguage(lang),
        },
      }
    );
    res = await validResponse(res);
    return JSON.parse(res);
  } else {
    throw new Error("reCaptchaFailed");
  }
};

export { postPaginatedPolicies, getPolicySetInformationFormCheckPolicy };
