"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  isSiteVerified,
  logError,
} from "@/components/ssrTools";

const postPaginatedPolicies = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/policies/paginated-policies`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postPaginatedPolicies", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getPolicySetInformationFormCheckPolicy = async (
  lang,
  policyNo,
  values
) => {
  const isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    let fResponse;
    const request = {
      url: `${process.env.API_URL}offers/${policyNo}/policies/set-information-form-check-policy`,
      options: {
        cache: "no-store",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getToken()}`,
          "Accept-Language": getAcceptLanguage(lang),
        },
      },
    };

    try {
      fResponse = await fetch(request.url, request.options);
      const vResponse = await validResponse(fResponse);
      return JSON.parse(vResponse);
    } catch (err) {
      logError(
        "getPolicySetInformationFormCheckPolicy",
        "fetchFailed",
        request,
        fResponse,
        err
      );
      throw err;
    }
  } else {
    logError(
      "getPolicySetInformationFormCheckPolicy",
      "reCaptchaFailed",
      values,
      undefined,
      undefined
    );
    return { status: "ERROR", message: "reCaptchaFailed" };
  }
};

export { postPaginatedPolicies, getPolicySetInformationFormCheckPolicy };
