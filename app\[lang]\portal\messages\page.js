"use server";

import { getDictionary } from "@/dictionaries";
import Messages from "./messages";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.messages.title} | ${dict.public.title}`,
  };
}

export default async function MessagesPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <Messages dict={dict} lang={lang} />;
}
