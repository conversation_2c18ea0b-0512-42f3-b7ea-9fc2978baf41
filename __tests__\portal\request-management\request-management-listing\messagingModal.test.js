import {
  render,
  screen,
  act,
  waitFor,
  fireEvent,
} from "@testing-library/react";
import { getLocale } from "@/components/tools";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  getRequestDetail,
  getRequestDownloadFile,
  getRequestStatus,
  postAddRequestDetail,
  putUpdateRequestStatus,
} from "@/services/requests";
import MessagingModal from "@/app/[lang]/portal/request-management/request-management-listing/messagingModal";
import { getDictionary } from "@/dictionaries";

// Mock RequestReviewModal
jest.mock("@/components/containers/requestReviewModal", () => ({
  __esModule: true,
  default: jest.fn(({ selectedData }) => {
    return (
      <div data-testid="mock-request-review-modal">
        {selectedData ? selectedData.id : "ID Yok"}
      </div>
    );
  }),
}));

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/components/tools", () => {
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();
  const mockFormatDate = jest.fn(() => "28/03/2025, 10:53:34");
  const mockHandleDownloadFile = jest.fn();
  const mockGetSpinIndicator = <div>Loading...</div>;

  return {
    useResponseHandler: () => ({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    }),
    formatDate: mockFormatDate,
    handleDownloadFile: mockHandleDownloadFile,
    getSpinIndicator: mockGetSpinIndicator,
    getLocale: { en: "en", tr: "tr" },
    __mocks: {
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
      formatDate: mockFormatDate,
      handleDownloadFile: mockHandleDownloadFile,
    },
  };
});

jest.mock("@/services/requests", () => ({
  getRequestDetail: jest.fn(),
  getRequestDownloadFile: jest.fn(),
  getRequestStatus: jest.fn(),
  putUpdateRequestStatus: jest.fn(),
  postAddRequestDetail: jest.fn(),
}));

jest.mock("next/image", () => ({
  __esModule: true,
  default: (props) => {
    return <img {...props} alt="EmaaPicture" />;
  },
}));

// Mock Buffer for Node.js environment
global.Buffer = Buffer;

jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    Popconfirm: ({
      children,
      onConfirm,
      title,
      okText,
      cancelText,
      ...props
    }) => {
      return (
        <div data-testid="popconfirm-mock">
          {children}
          <button data-testid="mock-confirm-yes" onClick={onConfirm}>
            {okText || "Yes"}
          </button>
        </div>
      );
    },
    Select: ({ children, onChange, options, value, placeholder, ...props }) => {
      return (
        <select
          data-testid="mock-select"
          onChange={(e) => onChange && onChange(parseInt(e.target.value))}
          value={value}
          {...props}
        >
          <option value="">{placeholder}</option>
          {options?.map((option, index) => (
            <option key={index} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      );
    },
    Upload: ({
      children,
      beforeUpload,
      onRemove,
      onChange,
      fileList,
      ...props
    }) => {
      return (
        <div data-testid="mock-upload">
          <input
            type="file"
            data-testid="file-input"
            onChange={(e) => {
              const file = e.target.files[0];
              if (file) {
                const mockFile = {
                  uid: Date.now().toString(),
                  name: file.name,
                  originFileObj: file,
                };
                if (beforeUpload) beforeUpload(mockFile);
                if (onChange) {
                  onChange({
                    fileList: [...(fileList || []), mockFile],
                  });
                }
              }
            }}
          />
          {children}
        </div>
      );
    },
  };
});

Object.defineProperty(window, "location", {
  value: { reload: jest.fn() },
  writable: true,
});

// Mock process.env
Object.defineProperty(process, "env", {
  value: {
    NEXT_PUBLIC_USER_TYPE: "PORTAL",
  },
});

// #region mock data
const mockSelectedData = {
  id: 105,
  policyNo: "TEST123",
  channel: "1001",
  userName: "EREN FILIZ",
  productNo: "101",
  requestReason: "Rücu - Rücu Talepleri",
  subject: "BİRİM YÖNLENDİRME MAİLİ",
  requestDate: "2025-03-28T10:16:01",
  detail:
    "BİRİM YÖNLENDİRME MAİLİ BİRİM YÖNLENDİRME MAİLİ BİRİM YÖNLENDİRME MAİLİ",
  status: "COZUM_BEKLENIYOR",
  productName: "TRAFİK SİGORTASI",
  unit: "Rücu Servisi",
  agencyName: "GRAVİS SİGORTA ARACILIK HİZMETLERİ LİMİTED ŞİRKETİ",
  adminName: "EMAA SİGORTA",
  closureDate: null,
  closureDateTime: null,
};

const mockDetailsData = {
  id: 105,
  userName: "EREN FILIZ",
  requestDate: "2025-03-28T10:16:01",
  status: "COZUM_BEKLENIYOR",
  closureDate: null,
  details: [
    {
      detailId: 76,
      comment: "rücu servisi kontrol edecektir.",
      commentDate: "2025-03-28T10:17:06",
      commentUser: "EMAA SİGORTA",
      isEmaa: true,
      messageGroupId: 0,
    },
    {
      detailId: 77,
      comment: "test rtl",
      commentDate: "2025-03-28T10:53:34",
      commentUser: "EMAA SİGORTA",
      isEmaa: true,
      messageGroupId: 1,
    },
  ],
  messages: [
    {
      fileId: 276,
      fileName: "testImg.png",
      fileExtension: ".png",
      fileCreatedAt: "28/03/2025, 10:53:34",
      messageId: 77,
    },
  ],
};

const mockStatusData = [
  {
    code: "5",
    value: "ACENTEDEN_BILGI_BEKLENIYOR",
  },
  {
    code: "6",
    value: "TALEP_KAPANDI",
  },
  {
    code: "7",
    value: "COZUM_BEKLENIYOR",
  },
];

// #endregion mock data

describe("MessagingModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language ${lang}`, () => {
      let dict,
        setIsModalOpen,
        getOffersList,
        updatedFilteredpagination,
        defaultModel;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        setIsModalOpen = jest.fn();
        getOffersList = jest.fn();
        updatedFilteredpagination = jest.fn();
        defaultModel = { page: 1, limit: 10 };

        const tools = require("@/components/tools");
        tools.__mocks.handleResponse.mockClear();
        tools.__mocks.handleError.mockClear();
        tools.__mocks.formatDate.mockClear();
        tools.__mocks.handleDownloadFile.mockClear();

        getRequestDetail.mockResolvedValue({
          status: "SUCCESS",
          data: mockDetailsData,
        });

        getRequestDownloadFile.mockResolvedValue("base64MockData");

        getRequestStatus.mockResolvedValue({
          status: "SUCCESS",
          data: mockStatusData,
        });

        postAddRequestDetail.mockResolvedValue({
          status: "SUCCESS",
          data: { id: 123 },
        });

        putUpdateRequestStatus.mockResolvedValue({
          status: "SUCCESS",
          data: {},
        });

        await act(async () => {
          render(
            <MessagingModal
              dict={dict}
              lang={lang}
              isModalOpen={true}
              setIsModalOpen={setIsModalOpen}
              selectedData={mockSelectedData}
              getOffersList={getOffersList}
              updatedFilteredpagination={updatedFilteredpagination}
              currentPageNumber={1}
              defaultModel={defaultModel}
            />
          );
        });
      });

      test("modal: Başlığı ve içeriği doğru şekilde render edilmeli.", async () => {
        expect(
          screen.getByText(
            dict.requestManagement.requestManagementListing.requestReview
          )
        ).toBeInTheDocument();
        expect(
          screen.getByText(
            dict.requestManagement.requestManagementListing.messageHistory
          )
        ).toBeInTheDocument();

        const requestReviewModal = screen.getByTestId(
          "mock-request-review-modal"
        );
        expect(requestReviewModal).toBeInTheDocument();
        expect(requestReviewModal).toHaveTextContent(
          mockSelectedData.id.toString()
        );
        expect(setIsModalOpen).not.toHaveBeenCalled();
      });
      // test history start
      test("Message History: Veriler içeri yüklenmeli", async () => {
        expect(getRequestDetail).toHaveBeenCalledTimes(1);
        expect(getRequestStatus).toHaveBeenCalledTimes(1);
        await waitFor(() => {
          expect(screen.getAllByTestId("messageTest").length).toBe(2);
          expect(screen.getAllByText("28/03/2025, 10:53:34")).toHaveLength(2);

          expect(
            screen.getByText(mockDetailsData.details[0].comment)
          ).toHaveTextContent("rücu servisi kontrol edecektir.");

          expect(
            screen.getByText(mockDetailsData.details[1].comment)
          ).toHaveTextContent("test rtl");
        });

        const fileLink = screen.getByTestId("downloadFileLink");
        expect(fileLink).toHaveTextContent("testImg.png");
        fireEvent.click(fileLink);

        await waitFor(() => {
          expect(getRequestDownloadFile).toHaveBeenCalledTimes(1);
          expect(getRequestDownloadFile).toHaveBeenCalledWith(
            lang,
            mockSelectedData.id,
            mockDetailsData.messages[0].fileId
          );
        });

        const image = await screen.findAllByAltText("EmaaPicture");
        const images = screen.getAllByAltText("EmaaPicture");
        expect(images).toHaveLength(2);
      });

      test("Yeni Mesaj Gönderme İşlevi: Message Group seçimi ile birlikte çalışmalı", async () => {
        expect(
          screen.getByPlaceholderText(
            dict.requestManagement.requestManagementListing.typeMessage
          )
        ).toBeInTheDocument();

        const messageInput = screen.getByPlaceholderText(
          dict.requestManagement.requestManagementListing.typeMessage
        );

        // Mesaj yazalım
        fireEvent.change(messageInput, {
          target: { value: "Mock Yeni Mesaj" },
        });

        // Message group seçelim
        const messageGroupSelect = screen.getByTestId("mock-select");
        fireEvent.change(messageGroupSelect, {
          target: { value: "0" },
        });

        // Submit butonunu tıklayalım
        const submitButton = screen.getByTestId("sendMessage");
        fireEvent.click(submitButton);

        await waitFor(() => {
          expect(postAddRequestDetail).toHaveBeenCalledTimes(1);
          // API çağrısının doğru parametrelerle yapıldığını kontrol et
          const callArgs = postAddRequestDetail.mock.calls[0];
          expect(callArgs[0]).toBe(lang);
          const requestBody = callArgs[1];
          expect(requestBody.Comment).toBe("Mock Yeni Mesaj");
          expect(requestBody.messageGroupId).toBe(0); // Genel seçeneği
          expect(requestBody.TalepNo).toBe(mockSelectedData.id);
          expect(requestBody.PolicyNo).toBe(mockSelectedData.policyNo);
          expect(requestBody.RequestReason).toBe(
            mockSelectedData.requestReason
          );
          expect(requestBody.Subject).toBe(mockSelectedData.subject);
          expect(requestBody.ProductName).toBe(mockSelectedData.productName);
          expect(requestBody.AgencyName).toBe("Emaa Sigorta A.Ş.");
        });
      });

      test("File Upload: Dosya yükleme işlevi çalışmalı", async () => {
        const fileInput = screen.getByTestId("file-input");
        const mockFile = new File(["test content"], "test.pdf", {
          type: "application/pdf",
        });

        fireEvent.change(fileInput, {
          target: { files: [mockFile] },
        });

        await waitFor(() => {
          // File upload mock'unun çalıştığını kontrol et
          expect(fileInput).toBeInTheDocument();
        });
      });

      test("Send Button: Mesaj boşken disabled olmalı", async () => {
        const submitButton = screen.getByTestId("sendMessage");
        expect(submitButton).toBeDisabled();

        const messageInput = screen.getByPlaceholderText(
          dict.requestManagement.requestManagementListing.typeMessage
        );

        // Boş mesaj yazalım
        fireEvent.change(messageInput, {
          target: { value: "   " }, // Sadece boşluk
        });
        expect(submitButton).toBeDisabled();

        // Gerçek mesaj yazalım
        fireEvent.change(messageInput, {
          target: { value: "Gerçek mesaj" },
        });
        expect(submitButton).not.toBeDisabled();
      });

      test("Save & Close Status Değiştirme İşlevi", async () => {
        const saveAndExitButton = screen.getByTestId("saveAndExit");
        expect(saveAndExitButton).toBeInTheDocument();
        expect(saveAndExitButton).toHaveTextContent(dict.public.saveAndExit);

        fireEvent.click(saveAndExitButton);

        await act(async () => {
          await new Promise((resolve) => setTimeout(resolve, 100));
        });

        const confirmButton = screen.getByTestId("mock-confirm-yes");
        fireEvent.click(confirmButton);
      });

      // test("Closed Request: Kapalı talep durumunda form disabled olmalı", async () => {
      //   // Kapalı talep durumu için mock data
      //   const closedRequestData = {
      //     ...mockDetailsData,
      //     status: "TALEP_KAPANDI",
      //   };

      //   getRequestDetail.mockResolvedValue({
      //     status: "SUCCESS",
      //     data: closedRequestData,
      //   });

      //   await act(async () => {
      //     render(
      //       <MessagingModal
      //         dict={dict}
      //         lang={lang}
      //         isModalOpen={true}
      //         setIsModalOpen={setIsModalOpen}
      //         selectedData={{ ...mockSelectedData, status: "TALEP_KAPANDI" }}
      //         getOffersList={getOffersList}
      //         updatedFilteredpagination={updatedFilteredpagination}
      //         currentPageNumber={1}
      //         defaultModel={defaultModel}
      //       />
      //     );
      //   });

      //   await waitFor(() => {
      //     //const messageInput = screen.getByTestId("messageInput");
      //     // const submitButton = screen.getByTestId("sendMessage");
      //     //const saveAndExitButton = screen.getByTestId("saveAndExit");

      //     expect(messageInput).toBeDisabled();
      //     // expect(submitButton).toBeDisabled();
      //     // expect(saveAndExitButton).toBeDisabled();
      //   });
      // });

      // test("Error Handling: API hataları doğru şekilde işlenmeli", async () => {
      //   const tools = require("@/components/tools");

      //   getRequestDetail.mockRejectedValue(new Error("API Error"));

      //   await act(async () => {
      //     render(
      //       <MessagingModal
      //         dict={dict}
      //         lang={lang}
      //         isModalOpen={true}
      //         setIsModalOpen={setIsModalOpen}
      //         selectedData={mockSelectedData}
      //         getOffersList={getOffersList}
      //         updatedFilteredpagination={updatedFilteredpagination}
      //         currentPageNumber={1}
      //         defaultModel={defaultModel}
      //       />
      //     );
      //   });

      //   await waitFor(() => {
      //     expect(tools.__mocks.handleError).toHaveBeenCalled();
      //   });
      // });

      test("Modal Close: Modal kapatma işlevi çalışmalı", async () => {
        const closeButton = screen.getByTestId("closeButton");
        if (closeButton) {
          fireEvent.click(closeButton);
          expect(setIsModalOpen).toHaveBeenCalledWith(false);
        }
      });
    });
  });
});
