"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const postPaginatedRequests = async (lang, values) => {
  let res = await fetch(`${process.env.API_URL}offers/paginated-offers`, {
    cache: "no-store",
    method: "POST",
    headers: {
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const postAddRequestDetail = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/add-offer-detail?TalepNo=${values.TalepNo}&Comment=${values.Comment}&CommentDate=${values.CommentDate}&CommentUser=${values.CommentUser}&PolicyNo=${values.PolicyNo}&RequestReason=${values.RequestReason}&Unit=${values.Unit}&Subject=${values.Subject}&ProductName=${values.ProductName}&UserName=${values.UserName}&MessageGroupId=${values.messageGroupId}`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: values.File,
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getRequestDetail = async (lang, id) => {
  let res = await fetch(`${process.env.API_URL}offers/${id}/offer-detail`, {
    cache: "no-store",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const getRequestUnits = async (lang) => {
  let res = await fetch(`${process.env.API_URL}offers/offer-unit-list`, {
    cache: "no-store",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const putUpdateRequestStatus = async (lang, values) => {
  let res = await fetch(`${process.env.API_URL}offers/offer-update`, {
    cache: "no-store",
    method: "PUT",
    headers: {
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const putUpdateRequestInCharge = async (lang, id) => {
  let res = await fetch(
    `${process.env.API_URL}offer/${id}/add-in-charge-to-offer`,
    {
      cache: "no-store",
      method: "PUT",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getRequestFiles = async (lang, id) => {
  let res = await fetch(
    `${process.env.API_URL}offers/${id}/offer-request-files`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getRequestDownloadFile = async (lang, requestId, fileId) => {
  const res = await fetch(
    `${process.env.API_URL}offers/${requestId}/offer-request-files/${fileId}/download`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  if (res?.ok) {
    const buffer = await res.arrayBuffer();
    const base64 = Buffer.from(buffer).toString("base64");
    return base64;
  } else {
    return null;
  }
};

const getRequestStatus = async (lang) => {
  let res = await fetch(`${process.env.API_URL}offers/get-offer-status`, {
    cache: "no-store",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const getRequestStatusListing = async (lang) => {
  let res = await fetch(`${process.env.API_URL}offers/get-offer-statu-list`, {
    cache: "no-store",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};
const getPortalOfferRequestUnits = async (lang, val = null) => {
  let res = await fetch(
    `${process.env.API_URL}offers/get-offer-unit${val.requestReason ? `?requestReason=${val.requestReason}` : ""}`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};
const getPortalOfferRequestReasons = async (lang, val = null) => {
  let res = await fetch(
    `${process.env.API_URL}offers/get-offer-request-reason-list${val ? `${val.productNo != false ? `?productNo=${val.productNo}&` : `?`}unitName=${val.unitName.trim()}` : ""}`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const postPrintExcelDoc = async (lang, values) => {
  let res = await fetch(`${process.env.API_URL}offers/offers-report`, {
    cache: "no-store",
    method: "POST",
    headers: {
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });
  if (res?.ok) {
    const buffer = await res.arrayBuffer();
    const base64 = Buffer.from(buffer).toString("base64");
    return base64;
  } else {
    return null;
  }
};

export {
  postPaginatedRequests,
  postAddRequestDetail,
  getRequestDetail,
  getRequestUnits,
  putUpdateRequestStatus,
  putUpdateRequestInCharge,
  getRequestFiles,
  getRequestDownloadFile,
  getRequestStatus,
  getRequestStatusListing,
  getPortalOfferRequestReasons,
  getPortalOfferRequestUnits,
  postPrintExcelDoc,
};
