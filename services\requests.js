"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const postPaginatedRequests = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/paginated-offers`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postPaginatedRequests", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postAddRequestDetail = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/add-offer-detail?TalepNo=${values.TalepNo}&Comment=${values.Comment}&CommentDate=${values.CommentDate}&CommentUser=${values.CommentUser}&PolicyNo=${values.PolicyNo}&RequestReason=${values.RequestReason}&Unit=${values.Unit}&Subject=${values.Subject}&ProductName=${values.ProductName}&UserName=${values.UserName}&MessageGroupId=${values.messageGroupId}`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: values.File,
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postAddRequestDetail", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getRequestDetail = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${id}/offer-detail`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getRequestDetail", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getRequestUnits = async (lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/offer-unit-list`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getRequestUnits", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const putUpdateRequestStatus = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/offer-update`,
    options: {
      cache: "no-store",
      method: "PUT",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("putUpdateRequestStatus", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const putUpdateRequestInCharge = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offer/${id}/add-in-charge-to-offer`,
    options: {
      cache: "no-store",
      method: "PUT",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "putUpdateRequestInCharge",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

const getRequestFiles = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${id}/offer-request-files`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getRequestFiles", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getRequestDownloadFile = async (lang, requestId, fileId) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${requestId}/offer-request-files/${fileId}/download`,
    options: {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    if (fResponse?.ok) {
      const buffer = await fResponse.arrayBuffer();
      const base64 = Buffer.from(buffer).toString("base64");
      return base64;
    } else {
      return null;
    }
  } catch (err) {
    logError("getRequestDownloadFile", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getRequestStatus = async (lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/get-offer-status`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getRequestStatus", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getRequestStatusListing = async (lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/get-offer-statu-list`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getRequestStatusListing", "fetchFailed", request, fResponse, err);
    throw err;
  }
};
const getPortalOfferRequestUnits = async (lang, val = null) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/get-offer-unit${val.requestReason ? `?requestReason=${val.requestReason}` : ""}`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "getPortalOfferRequestUnits",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};
const getPortalOfferRequestReasons = async (lang, val = null) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/get-offer-request-reason-list${val ? `${val.productNo != false ? `?productNo=${val.productNo}&` : `?`}unitName=${val.unitName.trim()}` : ""}`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "getPortalOfferRequestReasons",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

const postPrintExcelDoc = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/offers-report`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    if (fResponse?.ok) {
      const buffer = await fResponse.arrayBuffer();
      const base64 = Buffer.from(buffer).toString("base64");
      return base64;
    } else {
      return null;
    }
  } catch (err) {
    logError("postPrintExcelDoc", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

export {
  postPaginatedRequests,
  postAddRequestDetail,
  getRequestDetail,
  getRequestUnits,
  putUpdateRequestStatus,
  putUpdateRequestInCharge,
  getRequestFiles,
  getRequestDownloadFile,
  getRequestStatus,
  getRequestStatusListing,
  getPortalOfferRequestReasons,
  getPortalOfferRequestUnits,
  postPrintExcelDoc,
};
