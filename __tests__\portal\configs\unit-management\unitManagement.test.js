import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { getUnitManagement } from "@/services/unitManagement";
import { useRouter } from "next/navigation";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import UnitManagement from "@/app/[lang]/portal/configs/unit-management/unitManagement";

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/unitManagement", () => ({
  getUnitManagement: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

describe("UnitManagement", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        getUnitManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 2,
        });
        mockRouter = { push: jest.fn() };
        useRouter.mockReturnValue(mockRouter);

        await act(async () => {
          render(<UnitManagement dict={dict} lang={lang} />);
        });
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("form:Text alanlar doğru olmalı.", () => {
        const texts = [
          dict.configs.unitManagement.unitName,
          dict.configs.unitManagement.unitMail,
          dict.configs.unitManagement.unitTel,
          dict.configs.unitManagement.isActive,
          dict.public.action,
          dict.public.add,
          dict.public.clean,
          dict.public.filter,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("form:Bileşenler filtre bloğunda bulunmalı.", async () => {
        expect(screen.getByTestId("unitName")).toBeInTheDocument();
        expect(screen.getByTestId("unitMail")).toBeInTheDocument();
        expect(screen.getByTestId("addButton")).toBeInTheDocument();
        expect(screen.getByTestId("resetButton")).toBeInTheDocument();
        expect(screen.getByTestId("filterButton")).toBeInTheDocument();
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region API kontrolü
      test("api:İlk yükleme sırasında API fonksiyonları çağırılmalı.", async () => {
        await waitFor(() => {
          expect(getUnitManagement).toHaveBeenCalled();
        });
      });
      // #endregion API kontrolü

      // #region Form işlevselliği kontrolü
      test("filter:Filtre formu submit edildiğinde birimleri filtrelemeli.", async () => {
        getUnitManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 0,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.filter));
        });
        await waitFor(() => {
          expect(getUnitManagement).toHaveBeenCalledTimes(2);
        });
      });

      test("form:Filtre butonu form değerlerini doğru şekilde göndermeli.", async () => {
        const { getByTestId } = screen;
        const filterButton = getByTestId("filterButton");
        const unitNameInput = getByTestId("unitName").querySelector("input");

        await act(async () => {
          fireEvent.input(unitNameInput, { target: { value: "Test Unit" } });
        });

        await act(async () => {
          fireEvent.click(filterButton);
        });

        await waitFor(() => {
          expect(getUnitManagement).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
              unitName: "Test Unit",
            })
          );
        });
      });

      test("table:Tabloya veri yüklenmeli.", async () => {
        jest.clearAllMocks();
        const data = Array.from({ length: 10 }, (_, index) => ({
          id: index,
          unitName: "Unit" + index,
          unitMail: "unit" + index + "@test.com",
          unitTel: "1234567890",
          isActive: 1,
        }));
        getUnitManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: data,
          totalNumberOfRecords: 10,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.filter));
        });
        await waitFor(() => {
          expect(getUnitManagement).toHaveBeenCalledTimes(1);
        });
      });

      test("link:Yeni ekle butonu doğru sayfaya yönlendirmeli.", async () => {
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.add));
        });
        expect(mockRouter.push).toHaveBeenCalledWith(
          `/${lang}/portal/configs/unit-management/action/`
        );
      });

      test("form:Reset butonu form alanlarını temizlemeli.", async () => {
        const { getByTestId } = screen;
        const resetButton = getByTestId("resetButton");
        const unitNameInput = getByTestId("unitName").querySelector("input");
        const unitMailInput = getByTestId("unitMail").querySelector("input");

        // Form'a değerleri set et
        await act(async () => {
          fireEvent.change(unitNameInput, { target: { value: "Test Unit" } });
          fireEvent.change(unitMailInput, {
            target: { value: "<EMAIL>" },
          });
        });

        // Reset butonuna tıkla
        await act(async () => {
          fireEvent.click(resetButton);
        });

        // Form değerlerinin sıfırlandığını kontrol et
        await waitFor(() => {
          // Form değerlerini window.__form__ üzerinden kontrol et
          expect(window.__form__.getFieldValue("unitName")).toBe("");
          expect(window.__form__.getFieldValue("unitMail")).toBe("");

          // API çağrısının defaultModel ile yapıldığını kontrol et
          expect(getUnitManagement).toHaveBeenLastCalledWith(
            expect.any(String),
            expect.objectContaining({
              unitName: null,
              unitMail: null,
              pagination: {
                pageNumber: 1,
                pageSize: 10,
                orderBy: "Id",
                ascending: true,
              },
            })
          );
        });
      });
      // #endregion Form işlevselliği kontrolü
    });
  });
});
