"use client";

import { Tabs } from "antd";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";

export default function DataTabs({ dict, lang }) {
  let pathName = usePathname();
  const [pathNameState, setPathNameState] = useState();

  const productManagementUrl = `/${lang}/portal/configs/product-management/`;
  const agencyManagementUrl = `/${lang}/portal/configs/agency-management/`;
  const unitManagementUrl = `/${lang}/portal/configs/unit-management/`;

  const items = [
    {
      key: productManagementUrl,
      label: (
        <Link href={productManagementUrl}>
          {dict.configs.productManagement.title}
        </Link>
      ),
    },
    {
      key: agencyManagementUrl,
      label: (
        <Link href={agencyManagementUrl}>
          {dict.configs.agencyManagement.title}
        </Link>
      ),
    },
    {
      key: unitManagementUrl,
      label: (
        <Link href={unitManagementUrl}>
          {dict.configs.unitManagement.title}
        </Link>
      ),
    },
  ];

  useEffect(() => {
    pathName = pathName?.replace("action/", "");
    setPathNameState(pathName);
  }, [pathName]);

  return (
    <Tabs
      className="slf-tabs-card sticky top-16 z-10 shadow-sm"
      defaultActiveKey={pathName}
      activeKey={pathNameState}
      destroyInactiveTabPane
      items={items}
    />
  );
}
