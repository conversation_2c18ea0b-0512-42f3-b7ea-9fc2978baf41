"use client";

import { Modal } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import RequestReviewModal from "@/components/containers/requestReviewModal";

export default function ReviewModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
}) {
  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {dict.requestManagement.requestManagementListing.reviewRequest}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <RequestReviewModal dict={dict} lang={lang} selectedData={selectedData} />
    </Modal>
  );
}
