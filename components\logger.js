import pino from "pino";
import fs from "fs";

const serviceName = "digital-insurance-operational-portal";
const appEnv = process.env.APP_ENV || "local";
const logLevel = appEnv === "local" ? "debug" : "info";

// local ve prod stdout, diğerleri dosya
const isFileLogging = ["dev", "sta"].includes(appEnv);
const isLocal = appEnv === "local";
let logger;

if (isFileLogging) {
  //-- dev ve sta dosyaya yazar
  const logDir = "/srv/app/logs";
  const logFile = `${logDir}/${appEnv}.log`;
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  const logStream = fs.createWriteStream(logFile, {
    flags: "a",
    encoding: "utf8",
  });
  logger = pino(
    {
      level: logLevel,
      base: {
        env: appEnv,
        service: serviceName,
      },
      timestamp: pino.stdTimeFunctions.isoTime,
    },
    logStream
  );
} else if (isLocal) {
  //-- local ortamda renkli
  logger = pino({
    level: logLevel,
    transport: {
      target: "pino-pretty",
      options: {
        colorize: true,
        translateTime: "SYS:standard",
        ignore: "pid,hostname",
        singleLine: false,
      },
    },
    base: {
      env: appEnv,
      service: serviceName,
    },
    timestamp: pino.stdTimeFunctions.isoTime,
  });
} else {
  //-- prod → stdout (JSON)
  logger = pino({
    level: logLevel,
    base: {
      env: appEnv,
      service: serviceName,
    },
    timestamp: pino.stdTimeFunctions.isoTime,
  });
}

export default logger;
