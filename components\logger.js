import pino from "pino";

const serviceName = "digital-insurance-operational-portal";
const appEnv = process.env.APP_ENV || "local";
const logLevel = appEnv === "local" ? "debug" : "info";
const isLocal = appEnv === "local";
let logger;

if (isLocal) {
  //-- local ortamda pino-pretty ile renkli gör<PERSON>nüm
  logger = pino({
    level: logLevel,
    transport: {
      target: "pino-pretty",
      options: {
        colorize: true,
        translateTime: "SYS:standard",
        ignore: "pid,hostname",
        singleLine: false,
      },
    },
    base: {
      env: appEnv,
      service: serviceName,
    },
    timestamp: pino.stdTimeFunctions.isoTime,
  });
} else {
  //-- dev,sta,prod → stdout (JSON)
  logger = pino({
    level: logLevel,
    base: {
      env: appEnv,
      service: serviceName,
    },
    timestamp: pino.stdTimeFunctions.isoTime,
  });
}

export default logger;
