"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const postPrintDocument = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/insurance/print-document`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const postGetQueryPolicyDetail = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/insurance/query-policy-detail`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

export { postPrintDocument, postGetQueryPolicyDetail };
