"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const postPrintDocument = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/insurance/print-document`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postPrintDocument", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postGetQueryPolicyDetail = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/insurance/query-policy-detail`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "postGetQueryPolicyDetail",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

export { postPrintDocument, postGetQueryPolicyDetail };
