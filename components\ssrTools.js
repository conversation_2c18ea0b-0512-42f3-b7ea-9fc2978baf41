"use server";

import { cookies } from "next/headers";
import logger from "./logger";

const getToken = () => {
  try {
    const cookieStore = typeof cookies === "function" ? cookies() : undefined;
    if (!cookieStore || typeof cookieStore.get !== "function") return undefined;
    const token = cookieStore.get("token");
    return token?.value;
  } catch {
    return undefined;
  }
};

const getAcceptLanguage = (lang) => {
  return `${lang}-${lang.toUpperCase()}`;
};

const getTokenValue = () => {
  const token = getToken();
  if (!token) {
    return;
  }
  const base64Url = token.split(".")[1];
  const base64 = base64Url?.replace("-", "+").replace("_", "/");
  const decode = base64
    ? Buffer.from(base64, "base64").toString("utf-8")
    : null;
  //-- encode etmek için not olsun => Buffer.from(value, "utf-8").toString("base64");
  return JSON.parse(decode);
};

// #region response kodları
const getResponse = (status) => {
  return {
    status: status,
    code: "",
    message: "",
    validationMessages: [],
    errorMessages: [],
    data: [],
  };
};

const validResponse = async (res) => {
  let response = null;
  if (res?.ok) {
    try {
      response = await res?.json();
    } catch (err) {
      response = getResponse("SUCCESS");
      logError("validResponse", "status200ButInvalidJson", undefined, res, err);
    }
    return JSON.stringify(response);
  } else {
    if (res?.status == 401) {
      const cookieStore = cookies();
      cookieStore.set("token", "");
      response = getResponse("401");
      return JSON.stringify(response);
    }
    try {
      response = await res?.json();
    } catch (err) {
      response = getResponse("ERROR");
      logError(
        "validResponse",
        "statusNot200AndInvalidJson",
        undefined,
        res,
        err
      );
    }
    return JSON.stringify(response);
  }
};

const isSiteVerified = async (recaptchaToken) => {
  if (process.env.APP_ENV !== "prod") {
    const token = getToken();
    if (token) {
      const payloadBase64Url = token.split(".")[1];
      if (payloadBase64Url) {
        const payloadBase64 = payloadBase64Url
          .replace(/-/g, "+")
          .replace(/_/g, "/");
        const decodedPayload = atob(payloadBase64);
        const payloadObject = JSON.parse(decodedPayload);
        if (payloadObject.email === "<EMAIL>") {
          return true;
        }
      }
    }
  }

  let fResponse;
  try {
    fResponse = await fetch("https://www.google.com/recaptcha/api/siteverify", {
      method: "POST",
      body: new URLSearchParams({
        secret: process.env.GOOGLE_RECAPTCHAV3_SECRET,
        response: recaptchaToken,
      }),
    });
    const recaptchaVerifyResponse = await fResponse.json();
    const recaptchaThreshold = 0.5;
    const result =
      recaptchaVerifyResponse.success &&
      recaptchaVerifyResponse.score >= recaptchaThreshold;
    if (!result) {
      logError(
        "isSiteVerified",
        "recaptchaFailed",
        { recaptchaToken },
        fResponse,
        recaptchaVerifyResponse
      );
    }
    return result;
  } catch (err) {
    logError(
      "isSiteVerified",
      "recaptchaFetchError",
      { recaptchaToken },
      fResponse,
      err
    );
    return false;
  }
};

const encode = (text) => {
  try {
    return btoa(encodeURIComponent(text));
  } catch (error) {
    return "";
  }
};

const logError = (method, messageTemplate, request, fResponse, err) => {
  logger.error({
    method,
    messageTemplate,
    request,
    response: {
      status: fResponse?.status,
      statusText: fResponse?.statusText,
      headers: fResponse?.headers
        ? Object.fromEntries(fResponse.headers.entries())
        : {},
      url: fResponse?.url,
    },
    err,
  });
};
// #endregion response kodları

export {
  getToken,
  getAcceptLanguage,
  getTokenValue,
  validResponse,
  isSiteVerified,
  encode,
  logError,
};
