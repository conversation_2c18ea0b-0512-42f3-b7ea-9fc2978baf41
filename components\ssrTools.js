"use server";

import { cookies } from "next/headers";

const getToken = () => {
  const cookieStore = cookies();
  const token = cookieStore.get("token");
  return token?.value;
};

const getAcceptLanguage = (lang) => {
  return `${lang}-${lang.toUpperCase()}`;
};

const getTokenValue = () => {
  const token = getToken();
  if (!token) {
    return;
  }
  const base64Url = token.split(".")[1];
  const base64 = base64Url?.replace("-", "+").replace("_", "/");
  const decode = base64
    ? Buffer.from(base64, "base64").toString("utf-8")
    : null;
  //-- encode etmek için not olsun => Buffer.from(value, "utf-8").toString("base64");
  return JSON.parse(decode);
};

// #region response kodları
const getResponse = (status) => {
  return {
    status: status,
    code: "",
    message: "",
    validationMessages: [],
    errorMessages: [],
    data: [],
  };
};

const validResponse = async (res) => {
  let response = null;
  if (res?.ok) {
    try {
      response = await res?.json();
    } catch {
      response = getResponse("SUCCESS");
    }
    return JSON.stringify(response);
  } else {
    if (res?.status == 401) {
      const cookieStore = cookies();
      cookieStore.set("token", "");
      response = getResponse("401");
      return JSON.stringify(response);
    }
    try {
      response = await res?.json();
    } catch {
      response = getResponse("ERROR");
    }
    return JSON.stringify(response);
  }
};

const isSiteVerified = async (recaptchaToken) => {
  const tokenResponse = await fetch(
    "https://www.google.com/recaptcha/api/siteverify",
    {
      method: "POST",
      body: new URLSearchParams({
        secret: process.env.GOOGLE_RECAPTCHAV3_SECRET,
        response: recaptchaToken,
      }),
    }
  );
  const recaptchaVerifyResponse = await tokenResponse.json();
  const recaptchaThreshold = 0.5;
  return (
    recaptchaVerifyResponse.success &&
    recaptchaVerifyResponse.score >= recaptchaThreshold
  );
};

const encode = (text) => {
  try {
    return btoa(encodeURIComponent(text));
  } catch (error) {
    return "";
  }
};
// #endregion response kodları

export {
  getToken,
  getAcceptLanguage,
  getTokenValue,
  validResponse,
  isSiteVerified,
  encode,
};
