export function IconArrowRight({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowRight"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
}

export function IconChevronRight({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconChevronRight"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
}

export function IconMenu({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconMenu"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
      />
    </svg>
  );
}

export function IconUser({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconUser"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
      />
    </svg>
  );
}

export function IconChartPie({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconChartPie"
      aria-hidden="true"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
      <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
    </svg>
  );
}

export function IconSettings({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconSettings"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );
}

export function IconArrowDown({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowDown"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
}

export function IconPlus({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconPlus"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 4.5v15m7.5-7.5h-15"
      />
    </svg>
  );
}

export function IconPencil({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconPencil"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
      />
    </svg>
  );
}

export function IconTrash({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconTrash"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
      />
    </svg>
  );
}

export function IconCpuChip({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconCpuChip"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H6.75A2.25 2.25 0 004.5 6.75v10.5a2.25 2.25 0 002.25 2.25zm.75-12h9v9h-9v-9z"
      />
    </svg>
  );
}

export function IconBuildingOffice({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconBuildingOffice"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
      />
    </svg>
  );
}

export function IconUserPlus({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconUserPlus"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z"
      />
    </svg>
  );
}

export function IconInformationCircle({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconInformationCircle"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
      />
    </svg>
  );
}

export function IconEnvelope({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconEnvelope"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"
      />
    </svg>
  );
}

export function IconCake({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconCake"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 8.25v-1.5m0 1.5c-1.355 0-2.697.056-4.024.166C6.845 8.51 6 9.473 6 10.608v2.513m6-4.87c1.355 0 2.697.055 4.024.165C17.155 8.51 18 9.473 18 10.608v2.513m-3-4.87v-1.5m-6 1.5v-1.5m12 9.75l-1.5.75a3.354 3.354 0 01-3 0 3.354 3.354 0 00-3 0 3.354 3.354 0 01-3 0 3.354 3.354 0 00-3 0 3.354 3.354 0 01-3 0L3 16.5m15-3.38a48.474 48.474 0 00-6-.37c-2.032 0-4.034.125-6 .37m12 0c.39.049.777.102 1.163.16 1.07.16 1.837 1.094 1.837 2.175v5.17c0 .62-.504 1.124-1.125 1.124H4.125A1.125 1.125 0 013 20.625v-5.17c0-1.08.768-2.014 1.837-2.174A47.78 47.78 0 016 13.12M12.265 3.11a.375.375 0 11-.53 0L12 2.845l.265.265zm-3 0a.375.375 0 11-.53 0L9 2.845l.265.265zm6 0a.375.375 0 11-.53 0L15 2.845l.265.265z"
      />
    </svg>
  );
}

export function IconArrowSmallLeft({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowSmallLeft"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 12h-15m0 0l6.75 6.75M4.5 12l6.75-6.75"
      />
    </svg>
  );
}

export function IconArrowSmallRight({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowSmallRight"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75"
      />
    </svg>
  );
}

export function IconCheck({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconCheck"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.5 12.75l6 6 9-13.5"
      />
    </svg>
  );
}

export function IconLockClosed({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconLockClosed"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
      />
    </svg>
  );
}

export function IconBanknotes({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconBanknotes"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
      />
    </svg>
  );
}

export function IconArrowPathRoundedSquare({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowPathRoundedSquare"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3"
      />
    </svg>
  );
}

export function IconPhone({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconPhone"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z"
      />
    </svg>
  );
}

export function IconPaperAirplane({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconPaperAirplane"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5"
      />
    </svg>
  );
}

export function IconArrowDownTray({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowDownTray"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"
      />
    </svg>
  );
}

export function IconArrowTopRightOnSquare({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowTopRightOnSquare"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
      />
    </svg>
  );
}

export function IconDocumentText({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconDocumentText"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
      />
    </svg>
  );
}

export function IconDocumentPlus({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconDocumentPlus"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
      />
    </svg>
  );
}

export function IconFolder({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconFolder"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
      />
    </svg>
  );
}

export function IconClipboardDocumentList({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconClipboardDocumentList"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
      />
    </svg>
  );
}

export function IconArrowUpTray({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconArrowUpTray"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
      />
    </svg>
  );
}

export function IconDocument({ className }) {
  return (
    <svg
      className={className}
      data-testid="IconDocument"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
      />
    </svg>
  );
}
export function IconWindow({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconWindow"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3 8.25V18a2.25 2.25 0 002.25 2.25h13.5A2.25 2.25 0 0021 18V8.25m-18 0V6a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 6v2.25m-18 0h18M5.25 6h.008v.008H5.25V6zM7.5 6h.008v.008H7.5V6zm2.25 0h.008v.008H9.75V6z"
      />
    </svg>
  );
}
export function IconUserGroup({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconUserGroup"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
      />
    </svg>
  );
}
export function IconCalendar({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconCalendar"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
      />
    </svg>
  );
}
export function IconClock({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconClock"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  );
}
export function IconPencilSquare({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconPencilSquare"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
      />
    </svg>
  );
}
export function IconArrowLeftOnRectangle({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconArrowLeftOnRectangle"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75"
      />
    </svg>
  );
}
export function IconFlagTR({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      data-testid="IconFlagTR"
    >
      <g fillRule="evenodd">
        <path fill="#e30a17" d="M0 0h512v512H0z" />
        <path
          fill="#fff"
          d="M348.8 264c0 70.6-58.3 127.9-130.1 127.9s-130.1-57.3-130.1-128 58.2-127.8 130-127.8S348.9 193.3 348.9 264z"
        />
        <path
          fill="#e30a17"
          d="M355.3 264c0 56.5-46.6 102.3-104.1 102.3s-104-45.8-104-102.3 46.5-102.3 104-102.3 104 45.8 104 102.3z"
        />
        <path
          fill="#fff"
          d="m374.1 204.2-1 47.3-44.2 12 43.5 15.5-1 43.3 28.3-33.8 42.9 14.8-24.8-36.3 30.2-36.1-46.4 12.8-27.5-39.5z"
        />
      </g>
    </svg>
  );
}
export function IconFlagEN({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      data-testid="IconFlagEN"
    >
      <path fill="#bd3d44" d="M0 0h512v512H0" />
      <path
        stroke="#fff"
        strokeWidth="40"
        d="M0 58h512M0 137h512M0 216h512M0 295h512M0 374h512M0 453h512"
      />
      <path fill="#192f5d" d="M0 0h390v275H0z" />
      <marker id="us-a" markerHeight="30" markerWidth="30">
        <path fill="#fff" d="m15 0 9.3 28.6L0 11h30L5.7 28.6" />
      </marker>
      <path
        fill="none"
        markerMid="url(#us-a)"
        d="m0 0 18 11h65 65 65 65 66L51 39h65 65 65 65L18 66h65 65 65 65 66L51 94h65 65 65 65L18 121h65 65 65 65 66L51 149h65 65 65 65L18 177h65 65 65 65 66L51 205h65 65 65 65L18 232h65 65 65 65 66L0 0"
      />
    </svg>
  );
}

export function IconImage({ className }) {
  return (
    <svg
      className={className}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      data-testid="IconImage"
    >
      <path
        d="M9.33337 12.2499C10.2999 12.2499 11.0834 11.4664 11.0834 10.4999C11.0834 9.53342 10.2999 8.74992 9.33337 8.74992C8.36688 8.74992 7.58337 9.53342 7.58337 10.4999C7.58337 11.4664 8.36688 12.2499 9.33337 12.2499Z"
        fill="#5C59E8"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.70837 3.20825C4.77538 3.20825 3.20837 4.77526 3.20837 6.70825V21.2916C3.20837 23.2246 4.77538 24.7916 6.70837 24.7916H21.2917C23.2247 24.7916 24.7917 23.2246 24.7917 21.2916V6.70825C24.7917 4.77525 23.2247 3.20825 21.2917 3.20825H6.70837ZM21.2917 5.54159H6.70837C6.06404 5.54159 5.54171 6.06392 5.54171 6.70825V15.7711L8.45694 13.9771C8.65095 13.8577 8.8967 13.8622 9.08624 13.9885L12.1976 16.0628L17.1419 12.2171C17.3526 12.0533 17.6476 12.0533 17.8582 12.2171L22.4584 15.795V6.70825C22.4584 6.06392 21.936 5.54159 21.2917 5.54159ZM5.54171 21.2916V18.5108L8.72489 16.552L12.3026 18.9371L17.5001 14.8946L22.4584 18.751V21.2916C22.4584 21.9359 21.936 22.4583 21.2917 22.4583H6.70837C6.06404 22.4583 5.54171 21.9359 5.54171 21.2916Z"
        fill="#5C59E8"
      />
    </svg>
  );
}

export function IconGreenCheck({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 85 85"
      fill="none"
      data-testid="IconGreenCheck"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M41.5151 45.0828L76.3281 3.48267C80.0666 -0.578168 83.6971 2.84364 80.9305 7.60437L47.7495 59.3128C45.2846 63.1538 39.8911 63.0712 36.9469 59.6395L21.3152 41.4176C15.6851 34.8546 25.7754 26.1964 31.3577 32.8947L41.5151 45.0828ZM42.5 7.63418C49.805 7.63418 56.5841 9.88226 62.1857 13.7226L67.1035 7.84594C60.1605 2.90636 51.6706 0 42.5 0C19.028 0 0 19.028 0 42.5C0 65.972 19.028 85 42.5 85C65.972 85 85 65.972 85 42.5C85 33.7921 82.3799 25.6959 77.8856 18.9566L73.2857 26.1244C75.889 31.0068 77.3652 36.5805 77.3652 42.5C77.3652 61.7559 61.7553 77.3658 42.4994 77.3658C23.2435 77.3658 7.63418 61.7559 7.63418 42.5C7.63418 23.2441 23.2441 7.63418 42.5 7.63418Z"
        fill="#6DBA3A"
      />
    </svg>
  );
}

export function IconCheckBadge({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconCheckBadge"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z"
      />
    </svg>
  );
}

export function IconCalendarCheck({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconCalendarCheck"
    >
      <path d="M15.9666 12.6975C16.3519 12.3017 16.3433 11.6686 15.9475 11.2834C15.5517 10.8982 14.9186 10.9068 14.5334 11.3025L11.2737 14.6517L9.8755 13.5309C9.44459 13.1854 8.81522 13.2547 8.46979 13.6856C8.12435 14.1166 8.19365 14.7459 8.62457 15.0914L11.0857 17.0643C11.2867 17.2254 11.5771 17.2075 11.7567 17.0229L15.9666 12.6975Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 2C8.55228 2 9 2.44772 9 3H15C15 2.44772 15.4477 2 16 2C16.5523 2 17 2.44772 17 3H18C19.6569 3 21 4.34315 21 6V19C21 20.6569 19.6569 22 18 22H6C4.34315 22 3 20.6569 3 19V6C3 4.34315 4.34315 3 6 3H7C7 2.44772 7.44772 2 8 2ZM19 6V7H5V6C5 5.44772 5.44772 5 6 5H7C7 5.55228 7.44772 6 8 6C8.55228 6 9 5.55228 9 5H15C15 5.55228 15.4477 6 16 6C16.5523 6 17 5.55228 17 5H18C18.5523 5 19 5.44772 19 6ZM5 19V9H19V19C19 19.5523 18.5523 20 18 20H6C5.44772 20 5 19.5523 5 19Z"
      />
    </svg>
  );
}

export function IconUsers({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconUsers"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.5 6.5C14.5 8.98528 12.4853 11 10 11C7.51472 11 5.5 8.98528 5.5 6.5C5.5 4.01472 7.51472 2 10 2C12.4853 2 14.5 4.01472 14.5 6.5ZM12.5 6.5C12.5 7.88071 11.3807 9 10 9C8.61929 9 7.5 7.88071 7.5 6.5C7.5 5.11929 8.61929 4 10 4C11.3807 4 12.5 5.11929 12.5 6.5Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2 18.9231C2 15.0996 5.09957 12 8.92308 12H11.0769C14.9004 12 18 15.0996 18 18.9231C18 20.0701 17.0701 21 15.9231 21H4.07692C2.92987 21 2 20.0701 2 18.9231ZM4 18.9231C4 16.2041 6.20414 14 8.92308 14H11.0769C13.7959 14 16 16.2041 16 18.9231C16 18.9656 15.9656 19 15.9231 19H4.07692C4.03444 19 4 18.9656 4 18.9231Z"
      />
      <path d="M18.9198 20.0973C18.8164 20.4981 19.0774 21 19.4913 21H19.9231C21.0701 21 22 20.0701 22 18.9231C22 15.0996 18.9004 12 15.0769 12C14.9347 12 14.8829 12.1975 15.0036 12.2727C15.9494 12.8614 16.7705 13.6314 17.4182 14.5343C17.4621 14.5955 17.5187 14.6466 17.5835 14.685C19.0301 15.5424 20 17.1195 20 18.9231C20 18.9656 19.9656 19 19.9231 19H19.4494C19.1985 19 19 19.2106 19 19.4615C19 19.6811 18.9721 19.8941 18.9198 20.0973Z" />
      <path d="M14.919 8.96308C14.974 8.85341 15.0645 8.76601 15.1729 8.70836C15.9624 8.28814 16.5 7.45685 16.5 6.5C16.5 5.54315 15.9624 4.71186 15.1729 4.29164C15.0645 4.23399 14.974 4.14659 14.919 4.03692C14.6396 3.48001 14.2684 2.97712 13.8252 2.5481C13.623 2.35231 13.7185 2 14 2C16.4853 2 18.5 4.01472 18.5 6.5C18.5 8.98528 16.4853 11 14 11C13.7185 11 13.623 10.6477 13.8252 10.4519C14.2684 10.0229 14.6396 9.51999 14.919 8.96308Z" />
    </svg>
  );
}

export function IconQuestionMarkCircle({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconQuestionMarkCircle"
    >
      <path d="M11.7857 8C10.7995 8 10 8.79948 10 9.78569V10.2273C10 10.7796 9.55228 11.2273 9 11.2273C8.44772 11.2273 8 10.7796 8 10.2273V9.78569C8 7.69491 9.69491 6 11.7857 6H11.9339C13.6089 6 15.0625 7.15543 15.4403 8.78721C15.8594 10.5966 14.8328 12.4278 13.0705 13.0143L12.9679 13.0484C12.8212 13.0973 12.7222 13.2345 12.7222 13.3891V14C12.7222 14.5523 12.2745 15 11.7222 15C11.1699 15 10.7222 14.5523 10.7222 14V13.3891C10.7222 12.3733 11.3725 11.4716 12.3363 11.1508L12.4389 11.1166C13.222 10.856 13.6781 10.0424 13.4919 9.23843C13.324 8.51339 12.6781 8 11.9339 8H11.7857Z" />
      <path d="M11.75 16C12.3023 16 12.75 16.4477 12.75 17C12.75 17.5523 12.3023 18 11.75 18C11.1977 18 10.75 17.5523 10.75 17C10.75 16.4477 11.1977 16 11.75 16Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z"
      />
    </svg>
  );
}

export function IconUserX({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconUserX"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.5 6.5C16.5 8.98528 14.4853 11 12 11C9.51472 11 7.5 8.98528 7.5 6.5C7.5 4.01472 9.51472 2 12 2C14.4853 2 16.5 4.01472 16.5 6.5ZM14.5 6.5C14.5 7.88071 13.3807 9 12 9C10.6193 9 9.5 7.88071 9.5 6.5C9.5 5.11929 10.6193 4 12 4C13.3807 4 14.5 5.11929 14.5 6.5Z"
      />
      <path d="M15.6687 14.263C15.1694 14.0925 14.634 14 14.0769 14H9.92308C7.20414 14 5 16.2041 5 18.9231C5 18.9656 5.03444 19 5.07692 19H13C13.5523 19 14 19.4477 14 20C14 20.5523 13.5523 21 13 21H5.07692C3.92987 21 3 20.0701 3 18.9231C3 15.0996 6.09957 12 9.92308 12H14.0769C15.0066 12 15.8935 12.1832 16.7034 12.5156C17.1867 12.7139 17.355 13.2899 17.1214 13.7572C16.8626 14.2748 16.2163 14.45 15.6687 14.263Z" />
      <path d="M16.0251 16.0251C16.4157 15.6346 17.0488 15.6346 17.4393 16.0251L18.5 17.0858L19.5607 16.0251C19.9512 15.6346 20.5844 15.6346 20.9749 16.0251C21.3654 16.4157 21.3654 17.0488 20.9749 17.4393L19.9142 18.5L20.9749 19.5607C21.3654 19.9512 21.3654 20.5843 20.9749 20.9749C20.5844 21.3654 19.9512 21.3654 19.5607 20.9749L18.5 19.9142L17.4393 20.9749C17.0488 21.3654 16.4157 21.3654 16.0251 20.9749C15.6346 20.5843 15.6346 19.9512 16.0251 19.5607L17.0858 18.5L16.0251 17.4393C15.6346 17.0488 15.6346 16.4157 16.0251 16.0251Z" />
    </svg>
  );
}

export function IconCalendarPlus({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconCalendarPlus"
    >
      <path d="M12 11C12.5523 11 13 11.4477 13 12V13.5H14.5C15.0523 13.5 15.5 13.9477 15.5 14.5C15.5 15.0523 15.0523 15.5 14.5 15.5H13V17C13 17.5523 12.5523 18 12 18C11.4477 18 11 17.5523 11 17V15.5H9.5C8.94772 15.5 8.5 15.0523 8.5 14.5C8.5 13.9477 8.94772 13.5 9.5 13.5H11V12C11 11.4477 11.4477 11 12 11Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 3C9 2.44772 8.55228 2 8 2C7.44772 2 7 2.44772 7 3H6C4.34315 3 3 4.34315 3 6V19C3 20.6569 4.34315 22 6 22H18C19.6569 22 21 20.6569 21 19V6C21 4.34315 19.6569 3 18 3H17C17 2.44772 16.5523 2 16 2C15.4477 2 15 2.44772 15 3H9ZM19 7V6C19 5.44772 18.5523 5 18 5H17C17 5.55228 16.5523 6 16 6C15.4477 6 15 5.55228 15 5H9C9 5.55228 8.55228 6 8 6C7.44772 6 7 5.55228 7 5H6C5.44772 5 5 5.44772 5 6V7H19ZM5 9V19C5 19.5523 5.44772 20 6 20H18C18.5523 20 19 19.5523 19 19V9H5Z"
      />
    </svg>
  );
}

export function IconCalendarX({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconCalendarX"
    >
      <path d="M9.52517 12.0251C9.91569 11.6346 10.5489 11.6346 10.9394 12.0251L12 13.0858L13.0607 12.0251C13.4512 11.6346 14.0844 11.6346 14.4749 12.0251C14.8654 12.4157 14.8654 13.0488 14.4749 13.4393L13.4143 14.5L14.4749 15.5607C14.8654 15.9512 14.8654 16.5843 14.4749 16.9749C14.0844 17.3654 13.4512 17.3654 13.0607 16.9749L12 15.9142L10.9394 16.9749C10.5489 17.3654 9.91569 17.3654 9.52517 16.9749C9.13464 16.5843 9.13464 15.9512 9.52517 15.5607L10.5858 14.5L9.52517 13.4393C9.13464 13.0488 9.13464 12.4157 9.52517 12.0251Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 3C9 2.44772 8.55228 2 8 2C7.44772 2 7 2.44772 7 3H6C4.34315 3 3 4.34315 3 6V19C3 20.6569 4.34315 22 6 22H18C19.6569 22 21 20.6569 21 19V6C21 4.34315 19.6569 3 18 3H17C17 2.44772 16.5523 2 16 2C15.4477 2 15 2.44772 15 3H9ZM19 7V6C19 5.44772 18.5523 5 18 5H17C17 5.55228 16.5523 6 16 6C15.4477 6 15 5.55228 15 5H9C9 5.55228 8.55228 6 8 6C7.44772 6 7 5.55228 7 5H6C5.44772 5 5 5.44772 5 6V7H19ZM5 9V19C5 19.5523 5.44772 20 6 20H18C18.5523 20 19 19.5523 19 19V9H5Z"
      />
    </svg>
  );
}

export function IconFileCheck({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconFileCheck"
    >
      <path d="M15.9666 11.4475C16.3518 11.0517 16.3433 10.4186 15.9475 10.0334C15.5517 9.64819 14.9186 9.65675 14.5334 10.0525L11.2737 13.4017L9.87547 12.2809C9.44455 11.9354 8.81519 12.0047 8.46976 12.4356C8.12432 12.8666 8.19362 13.4959 8.62454 13.8414L11.0857 15.8143C11.2866 15.9754 11.5771 15.9575 11.7567 15.7729L15.9666 11.4475Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 2C5.34315 2 4 3.34315 4 5V19C4 20.6569 5.34315 22 7 22H17C18.6569 22 20 20.6569 20 19V7.24264C20 6.44699 19.6839 5.68393 19.1213 5.12132L16.8787 2.87868C16.3161 2.31607 15.553 2 14.7574 2H7ZM7 4H14V6.9C14 7.50751 14.4925 8 15.1 8H18V19C18 19.5523 17.5523 20 17 20H7C6.44772 20 6 19.5523 6 19V5C6 4.44772 6.44771 4 7 4ZM16 4.82843L17.1716 6H16V4.82843Z"
      />
    </svg>
  );
}

export function IconFileBlank({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconFileBlank"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 5C4 3.34315 5.34315 2 7 2H14.7574C15.553 2 16.3161 2.31607 16.8787 2.87868L19.1213 5.12132C19.6839 5.68393 20 6.44699 20 7.24264V19C20 20.6569 18.6569 22 17 22H7C5.34315 22 4 20.6569 4 19V5ZM14 4H7C6.44771 4 6 4.44772 6 5V19C6 19.5523 6.44772 20 7 20H17C17.5523 20 18 19.5523 18 19V8H15.1C14.4925 8 14 7.50751 14 6.9V4ZM17.1716 6L16 4.82843V6H17.1716Z"
      />
    </svg>
  );
}

export function IconFileX({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconFileX"
    >
      <path d="M9.52514 10.5251C9.91566 10.1346 10.5488 10.1346 10.9393 10.5251L12 11.5858L13.0607 10.5251C13.4512 10.1346 14.0844 10.1346 14.4749 10.5251C14.8654 10.9157 14.8654 11.5488 14.4749 11.9393L13.4142 13L14.4749 14.0607C14.8654 14.4512 14.8654 15.0843 14.4749 15.4749C14.0844 15.8654 13.4512 15.8654 13.0607 15.4749L12 14.4142L10.9393 15.4749C10.5488 15.8654 9.91566 15.8654 9.52514 15.4749C9.13461 15.0843 9.13461 14.4512 9.52514 14.0607L10.5858 13L9.52514 11.9393C9.13461 11.5488 9.13461 10.9157 9.52514 10.5251Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 5C4 3.34315 5.34315 2 7 2H14.7574C15.553 2 16.3161 2.31607 16.8787 2.87868L19.1213 5.12132C19.6839 5.68393 20 6.44699 20 7.24264V19C20 20.6569 18.6569 22 17 22H7C5.34315 22 4 20.6569 4 19V5ZM14 4H7C6.44771 4 6 4.44772 6 5V19C6 19.5523 6.44772 20 7 20H17C17.5523 20 18 19.5523 18 19V8H15.1C14.4925 8 14 7.50751 14 6.9V4ZM17.1716 6L16 4.82843V6H17.1716Z"
      />
    </svg>
  );
}

export function IconPin({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconPin"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 10C16 12.2091 14.2091 14 12 14C9.79086 14 8 12.2091 8 10C8 7.79086 9.79086 6 12 6C14.2091 6 16 7.79086 16 10ZM14 10C14 11.1046 13.1046 12 12 12C10.8954 12 10 11.1046 10 10C10 8.89543 10.8954 8 12 8C13.1046 8 14 8.89543 14 10Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 9.99998C19.9999 16.8707 14.0992 20.791 12.4332 21.7609C12.1612 21.9192 11.8388 21.9192 11.5668 21.7609C9.90082 20.791 4 16.8707 4 9.99998C4 5.99998 7 2 12 2C17 2 20 5.99998 20 9.99998ZM6 9.99998C6 6.93718 8.26481 4 12 4C15.7352 4 18 6.9372 18 9.99995C18 13.2825 16.3677 15.8038 14.5857 17.5858C13.7002 18.4714 12.8093 19.1401 12.1406 19.5859C12.0924 19.618 12.0455 19.6489 12 19.6785C11.9544 19.6489 11.9075 19.618 11.8594 19.5859C11.1907 19.1401 10.2998 18.4714 9.41422 17.5858C7.63221 15.8038 6 13.2825 6 9.99998Z"
      />
    </svg>
  );
}

export function IconCalendarSlot({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconCalendarSlot"
    >
      <path d="M6 11C6 10.4477 6.44772 10 7 10H8C8.55228 10 9 10.4477 9 11C9 11.5523 8.55228 12 8 12H7C6.44772 12 6 11.5523 6 11Z" />
      <path d="M10.5 11C10.5 10.4477 10.9477 10 11.5 10H12.5C13.0523 10 13.5 10.4477 13.5 11C13.5 11.5523 13.0523 12 12.5 12H11.5C10.9477 12 10.5 11.5523 10.5 11Z" />
      <path d="M16 10C15.4477 10 15 10.4477 15 11C15 11.5523 15.4477 12 16 12H17C17.5523 12 18 11.5523 18 11C18 10.4477 17.5523 10 17 10H16Z" />
      <path d="M6 14.5C6 13.9477 6.44772 13.5 7 13.5H8C8.55228 13.5 9 13.9477 9 14.5C9 15.0523 8.55228 15.5 8 15.5H7C6.44772 15.5 6 15.0523 6 14.5Z" />
      <path d="M11.5 13.5C10.9477 13.5 10.5 13.9477 10.5 14.5C10.5 15.0523 10.9477 15.5 11.5 15.5H12.5C13.0523 15.5 13.5 15.0523 13.5 14.5C13.5 13.9477 13.0523 13.5 12.5 13.5H11.5Z" />
      <path d="M15 14.5C15 13.9477 15.4477 13.5 16 13.5H17C17.5523 13.5 18 13.9477 18 14.5C18 15.0523 17.5523 15.5 17 15.5H16C15.4477 15.5 15 15.0523 15 14.5Z" />
      <path d="M7 17C6.44772 17 6 17.4477 6 18C6 18.5523 6.44772 19 7 19H8C8.55228 19 9 18.5523 9 18C9 17.4477 8.55228 17 8 17H7Z" />
      <path d="M10.5 18C10.5 17.4477 10.9477 17 11.5 17H12.5C13.0523 17 13.5 17.4477 13.5 18C13.5 18.5523 13.0523 19 12.5 19H11.5C10.9477 19 10.5 18.5523 10.5 18Z" />
      <path d="M16 17C15.4477 17 15 17.4477 15 18C15 18.5523 15.4477 19 16 19H17C17.5523 19 18 18.5523 18 18C18 17.4477 17.5523 17 17 17H16Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 3C9 2.44772 8.55228 2 8 2C7.44772 2 7 2.44772 7 3H6C4.34315 3 3 4.34315 3 6V19C3 20.6569 4.34315 22 6 22H18C19.6569 22 21 20.6569 21 19V6C21 4.34315 19.6569 3 18 3H17C17 2.44772 16.5523 2 16 2C15.4477 2 15 2.44772 15 3H9ZM19 7V6C19 5.44772 18.5523 5 18 5H17C17 5.55228 16.5523 6 16 6C15.4477 6 15 5.55228 15 5H9C9 5.55228 8.55228 6 8 6C7.44772 6 7 5.55228 7 5H6C5.44772 5 5 5.44772 5 6V7H19ZM5 9V19C5 19.5523 5.44772 20 6 20H18C18.5523 20 19 19.5523 19 19V9H5Z"
      />
    </svg>
  );
}

export function IconClockSlot({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="IconClockSlot"
    >
      <path d="M12 6C12.5523 6 13 6.44772 13 7V11H15C15.5523 11 16 11.4477 16 12C16 12.5523 15.5523 13 15 13H12C11.4477 13 11 12.5523 11 12V7C11 6.44772 11.4477 6 12 6Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z"
      />
    </svg>
  );
}

export function IconMapPin({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconMapPin"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"
      />
    </svg>
  );
}

export function IconSwatch({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconSwatch"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"
      />
    </svg>
  );
}

export function IconPresentationChartLine({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconPresentationChartLine"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"
      />
    </svg>
  );
}

export function IconCog6Tooth({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconCog6Tooth"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
      />
    </svg>
  );
}

export function IconVisa({ className }) {
  return (
    <svg
      className={className}
      width="75px"
      height="45px"
      viewBox="0 0 200 120"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      data-testid="IconVisa"
    >
      <title>visa</title>
      <g
        id="Rounded"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="Large" transform="translate(-32.000000, -28.000000)">
          <g id="large/visa" transform="translate(32.000000, 28.000000)">
            <rect
              id="Background"
              fill="#25459A"
              x="0"
              y="0"
              width="200"
              height="120"
              rx="8"
            ></rect>
            <g
              id="Logo"
              transform="translate(32.000000, 40.000000)"
              fill="#FFFFFF"
              fillRule="nonzero"
            >
              <path
                d="M68.2672516,27.2122001 C68.1919404,21.6403198 73.5588253,18.5307792 77.601826,16.6821648 C81.7558108,14.7851962 83.151042,13.5689046 83.1351871,11.8727915 C83.1034772,9.27654823 79.8215122,8.130928 76.7496247,8.08629345 C71.3906676,8.00818295 68.2751783,9.44392782 65.7978502,10.5300353 L63.8675158,2.05318951 C66.3527715,0.97824065 70.9546576,0.0409150083 75.7269836,0 C86.9284726,0 94.2574019,5.18876693 94.2970395,13.2341454 C94.3406403,23.4442997 79.2467723,24.0096708 79.3498288,28.5735539 C79.3855023,29.9572251 80.7926254,31.4338851 83.8764037,31.8095591 C85.4024382,31.9992559 89.6158792,32.1443183 94.3921687,30.0799701 L96.2670112,38.2815696 C93.6985165,39.1593824 90.3967329,40 86.2863494,40 C75.7428385,40 68.3267073,34.7405617 68.2672516,27.2122001 M114.282145,39.2932862 C112.236863,39.2932862 110.512642,38.1737028 109.743679,36.4552724 L93.7421172,0.602566485 L104.93568,0.602566485 L107.163294,6.37902176 L120.842112,6.37902176 L122.134286,0.602566485 L132,0.602566485 L123.390787,39.2932862 L114.282145,39.2932862 M115.847817,28.8413614 L119.078253,14.3128138 L110.231217,14.3128138 L115.847817,28.8413614 M54.6954535,39.2932862 L45.8721999,0.602566485 L56.538586,0.602566485 L65.3578762,39.2932862 L54.6954535,39.2932862 M38.915861,39.2932862 L27.8134648,12.958899 L23.3225632,35.3505672 C22.7953877,37.8501021 20.7144316,39.2932862 18.4035794,39.2932862 L0.253678458,39.2932862 L0,38.1699831 C3.72590236,37.4111957 7.95916162,36.187465 10.5236923,34.8781847 C12.0933277,34.0784823 12.5412287,33.3792076 13.0565131,31.4785195 L21.5626689,0.602566485 L32.8355053,0.602566485 L50.1173503,39.2932862 L38.915861,39.2932862"
                id="Shape"
                transform="translate(66.000000, 20.000000) scale(-1, 1) rotate(-180.000000) translate(-66.000000, -20.000000) "
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconMastercard({ className }) {
  return (
    <svg
      className={className}
      width="75px"
      height="45px"
      viewBox="0 0 200 120"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      data-testid="IconMastercard"
    >
      <title>mastercard</title>
      <g
        id="Rounded"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="Large" transform="translate(-32.000000, -404.000000)">
          <g id="large/mastercard" transform="translate(32.000000, 404.000000)">
            <rect
              id="Background"
              fill="#10427A"
              x="0"
              y="0"
              width="200"
              height="120"
              rx="8"
            ></rect>
            <g
              id="Logo"
              transform="translate(52.000000, 20.000000)"
              fillRule="nonzero"
            >
              <g id="mastercard">
                <rect
                  id="Rectangle-path"
                  fill="#FF5F00"
                  x="36.444549"
                  y="6.79534022"
                  width="27.0118354"
                  height="49.9501544"
                ></rect>
                <path
                  d="M38.1595777,31.7704094 C38.1595777,21.621526 42.7901781,12.6199079 49.9075824,6.79533142 C44.6767188,2.55927578 38.0738258,-7.82864208e-06 30.8706697,-7.82864208e-06 C13.8060499,-7.82864208e-06 6.74147421e-07,14.2084287 6.74147421e-07,31.7704094 C6.74147421e-07,49.3323899 13.8060499,63.5408265 30.8706697,63.5408265 C38.0738258,63.5408265 44.6767188,60.9815429 49.9075824,56.7454874 C42.7901781,51.0091618 38.1595777,41.9192929 38.1595777,31.7704094 Z"
                  id="Shape"
                  fill="#EB001B"
                ></path>
                <path
                  d="M99.900916,31.7704094 C99.900916,49.3323899 86.0948668,63.5408265 69.0302469,63.5408265 C61.8270906,63.5408265 55.2241973,60.9815429 49.9933344,56.7454874 C57.1964902,50.920911 61.7413387,41.9192929 61.7413387,31.7704094 C61.7413387,21.621526 57.1107383,12.6199079 49.9933344,6.79533142 C55.2241973,2.55927578 61.8270906,-7.82864208e-06 69.0302469,-7.82864208e-06 C86.0948668,-7.82864208e-06 99.900916,14.2966799 99.900916,31.7704094 Z"
                  id="Shape"
                  fill="#F79E1B"
                ></path>
              </g>
              <path
                d="M14.5714286,83.8235292 L14.5714286,78.5294113 C14.5714286,76.4999997 13.2380952,75.1764701 10.9523809,75.1764701 C9.80952379,75.1764701 8.57142853,75.5294112 7.71428569,76.6764701 C7.04761902,75.705882 6.09523806,75.1764701 4.66666666,75.1764701 C3.71428569,75.1764701 2.76190473,75.4411763 2,76.4117648 L2,75.3529409 L0,75.3529409 L0,83.8235292 L2,83.8235292 L2,79.1470587 C2,77.6470586 2.85714284,76.9411763 4.19047618,76.9411763 C5.52380951,76.9411763 6.19047617,77.7352936 6.19047617,79.1470587 L6.19047617,83.8235292 L8.19047617,83.8235292 L8.19047617,79.1470587 C8.19047617,77.6470586 9.14285713,76.9411763 10.3809523,76.9411763 C11.7142857,76.9411763 12.3809524,77.7352936 12.3809524,79.1470587 L12.3809524,83.8235292 L14.5714286,83.8235292 L14.5714286,83.8235292 Z M44.1904762,75.3529409 L40.9523808,75.3529409 L40.9523808,72.7941173 L38.9523812,72.7941173 L38.9523812,75.3529409 L37.1428572,75.3529409 L37.1428572,77.0294113 L38.9523812,77.0294113 L38.9523812,80.9117645 C38.9523812,82.8529411 39.8095237,84 42.0952384,84 C42.9523809,84 43.904762,83.7352938 44.5714285,83.3823527 L44.0000002,81.7941173 C43.4285714,82.1470584 42.7619049,82.2352938 42.2857143,82.2352938 C41.3333336,82.2352938 40.9523808,81.7058822 40.9523808,80.8235291 L40.9523808,77.0294113 L44.1904762,77.0294113 L44.1904762,75.3529409 L44.1904762,75.3529409 Z M61.1428572,75.1764701 C60.0000001,75.1764701 59.2380954,75.705882 58.7619047,76.4117648 L58.7619047,75.3529409 L56.7619047,75.3529409 L56.7619047,83.8235292 L58.7619047,83.8235292 L58.7619047,79.0588233 C58.7619047,77.6470586 59.4285713,76.8529409 60.6666666,76.8529409 C61.047619,76.8529409 61.5238096,76.9411763 61.9047619,77.0294113 L62.4761907,75.2647055 C62.0952383,75.1764701 61.5238096,75.1764701 61.1428572,75.1764701 L61.1428572,75.1764701 L61.1428572,75.1764701 Z M35.5238095,76.0588232 C34.5714286,75.4411763 33.2380953,75.1764701 31.8095238,75.1764701 C29.5238096,75.1764701 27.9999999,76.235294 27.9999999,77.9117644 C27.9999999,79.3235295 29.1428572,80.1176468 31.142857,80.382353 L32.0952382,80.470588 C33.1428571,80.6470583 33.7142856,80.9117645 33.7142856,81.3529411 C33.7142856,81.970588 32.9523809,82.4117646 31.6190475,82.4117646 C30.2857143,82.4117646 29.2380952,81.970588 28.5714284,81.5294114 L27.6190475,82.9411765 C28.6666666,83.6470584 30.0952381,84 31.5238094,84 C34.1904762,84 35.7142857,82.8529411 35.7142857,81.2647057 C35.7142857,79.7647056 34.4761904,78.9705883 32.5714285,78.7058821 L31.6190475,78.6176467 C30.7619047,78.5294113 30.0952381,78.352941 30.0952381,77.8235294 C30.0952381,77.2058821 30.7619047,76.8529409 31.8095238,76.8529409 C32.9523809,76.8529409 34.095238,77.2941175 34.6666665,77.5588232 L35.5238095,76.0588232 L35.5238095,76.0588232 Z M88.6666667,75.1764701 C87.5238096,75.1764701 86.7619049,75.705882 86.2857143,76.4117648 L86.2857143,75.3529409 L84.2857143,75.3529409 L84.2857143,83.8235292 L86.2857143,83.8235292 L86.2857143,79.0588233 C86.2857143,77.6470586 86.9523809,76.8529409 88.1904762,76.8529409 C88.5714285,76.8529409 89.0476191,76.9411763 89.4285715,77.0294113 L90.0000002,75.2647055 C89.6190479,75.1764701 89.0476191,75.1764701 88.6666667,75.1764701 L88.6666667,75.1764701 L88.6666667,75.1764701 Z M63.1428572,79.5882348 C63.1428572,82.1470584 65.0476191,84 68.0000002,84 C69.3333333,84 70.2857144,83.7352938 71.2380952,83.0294115 L70.2857144,81.5294114 C69.5238097,82.0588234 68.761905,82.3235292 67.904762,82.3235292 C66.2857144,82.3235292 65.1428572,81.2647057 65.1428572,79.5882348 C65.1428572,77.9999998 66.2857144,76.9411763 67.904762,76.8529409 C68.761905,76.8529409 69.5238097,77.1176467 70.2857144,77.6470586 L71.2380952,76.1470586 C70.2857144,75.4411763 69.3333333,75.1764701 68.0000002,75.1764701 C65.0476191,75.1764701 63.1428572,77.0294113 63.1428572,79.5882348 L63.1428572,79.5882348 L63.1428572,79.5882348 Z M81.6190477,79.5882348 L81.6190477,75.3529409 L79.6190477,75.3529409 L79.6190477,76.4117648 C78.9523811,75.6176466 78,75.1764701 76.7619047,75.1764701 C74.1904763,75.1764701 72.1904763,77.0294113 72.1904763,79.5882348 C72.1904763,82.1470584 74.1904763,84 76.7619047,84 C78.0952382,84 79.0476193,83.558823 79.6190477,82.7647057 L79.6190477,83.8235292 L81.6190477,83.8235292 L81.6190477,79.5882348 Z M74.2857145,79.5882348 C74.2857145,78.0882348 75.3333334,76.8529409 77.0476193,76.8529409 C78.666667,76.8529409 79.8095241,77.9999998 79.8095241,79.5882348 C79.8095241,81.0882349 78.666667,82.3235292 77.0476193,82.3235292 C75.3333334,82.2352938 74.2857145,81.0882349 74.2857145,79.5882348 L74.2857145,79.5882348 Z M50.3809523,75.1764701 C47.7142857,75.1764701 45.8095238,76.9411763 45.8095238,79.5882348 C45.8095238,82.2352938 47.7142857,84 50.4761905,84 C51.8095238,84 53.142857,83.6470584 54.1904759,82.8529411 L53.2380952,81.5294114 C52.4761905,82.0588234 51.5238094,82.4117646 50.5714287,82.4117646 C49.3333334,82.4117646 48.0952381,81.882353 47.8095238,80.382353 L54.5714283,80.382353 L54.5714283,79.6764701 C54.6666665,76.9411763 52.9523806,75.1764701 50.3809523,75.1764701 L50.3809523,75.1764701 L50.3809523,75.1764701 Z M50.3809523,76.7647055 C51.6190476,76.7647055 52.4761905,77.4705883 52.6666665,78.7941175 L47.9047621,78.7941175 C48.0952381,77.6470586 48.952381,76.7647055 50.3809523,76.7647055 L50.3809523,76.7647055 Z M100,79.5882348 L100,72 L98,72 L98,76.4117648 C97.3333334,75.6176466 96.3809523,75.1764701 95.1428574,75.1764701 C92.5714286,75.1764701 90.5714286,77.0294113 90.5714286,79.5882348 C90.5714286,82.1470584 92.5714286,84 95.1428574,84 C96.4761905,84 97.4285716,83.558823 98,82.7647057 L98,83.8235292 L100,83.8235292 L100,79.5882348 Z M92.6666668,79.5882348 C92.6666668,78.0882348 93.7142857,76.8529409 95.4285716,76.8529409 C97.0476193,76.8529409 98.1904764,77.9999998 98.1904764,79.5882348 C98.1904764,81.0882349 97.0476193,82.3235292 95.4285716,82.3235292 C93.7142857,82.2352938 92.6666668,81.0882349 92.6666668,79.5882348 L92.6666668,79.5882348 Z M25.8095239,79.5882348 L25.8095239,75.3529409 L23.8095239,75.3529409 L23.8095239,76.4117648 C23.1428571,75.6176466 22.1904762,75.1764701 20.9523809,75.1764701 C18.3809522,75.1764701 16.3809522,77.0294113 16.3809522,79.5882348 C16.3809522,82.1470584 18.3809522,84 20.9523809,84 C22.2857142,84 23.2380953,83.558823 23.8095239,82.7647057 L23.8095239,83.8235292 L25.8095239,83.8235292 L25.8095239,79.5882348 Z M18.3809523,79.5882348 C18.3809523,78.0882348 19.4285714,76.8529409 21.1428571,76.8529409 C22.7619047,76.8529409 23.9047618,77.9999998 23.9047618,79.5882348 C23.9047618,81.0882349 22.7619047,82.3235292 21.1428571,82.3235292 C19.4285714,82.2352938 18.3809523,81.0882349 18.3809523,79.5882348 Z"
                id="Shape"
                fill="#FFFFFF"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconMail({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconMail"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
      />
    </svg>
  );
}

export function IconClose({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconClose"
>
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6 18 18 6M6 6l12 12"
      />
    </svg>
  );
}

export function IconPaperClip({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      data-testid="IconPaperClip"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m18.375 12.739-7.693 7.693a4.5 4.5 0 0 1-6.364-6.364l10.94-10.94A3 3 0 1 1 19.5 7.372L8.552 18.32m.009-.01-.01.01m5.699-9.941-7.81 7.81a1.5 1.5 0 0 0 2.112 2.13"
      />
    </svg>
  );
}
