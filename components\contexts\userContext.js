"use client";

import { createContext, useContext, useState, useEffect } from "react";
import Favicon from "@/public/favicon.ico";
import { removeLoginCookie, getLoginCookie } from "@/components/tools";
const UserContext = createContext();

export function useUser() {
  return useContext(UserContext);
}

export function UserProvider({ children, lang }) {
  const [user, setUser] = useState(null);
  const [profilePicture, setProfilePicture] = useState(Favicon.src);
  const [reloadData, setReloadData] = useState(false);

  useEffect(() => {
    const startFetching = async () => {
      const storedInfo = getLoginCookie();
      setUser(storedInfo);
    };

    startFetching();
  }, [lang, reloadData]);

  const reloadUserData = () => {
    setReloadData(!reloadData);
  };

  const clearUserData = () => {
    setUser(null);
    removeLoginCookie();
  };

  return (
    <UserContext.Provider
      value={{ user, profilePicture, reloadUserData, clearUserData }}
      data-testid="user-context-provider"
    >
      {children}
    </UserContext.Provider>
  );
}
