import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import {
  getBroadcastChannel,
  getMessageAnnouncement,
} from "@/services/messages";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { useRouter } from "next/navigation";
import { getLocale } from "@/components/tools";
import Messages from "@/app/[lang]/portal/messages/messages";

// Mock tanımlamaları
jest.mock("@/services/messages", () => ({
  getBroadcastChannel: jest.fn(),
  getMessageAnnouncement: jest.fn(),
}));

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("@/components/tools", () => {
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();
  const mockFormatDate = jest.fn();
  const mockGetSpinIndicator = jest.fn();
  const mockGetDateFormat = { en: "DD/MM/YYYY", tr: "DD.MM.YYYY" };

  return {
    useResponseHandler: () => ({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    }),
    formatDate: mockFormatDate,
    getSpinIndicator: mockGetSpinIndicator,
    getDateFormat: mockGetDateFormat,
    getLocale: { en: "en", tr: "tr" },
    __mocks: {
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
      formatDate: mockFormatDate,
      getSpinIndicator: mockGetSpinIndicator,
      getDateFormat: mockGetDateFormat,
    },
  };
});

describe("Messages", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter, mockHandleResponse, mockHandleError, mockFormatDate;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        // Mock setup
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        mockRouter = { push: jest.fn() };
        useRouter.mockReturnValue(mockRouter);

        // Get mock functions
        const tools = require("@/components/tools");
        mockHandleResponse = tools.__mocks.handleResponse;
        mockHandleError = tools.__mocks.handleError;
        mockFormatDate = tools.__mocks.formatDate;

        // Clear all mocks
        mockHandleResponse.mockClear();
        mockHandleError.mockClear();
        mockFormatDate.mockClear();
        mockRouter.push.mockClear();

        // Default API responses
        getBroadcastChannel.mockResolvedValue({
          status: "SUCCESS",
          data: [
            { key: "1", value: "Channel 1" },
            { key: "2", value: "Channel 2" },
          ],
        });

        getMessageAnnouncement.mockResolvedValue({
          status: "SUCCESS",
          data: [
            {
              id: 1,
              broadcastDate: "2024-03-20",
              title: "Test Message",
              type: "Mesaj",
              broadcastChannel: "Channel 1",
              userName: "Test User",
            },
          ],
          totalNumberOfRecords: 1,
        });
        await act(async () => {
          render(<Messages dict={dict} lang={lang} />);
        });
      });

      test("form:Form alanları doğru şekilde render edilmeli.", () => {
        // Form alanlarını test et
        expect(screen.getByTestId("date")).toBeInTheDocument();
        expect(screen.getByTestId("broadcastChannel")).toBeInTheDocument();
        expect(screen.getByTestId("messageUser")).toBeInTheDocument();
        expect(screen.getByTestId("type")).toBeInTheDocument();

        // Butonları test et
        expect(screen.getByTestId("addButton")).toBeInTheDocument();
        expect(screen.getByTestId("resetButton")).toBeInTheDocument();
        expect(screen.getByTestId("filterButton")).toBeInTheDocument();
      });

      test("table:Tablo başlıkları doğru şekilde render edilmeli.", () => {
        // Tablo başlıklarını test et
        const tableHeaders = screen.getAllByRole("columnheader");
        expect(tableHeaders).toHaveLength(6);
        expect(tableHeaders[0]).toHaveTextContent(dict.public.date);
        expect(tableHeaders[1]).toHaveTextContent(dict.messages.summaryMessage);
        expect(tableHeaders[2]).toHaveTextContent(dict.messages.type);
        expect(tableHeaders[3]).toHaveTextContent(dict.messages.deliveries);
        expect(tableHeaders[4]).toHaveTextContent(dict.messages.sender);
        expect(tableHeaders[5]).toHaveTextContent(dict.public.action);
      });

      test("api:İlk yüklemede gerekli API çağrıları yapılmalı.", async () => {
        await waitFor(() => {
          expect(getBroadcastChannel).toHaveBeenCalledWith(lang);
          expect(getMessageAnnouncement).toHaveBeenCalled();
        });
      });

      test("api:Broadcast kanalları doğru şekilde yüklenmeli.", async () => {
        await waitFor(() => {
          expect(getBroadcastChannel).toHaveBeenCalledWith(lang);
        });
      });

      test("api:Mesaj listesi doğru şekilde yüklenmeli.", async () => {
        await waitFor(() => {
          expect(getMessageAnnouncement).toHaveBeenCalled();
        });
      });

      test("form:Filtre butonu tıklandığında form submit edilmeli.", async () => {
        const filterButton = screen.getByTestId("filterButton");
        fireEvent.click(filterButton);

        await waitFor(() => {
          expect(getMessageAnnouncement).toHaveBeenCalled();
        });
      });

      test("form:Reset butonu form alanlarını temizlemeli.", async () => {
        const resetButton = screen.getByTestId("resetButton");
        fireEvent.click(resetButton);

        await waitFor(() => {
          expect(getMessageAnnouncement).toHaveBeenCalled();
        });
      });

      test("link:Yeni ekle butonu doğru sayfaya yönlendirmeli.", () => {
        const addButton = screen.getByTestId("addButton");
        fireEvent.click(addButton);

        expect(mockRouter.push).toHaveBeenCalledWith(
          `/${lang}/portal/messages/action/`
        );
      });

      test("modal:İnceleme modalı açılmalı.", async () => {
        await waitFor(() => {
          const reviewButton = screen.getByTestId("reviewButton");
          fireEvent.click(reviewButton);
        });

        expect(screen.getByRole("dialog")).toBeInTheDocument();
      });

      test("modal:Silme modalı açılmalı.", async () => {
        await waitFor(() => {
          const deleteButton = screen.getByTestId("deleteButton");
          fireEvent.click(deleteButton);
        });

        expect(screen.getByRole("dialog")).toBeInTheDocument();
      });
    });
  });
});
