version: "3.7"

services:
  digitalinsurance-portal:
    image: harbor.gateway.com.tr/dev/digitalsigorta-portal:#{version}#
    deploy:
      replicas: 1
    ports:
      -  #{port}#:3000
    networks:
      - backend
    # volumes:
    # - ./logs:/srv/app/logs

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.12.1
    user: root
    depends_on:
      - digitalinsurance-portal
    networks:
      - backend
    volumes:
      - ./logs:/var/log/app:ro
      - ./filebeat.dev.yml:/usr/share/filebeat/filebeat.yml:ro
    # - /var/lib/docker/containers:/var/lib/docker/containers:ro
    # - /var/run/docker.sock:/var/run/docker.sock:ro

networks:
  backend:
    name: #{network_name}#
    external: true
