filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /var/log/app/*.log
    json.keys_under_root: true

    processors:
      - rename:
          fields:
            - from: "time"
              to: "timestamp"
            - from: "msg"
              to: "messageTemplate"
            - from: "level"
              to: "logLevel"

      - convert:
          fields:
            - from: "logLevel"
              to: "level"
              type: string
          ignore_missing: true
          fail_on_error: false

      - add_fields:
          target: "properties"
          fields:
            applicationName: "digital-insurance-operational-portal"
            environmentName: "Development"

output.elasticsearch:
  hosts: ["http://***********:9200"]
  username: "elastic"
  password: "htJsew49I8jPiL6How7U"
  index: "digital-insurance-log.FE.%{+yyyy.MM.dd}"

setup.template.name: "digital-insurance-log.FE"
setup.template.pattern: "digital-insurance-log.FE.*"
setup.template.enabled: true

logging:
  level: info
  to_files: false
  to_stdout: true
