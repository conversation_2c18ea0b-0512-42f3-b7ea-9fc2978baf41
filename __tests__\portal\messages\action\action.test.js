import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { useRouter } from "next/navigation";
import { useUser } from "@/components/contexts/userContext";
import { getBroadcastChannel, postSendMessage } from "@/services/messages";
import Action from "@/app/[lang]/portal/messages/action/action";

// Mock tanımlamaları
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/messages", () => ({
  getBroadcastChannel: jest.fn(),
  postSendMessage: jest.fn(),
}));

describe("Action", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter, mockPush;
      const mockUser = { id: 1, name: "Test User" };

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockPush = jest.fn();
        mockRouter = { push: mockPush };
        useRouter.mockReturnValue(mockRouter);
        useUser.mockReturnValue({ user: mockUser });
        getBroadcastChannel.mockResolvedValue({
          status: "SUCCESS",
          data: [
            { key: "1", value: "Channel 1" },
            { key: "2", value: "Channel 2" },
          ],
        });
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("form:Form alanları doğru şekilde render edilmeli.", async () => {
        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        expect(screen.getByTestId("title")).toBeInTheDocument();
        expect(screen.getByTestId("contents")).toBeInTheDocument();
        expect(screen.getByTestId("broadcastChannel")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("messageReleasePlace")).toBeInTheDocument();
        expect(screen.getByTestId("type")).toBeInTheDocument();
        expect(screen.getByTestId("files")).toBeInTheDocument();
        expect(screen.getByTestId("backButton")).toBeInTheDocument();
        expect(screen.getByTestId("saveButton")).toBeInTheDocument();
      });

      test("form:Başlık doğru şekilde gösterilmeli.", async () => {
        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });
        expect(screen.getByText(dict.messages.action.add)).toBeInTheDocument();
      });

      // #endregion Sayfa bileşenlerinin kontrolü

      // #region API çağrıları kontrolü
      test("api:Sayfa yüklendiğinde broadcast channel API'si çağrılmalı.", async () => {
        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });
        await waitFor(() => {
          expect(getBroadcastChannel).toHaveBeenCalledWith(lang);
        });
      });

      // #endregion API çağrıları kontrolü

      // #region Form işlevselliği kontrolü
      test("form:Dosya yükleme alanı doğru şekilde çalışmalı.", async () => {
        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        const file = new File(["test"], "test.png", { type: "image/png" });
        const uploadInput = screen.getByTestId("files").querySelector("input");

        await act(async () => {
          fireEvent.change(uploadInput, { target: { files: [file] } });
        });

        await waitFor(() => {
          expect(screen.getByText("test.png")).toBeInTheDocument();
        });
      });
      // #endregion Form işlevselliği kontrolü

      // #region Yönlendirme kontrolü
      test("link:Geri butonu doğru sayfaya yönlendirmeli.", async () => {
        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        await act(async () => {
          fireEvent.click(screen.getByTestId("backButton"));
        });

        expect(mockPush).toHaveBeenCalledWith(`/${lang}/portal/messages/`);
      });
      // #endregion Yönlendirme kontrolü
    });
  });
});
