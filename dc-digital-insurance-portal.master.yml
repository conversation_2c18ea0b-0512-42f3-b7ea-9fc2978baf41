version: "3.7"

services:
  digitalinsurance-portal:
    image: dockerregistry.gateway.com.tr/digitalinsurance-portal:#{version}#
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true

        - traefik.http.routers.digiportal.entrypoints=websecure
        - traefik.http.routers.digiportal.tls=true
        - traefik.http.routers.digiportal.rule=Host(`netaportal.emaasigorta.com.tr`) || Host(`www.netaportal.emaasigorta.com.tr`)
        - traefik.http.routers.digiportal.service=digiportal
        - traefik.http.services.digiportal.loadbalancer.server.port=3000
    networks:
      - backend
    ports:
      -  #{port}#:3000
networks:
  backend:
    name: #{network_name}#
    external: true
