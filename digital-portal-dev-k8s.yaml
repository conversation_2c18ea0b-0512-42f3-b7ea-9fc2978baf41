###############################################################################
# Digital Portal Deployment
###############################################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-portal
  namespace: emaa-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: digital-portal
  template:
    metadata:
      labels:
        app: digital-portal
    spec:
      containers:
        - name: digital-portal
          image: harbor.gateway.com.tr/dev/digitalsigorta-portal:#{version}#
          ports:
            - containerPort: 3000

          # Liveness Probe
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - >
                  for i in {1..3}; do
                    STATUS=$(curl -k -s -o /dev/null -w '%{http_code}' https://dev-privateapi.emaasigorta.com.tr/);
                    if [ "$STATUS" -eq 301 ]; then
                      exit 0;
                    fi;
                    sleep 3;
                  done;
                  exit 1;
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

      imagePullSecrets:
        - name: harbor

---
###############################################################################
# Digital Portal Service
###############################################################################
apiVersion: v1
kind: Service
metadata:
  name: digital-portal
  namespace: emaa-dev
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
  selector:
    app: digital-portal

---
###############################################################################
# IngressRoute for Digital Portal
###############################################################################
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: digital-portal
  namespace: emaa-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`dev-netaportal.emaasigorta.com.tr`) || Host(`www.dev-netaportal.emaasigorta.com.tr`)
      kind: Rule
      services:
        - name: digital-portal
          port: 3000
  tls:
    secretName: emaasigorta-tls
