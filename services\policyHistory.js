"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const getPolicyHistories = async (productId, lang) => {
  let res = await fetch(
    `${process.env.API_URL}offers/${productId}/policies/policy-histories`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

export { getPolicyHistories };
