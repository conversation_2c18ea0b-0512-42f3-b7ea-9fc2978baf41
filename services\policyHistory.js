"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const getPolicyHistories = async (productId, lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${productId}/policies/policy-histories`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getPolicyHistories", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

export { getPolicyHistories };
