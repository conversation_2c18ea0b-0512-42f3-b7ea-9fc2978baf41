"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  getSpinIndicator,
  useResponseHandler,
  getDateFormat,
  handleJustNumber,
  formatDate,
  replaceUndefinedWithNull,
  handleLocaleUpperCase,
  handleDownloadFile,
} from "@/components/tools";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Select,
  Spin,
  Table,
  Checkbox,
  Dropdown,
  Space,
} from "antd";
import { useEffect, useState } from "react";
import { getActiveProducts } from "@/services/product";

import {
  postPaginatedRequests,
  getRequestUnits,
  getRequestStatusListing,
  getPortalOfferRequestReasons,
  postPrintExcelDoc,
} from "@/services/requests";
import {
  ClearOutlined,
  EyeOutlined,
  FilterOutlined,
  FileSearchOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { IconArrowDown } from "@/components/icons";
import Link from "next/link";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import MessagingModal from "./messagingModal";
import DocumentsModal from "./documentsModal";

export default function RequestManagementListing({ dict, lang }) {
  const { reloadByNewToken } = useModalLogin();
  const { handleError, handleResponse } = useResponseHandler();
  const { user } = useUser();
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [formLoading, setFormLoading] = useState(false);
  const [offersList, setOffersList] = useState([]);
  const [onlineProductsList, setOnlineProductsList] = useState([]);
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const [showMessagingModal, setShowMessagingModel] = useState(false);
  const [showDocumentsModel, setShowDocumentsModel] = useState(false);
  //const [showReviewModal, setShowReviewModel] = useState(false);
  const [selectedData, setSelectedData] = useState();
  const [units, setUnits] = useState([]);
  const [requestStatus, setRequestStatus] = useState([]);
  const [requestReasons, setRequestReasons] = useState([]);
  const [updatedFilteredpagination, setUpdatedFilteredPagination] =
    useState(null);
  const [idtype, setIdtype] = useState(null);
  dayjs.extend(utc);
  const defaultModel = {
    offerId: null,
    requestReason: null,
    begDate: null,
    endDate: null,
    productNo: null,
    agencyName: null,
    civilRegistration: null,
    civilRegistrationNumber: null,
    policyNo: null,
    unit: null,
    name: null,
    surname: null,
    title: null,
    plate: null,
    policyStatus: null,
    channel: null,
    agencyName: null,
    includeClosedOffers: false,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "id",
      ascending: false,
    },
  };
  const idtypeList = [
    {
      id: 1,
      idType: dict.public.tckn,
    },
    {
      id: 2,
      idType: dict.public.ykn,
    },
    {
      id: 3,
      idType: dict.public.vkn,
    },
    {
      id: 4,
      idType: dict.public.passportNumber,
    },
  ];
  const [requestBody, setRequestBody] = useState(defaultModel);

  useEffect(() => {
    const startFetching = async () => {
      try {
        setFormLoading(true);
        await Promise.all([
          getProductsList(),
          getUnits(),
          getRequestReasonsFunc(),
          getStatusListingFunc(),
          getOffersList(defaultModel),
        ]);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getProductsList = async () => {
    try {
      const res = await getActiveProducts(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setOnlineProductsList(res?.data);
      } else {
        setOnlineProductsList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };
  const getRequestReasonsFunc = async () => {
    try {
      const res = await getPortalOfferRequestReasons(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setRequestReasons(res.data);
      } else {
        setRequestReasons([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      //form.resetFields(["unit"]);
      setRequestReasons([]);
    }
  };
  const getStatusListingFunc = async () => {
    try {
      const res = await getRequestStatusListing(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setRequestStatus(res?.data);
      } else {
        setRequestStatus([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };

  const getOffersList = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await postPaginatedRequests(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setOffersList(res?.data);
        } else {
          setOffersList([]);
        }
        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setOffersList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setOffersList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };

  const getUnits = async () => {
    try {
      const res = await getRequestUnits(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setUnits(res?.data);
      } else {
        setUnits([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };

  const getPrintDocument = async () => {
    try {
      setFormLoading(true);
      requestBody.begDate = requestBody.begDate
        ? dayjs
            .utc(
              dayjs(requestBody?.begDate).format("DD/MM/YYYY 00:00:00"),
              "DD/MM/YYYY HH:mm:ss"
            )
            .toISOString()
        : null;
      requestBody.endDate = requestBody.endDate
        ? dayjs
            .utc(
              dayjs(requestBody?.endDate).format("DD/MM/YYYY 00:00:00"),
              "DD/MM/YYYY HH:mm:ss"
            )
            .toISOString()
        : null;
      const res = await postPrintExcelDoc(lang, requestBody);
      if (res !== null) {
        const buffer = Buffer.from(res, "base64");
        handleDownloadFile(buffer, "Talep Yönetim Raporu.xlsx");
      } else {
        const error = { message: dict.public.error };
        handleError(error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const columns = [
    {
      title: `${dict.requestManagement.requestManagementListing.requestNumber}`,

      key: "id",
      dataIndex: "id",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.agencyCode}`,
      key: "channel",
      dataIndex: "channel",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.agencyName}`,
      key: "agencyName",
      dataIndex: "agencyName",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.user}`,
      key: "userName",
      dataIndex: "userName",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.unit}`,
      key: "unit",
      dataIndex: "unit",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.productName}`,
      key: "productName",
      dataIndex: "productName",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.requestReason}`,
      key: "requestReason",
      dataIndex: "requestReason",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.subject}`,
      key: "subject",
      dataIndex: "subject",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.requestDate}`,
      key: "requestDate",
      dataIndex: "requestDate",
      render: (val) => formatDate(val, lang, true),
    },
    {
      title: `${dict.public.status}`,
      key: "status",
      dataIndex: "status",
    },
    {
      title: `${dict.requestManagement.requestManagementListing.gmUser}`,
      key: "adminName",
      dataIndex: "adminName",
    },

    {
      title: `${dict.public.detail}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: "100px",
      render: (value) => {
        const items = [
          // {
          //   label: (
          //     <Link
          //       href={""}
          //       onClick={() => {
          //         setSelectedData(value);
          //         setShowReviewModel(true);
          //       }}
          //     >
          //       <div className="flex items-center gap-2">
          //         <EyeOutlined className="!text-lg !text-blue-500" />
          //         {dict.public.review}
          //       </div>
          //     </Link>
          //   ),
          //   key: "0",
          // },
          {
            label: (
              <Link
                href={""}
                onClick={() => {
                  setSelectedData(value);
                  setShowMessagingModel(true);
                }}
              >
                <div className="flex items-center gap-2">
                  <EyeOutlined className="!text-lg !text-blue-500" />
                  {dict.public.review}
                </div>
              </Link>
            ),
            key: "0",
          },
          {
            label: (
              <Link
                href={""}
                onClick={() => {
                  setSelectedData(value);
                  setShowDocumentsModel(true);
                }}
              >
                <div className="flex items-center gap-2">
                  <FileSearchOutlined className="!text-lg !text-indigo-600" />
                  {dict.public.files}
                </div>
              </Link>
            ),
            key: "1",
          },
        ];
        return (
          <div className="relative flex flex-wrap justify-center gap-1">
            <Dropdown
              onOpenChange={(a) => {}}
              menu={{ items }}
              trigger={["click"]}
              className="!h-10 !w-10"
            >
              <Button>
                <span>
                  <Space>
                    <IconArrowDown
                      className={"inline-block h-5 w-5 align-text-top"}
                    />
                  </Space>
                </span>
              </Button>
            </Dropdown>
          </div>
        );
      },
    },
  ];

  const onFinish = async (values) => {
    if (values["requestNum"] == "") {
      values["requestNum"] = null;
    }
    let model = {
      offerId:
        isNaN(values.requestNum) ||
        values.requestNum == "" ||
        values.requestNum == null
          ? null
          : Number(values.requestNum),
      requestReason: values.requestReason,
      begDate: values?.startDate
        ? dayjs
            .utc(
              dayjs(values?.startDate).format("DD/MM/YYYY 00:00:00"),
              "DD/MM/YYYY HH:mm:ss"
            )
            .toISOString()
        : null,

      endDate: values?.endDate
        ? dayjs
            .utc(
              dayjs(values?.endDate).format("DD/MM/YYYY 00:00:00"),
              "DD/MM/YYYY HH:mm:ss"
            )
            .toISOString()
        : null,
      productNo: values.products,
      civilRegistration: values.civilRegistiration,
      civilRegistrationNumber: values.civilRegistrationNumber,
      policyNo: values.policyNo,
      unit: values.unit,
      name: values.insurantName,
      surname: values.insurantSurname,
      title: values.title,
      plate: values.plateNumber,
      policyStatus: values?.status,
      agencyName: values.agentName,
      isOwnOffers: values?.waitingRequestForMe ? 1 : null,
      includeClosedOffers: !!values?.includeClosedOffers,
      pagination: {
        pageNumber: 1,
        pageSize: 10,
        orderBy: "Id",
        ascending: false,
      },
    };
    // const { begDate, endDate } = model;
    // if (begDate) {
    //   model.begDate = formatDateSimpleFormat(begDate);
    // } else {
    //   delete model.begDate;
    // }
    // if (endDate) {
    //   model.endDate = formatDateSimpleFormat(endDate);
    // } else {
    //   delete model.endDate;
    // }
    const updatedBody = replaceUndefinedWithNull({
      ...defaultModel,
      ...model,
    });

    getOffersList(updatedBody);
    setCurrentPageNumber(1);
    setUpdatedFilteredPagination(updatedBody);
  };

  const handleReset = () => {
    form.resetFields();
    getOffersList(defaultModel);
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      {/* 
      09-09-2024 tarihindeÇıkarılması istendi 
      {showReviewModal && (
        <ReviewModal
          dict={dict}
          lang={lang}
          isModalOpen={showReviewModal}
          setIsModalOpen={setShowReviewModel}
          selectedData={selectedData}
        />
      )} */}
      {showMessagingModal && (
        <MessagingModal
          dict={dict}
          lang={lang}
          isModalOpen={showMessagingModal}
          setIsModalOpen={setShowMessagingModel}
          selectedData={selectedData}
          getOffersList={getOffersList}
          defaultModel={defaultModel}
          currentPageNumber={currentPageNumber}
          updatedFilteredpagination={updatedFilteredpagination}
        />
      )}
      {showDocumentsModel && (
        <DocumentsModal
          dict={dict}
          lang={lang}
          isModalOpen={showDocumentsModel}
          setIsModalOpen={setShowDocumentsModel}
          selectedData={selectedData}
        />
      )}
      <Form
        form={form}
        layout="vertical"
        className="w-full"
        onFinish={onFinish}
      >
        <div className="rounded-xl bg-white p-6 drop-shadow-md">
          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="unit"
              data-testid="unit"
              className="!shrink !grow !basis-52"
              label={dict.requestManagement.requestManagementListing.unit}
            >
              <Select
                mode="multiple"
                showSearch
                data-testid="unit"
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={units?.map((product) => {
                  return {
                    value: product.unitName,
                    label: product.unitName,
                  };
                })}
              />
            </Form.Item>
            <Form.Item
              name="requestReason"
              data-testid="requestReason"
              className="!shrink !grow !basis-52"
              label={
                dict.requestManagement.requestManagementListing.requestReason
              }
            >
              <Select
                mode="multiple"
                showSearch
                allowClear
                optionFilterProp="children"
                data-testid="requestReason"
                filterOption={(input, option) =>
                  option?.label?.toLowerCase().indexOf(input?.toLowerCase()) >=
                  0
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={requestReasons?.map((request) => {
                  return {
                    value: request,
                    label: request,
                  };
                })}
              />
            </Form.Item>

            <Form.Item
              name="startDate"
              data-testid="startDate"
              className="!shrink !grow !basis-52"
              label={dict.public.startDate}
            >
              <DatePicker
                className="w-full"
                format={getDateFormat[lang]}
                disabledDate={(d) =>
                  formValues["endDate"]
                    ? !d || d.isAfter(formValues["endDate"])
                    : false
                }
              />
            </Form.Item>
            <Form.Item
              name="endDate"
              data-testid="endDate"
              className="!shrink !grow !basis-52"
              label={dict.public.endDate}
            >
              <DatePicker
                className="w-full"
                format={getDateFormat[lang]}
                disabledDate={(d) =>
                  formValues["startDate"]
                    ? !d || d.isBefore(formValues["startDate"])
                    : false
                }
              />
            </Form.Item>
            <Form.Item
              name="agentName"
              data-testid="agentName"
              className="!shrink !grow !basis-52"
              label={dict.offers.offersListing.agentName}
            >
              <Input
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
          </div>

          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="products"
              data-testid="products"
              className="!shrink !grow !basis-52"
              label={dict.public.products}
            >
              <Select
                mode="multiple"
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={onlineProductsList?.map((product) => {
                  return {
                    value: product.key,
                    label: `${product.key} - ${product.value}`,
                  };
                })}
              />
            </Form.Item>
            <Form.Item
              name="civilRegistiration"
              data-testid="civilRegistiration"
              className="!shrink !grow !basis-52"
              label={dict.offers.offersListing.idType}
            >
              <Select
                showSearch
                allowClear
                data-testid="civilRegistiration"
                onChange={(a) => {
                  let m = idtypeList.find((t) => t.id == a);
                  if (m) {
                    setIdtype(m);
                  } else {
                    setIdtype(1);
                  }
                }}
                options={idtypeList.map((a) => {
                  return {
                    value: a.id,
                    label: a.idType,
                  };
                })}
              />
            </Form.Item>
            {idtype?.id === 1 ? (
              <Form.Item
                name="civilRegistirationNumber"
                data-testid="civilRegistirationNumber"
                className="!shrink !grow !basis-52"
                label={idtype.idType}
              >
                <Input maxLength={11} onInput={handleJustNumber} />
              </Form.Item>
            ) : idtype?.id === 2 ? (
              <Form.Item
                name="civilRegistirationNumber"
                data-testid="civilRegistirationNumber"
                className="!shrink !grow !basis-52"
                label={idtype.idType}
              >
                <Input maxLength={11} onInput={handleJustNumber} />
              </Form.Item>
            ) : idtype?.id === 3 ? (
              <Form.Item
                name="civilRegistirationNumber"
                data-testid="civilRegistirationNumber"
                className="!shrink !grow !basis-52"
                label={idtype.idType}
              >
                <Input maxLength={10} onInput={handleJustNumber} />
              </Form.Item>
            ) : idtype?.id === 4 ? (
              <Form.Item
                name="civilRegistirationNumber"
                data-testid="civilRegistirationNumber"
                className="!shrink !grow !basis-52"
                label={idtype.idType}
              >
                <Input
                  maxLength={50}
                  onInput={(e) =>
                    (e.target.value = handleLocaleUpperCase(
                      e.target.value,
                      lang
                    ))
                  }
                />
              </Form.Item>
            ) : (
              <Form.Item
                name="tmp"
                data-testid="tmp"
                className="!shrink !grow !basis-52"
                label="&nbsp;"
              >
                <Input disabled />
              </Form.Item>
            )}
            <Form.Item
              name="policyNo"
              data-testid="policyNo"
              className="!shrink !grow !basis-52"
              label={
                dict.requestManagement.requestManagementCreating.offerPolicyNum
              }
            >
              <Input
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
            <Form.Item
              name="requestNum"
              data-testid="requestNum"
              className="!shrink !grow !basis-52"
              label={
                dict.requestManagement.requestManagementListing.requestNumber
              }
            >
              <Input maxLength={5} onInput={handleJustNumber} />
            </Form.Item>
          </div>

          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="insurantName"
              data-testid="insurantName"
              className="!shrink !grow !basis-52"
              label={dict.offers.offersListing.insuredName}
            >
              <Input
                disabled={idtype?.id == null || idtype?.id === 3}
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
            <Form.Item
              name="insurantSurname"
              data-testid="insurantSurname"
              className="!shrink !grow !basis-52"
              label={dict.offers.offersListing.insuredSurname}
            >
              <Input
                disabled={idtype?.id == null || idtype?.id === 3}
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
            <Form.Item
              name="plateNumber"
              data-testid="plateNumber"
              className="!shrink !grow !basis-52"
              label={dict.public.plateNumber}
            >
              <Input
                maxLength={50}
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
            <Form.Item
              name="title"
              data-testid="title"
              className="!shrink !grow !basis-52"
              label={dict.offers.offersListing.company}
            >
              <Input
                disabled={idtype?.id !== 3}
                onInput={(e) =>
                  (e.target.value = handleLocaleUpperCase(e.target.value, lang))
                }
              />
            </Form.Item>
            <Form.Item
              name="status"
              data-testid="status"
              className="!shrink !grow !basis-52"
              label={dict.public.status}
            >
              <Select
                mode="multiple"
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={requestStatus?.map((status) => {
                  return { value: status.code, label: status.value };
                })}
              />
            </Form.Item>
          </div>
          <div className="flex flex-row">
            {process.env.NEXT_PUBLIC_USER_TYPE === "PORTAL" && (
              <Form.Item
                className="!mb-0"
                valuePropName="checked"
                name={"waitingRequestForMe"}
              >
                <Checkbox className="select-none">
                  {
                    dict.requestManagement.requestManagementListing
                      .requestsWaitingForMe
                  }
                </Checkbox>
              </Form.Item>
            )}
            {process.env.NEXT_PUBLIC_USER_TYPE === "PORTAL" && (
              <Form.Item
                className="!mb-0"
                valuePropName="checked"
                name={"includeClosedOffers"}
              >
                <Checkbox className="select-none">
                  {
                    dict.requestManagement.requestManagementListing
                      .includeClosedOffers
                  }
                </Checkbox>
              </Form.Item>
            )}
          </div>
        </div>
        <div className="mt-5 flex flex-wrap justify-between gap-2">
          <Button icon={<DownloadOutlined />} onClick={getPrintDocument}>
            {dict.requestManagement.requestManagementListing.exportToExcel}
          </Button>
          <div className="flex flex-1 flex-wrap items-center justify-end gap-2">
            <Button
              type="primary"
              data-testid="clearButton"
              onClick={() => {
                handleReset();
              }}
              className="!bg-gray-500 !bg-opacity-75"
              icon={<ClearOutlined />}
            >
              {dict.public.clean}
            </Button>
            <Button
              type="primary"
              className="!bg-dark-gray"
              htmlType="submit"
              icon={<FilterOutlined />}
            >
              {dict.public.view}
            </Button>
          </div>
        </div>
      </Form>
      <div className="mt-5">
        <Table
          className="grid-table"
          columns={columns}
          dataSource={offersList}
          rowKey={"id"}
          scroll={{
            x: 1000,
          }}
          pagination={{
            current: currentPageNumber,
            total: totalRecords,
            defaultPageSize: 10,
            showSizeChanger: false,
            responsive: true,
            onChange: (pageNumber, pageSize) => {
              const updatedBody = {
                ...requestBody,
                pagination: {
                  pageNumber: pageNumber,
                  pageSize: pageSize,
                  orderBy: "id",
                  ascending: false,
                },
              };
              setCurrentPageNumber(pageNumber);
              getOffersList(updatedBody);
            },
          }}
        />
      </div>
    </Spin>
  );
}
