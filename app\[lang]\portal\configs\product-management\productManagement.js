"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  formatDate,
  getLocalISOString,
  getSpinIndicator,
  useResponseHandler,
} from "@/components/tools";
import {
  Button,
  Form,
  Select,
  Spin,
  Table,
  Tooltip,
  Switch,
  DatePicker,
} from "antd";
import { useEffect, useState } from "react";
import { IconCheck, IconClose, IconPlus } from "@/components/icons";
import { ClearOutlined, FilterOutlined, EyeOutlined } from "@ant-design/icons";
import { getDateFormat } from "@/components/tools";
import { useRouter } from "next/navigation";
import { getProductManagement } from "@/services/productManagement";
import { getActiveProducts } from "@/services/product";

export default function ProductManagement({ dict, lang }) {
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const [productsList, setProductsList] = useState([]);
  const [products, setProducts] = useState([]);
  const [form] = Form.useForm();
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const router = useRouter();
  const defaultModel = {
    productNo: null,
    begDate: null,
    endDate: null,
    b2b: 0,
    b2c: 0,
    publishedProducts: 0,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "Id",
      ascending: true,
    },
  };
  const [requestBody, setRequestBody] = useState(defaultModel);

  useEffect(() => {
    const startFetching = async () => {
      try {
        getProductManagementList(requestBody);
        getProductsList();
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getProductsList = async () => {
    setFormLoading(true);
    try {
      const res = await getActiveProducts(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setProducts(res?.data);
      } else {
        setProducts([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const getProductManagementList = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await getProductManagement(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setProductsList(res?.data);
        } else {
          setProductsList([]);
        }

        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setProductsList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setProductsList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };

  // #region table için kodlar
  const getCheckOrCloseIcon = (isChecked) => {
    return isChecked == "E" ? (
      <IconCheck className={"h-6 w-6 text-green-500"} />
    ) : (
      <IconClose className={"h-6 w-6 text-red-500"} />
    );
  };
  const columns = [
    {
      title: `${dict.configs.productManagement.productNo}`,
      key: "productNo",
      dataIndex: "productNo",
    },
    {
      title: `${dict.configs.productManagement.productName}`,
      key: "productName",
      dataIndex: "productName",
    },
    {
      title: `${dict.configs.productManagement.offerPrint}`,
      key: "offerPrint",
      render: (product) => <>{getCheckOrCloseIcon(product.offerPrint)}</>,
    },
    {
      title: `${dict.configs.productManagement.policyPrint}`,
      key: "policyPrint",
      render: (product) => <>{getCheckOrCloseIcon(product.policyPrint)}</>,
    },
    {
      title: `${dict.configs.productManagement.receiptPrint}`,
      key: "receiptPrint",
      render: (product) => <>{getCheckOrCloseIcon(product.receiptPrint)}</>,
    },
    {
      title: `${dict.configs.productManagement.b2b}`,
      key: "b2b",
      render: (product) => (
        <>
          {getCheckOrCloseIcon(
            product.userGroup == 0 || product.userGroup == 2 ? "E" : "H"
          )}
        </>
      ),
    },
    {
      title: `${dict.configs.productManagement.b2c}`,
      key: "b2c",
      render: (product) => (
        <>
          {getCheckOrCloseIcon(
            product.userGroup == 1 || product.userGroup == 2 ? "E" : "H"
          )}
        </>
      ),
    },
    {
      title: `${dict.configs.productManagement.releaseDate}`,
      key: "startDate",
      render: (product) => <>{formatDate(product.startDate, lang, false)}</>,
    },
    {
      title: `${dict.configs.productManagement.endDate}`,
      key: "endDate",
      render: (product) => <>{formatDate(product.endDate, lang, false)}</>,
    },
    {
      title: `${dict.configs.productManagement.status}`,
      key: "status",
      render: (product) => (
        <div>
          {product.status
            ? `${dict.configs.productManagement.published}`
            : `${dict.configs.productManagement.notPublished}`}
        </div>
      ),
    },
    {
      title: `${dict.public.action}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: 100,
      render: (value) => (
        <div className="flex flex-wrap justify-center gap-1">
          <Tooltip placement="top" title={dict.public.review}>
            <Button
              onClick={() => {
                router.push(
                  `/${lang}/portal/configs/product-management/action/?id=${value?.id}`
                );
              }}
              size="small"
              type="text"
              className="!flex items-center justify-center !border-none !px-1.5"
              icon={<EyeOutlined className="!text-lg !text-blue-500" />}
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  // #endregion table için kodlar

  // #region filtre kodlar
  const convertToISODate = (values) => {
    ["begDate", "endDate"].forEach((field) => {
      if (values?.[field]) {
        values[field] = getLocalISOString(new Date(values[field]));
      }
    });
  };

  const onFinish = async (values) => {
    values.b2b = +!!values.b2b;
    values.b2c = +!!values.b2c;
    values.publishedProducts = +!!values.publishedProducts;
    convertToISODate(values);
    const updatedBody = {
      ...requestBody,
      ...values,
    };
    getProductManagementList(updatedBody);
  };

  const handleReset = () => {
    form.resetFields();
    getProductManagementList(defaultModel);
  };
  // #endregion filtre ve rapor için kodlar

  return (
    <>
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl bg-white p-6 drop-shadow-md">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="productNo"
                data-testid="productNo"
                className="!shrink !grow !basis-52"
                label={dict.public.products}
              >
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={products?.map((product) => {
                    return {
                      value: product.key,
                      label: `${product.key} - ${product.value}`,
                    };
                  })}
                />
              </Form.Item>
              <Form.Item
                name="begDate"
                data-testid="begDate"
                className="!shrink !grow !basis-52"
                label={dict.public.startDate}
              >
                <DatePicker className="w-full" format={getDateFormat[lang]} />
              </Form.Item>
              <Form.Item
                name="endDate"
                data-testid="endDate"
                className="!shrink !grow !basis-52"
                label={dict.public.endDate}
              >
                <DatePicker className="w-full" format={getDateFormat[lang]} />
              </Form.Item>
            </div>
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="b2b"
                data-testid="b2b"
                label={dict.configs.productManagement.b2b}
              >
                <Switch />
              </Form.Item>
              <Form.Item
                name="b2c"
                data-testid="b2c"
                label={dict.configs.productManagement.b2c}
              >
                <Switch />
              </Form.Item>
              <Form.Item
                name="publishedProducts"
                data-testid="publishedProducts"
                label={dict.configs.productManagement.publishedProducts}
              >
                <Switch />
              </Form.Item>
            </div>
          </div>
          <div className="mt-5 flex flex-wrap justify-end gap-2">
            <Button
              data-testid="addButton"
              type="primary"
              onClick={() => {
                router.push(
                  `/${lang}/portal/configs/product-management/action/`
                );
              }}
            >
              <IconPlus className={"inline-block h-5 w-5 align-text-top"} />
              <span className="ml-1">{dict.public.add}</span>
            </Button>
            <div className="flex flex-1 flex-wrap justify-end gap-2">
              <Button
                data-testid="resetButton"
                type="primary"
                onClick={() => {
                  handleReset();
                }}
                className="!bg-gray-500 !bg-opacity-75"
                icon={<ClearOutlined />}
              >
                {dict.public.clean}
              </Button>
              <Button
                data-testid="filterButton"
                type="primary"
                className="!bg-dark-gray"
                htmlType="submit"
                icon={<FilterOutlined />}
              >
                {dict.public.filter}
              </Button>
            </div>
          </div>
        </Form>
        <div className="mt-5">
          <Table
            className="grid-table"
            columns={columns}
            dataSource={productsList}
            rowKey={"productNo"}
            scroll={{
              x: 1000,
            }}
            pagination={{
              current: currentPageNumber,
              total: totalRecords,
              defaultPageSize: 10,
              showSizeChanger: false,
              responsive: true,
              onChange: (pageNumber, pageSize) => {
                const updatedBody = {
                  ...requestBody,
                  pagination: {
                    pageNumber: pageNumber,
                    pageSize: pageSize,
                    orderBy: "id",
                    ascending: false,
                  },
                };
                setCurrentPageNumber(pageNumber);
                getProductsList(updatedBody);
              },
            }}
          />
        </div>
      </Spin>
    </>
  );
}
