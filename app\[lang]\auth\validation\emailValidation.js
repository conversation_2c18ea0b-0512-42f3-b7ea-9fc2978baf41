"use client";

import { useEffect, useState } from "react";
import { postConfirmEmail } from "@/services/identityConnect";
import { Player } from "@lottiefiles/react-lottie-player";
import { useSearchParams } from "next/navigation";
import { Button } from "antd";
import { IconArrowRight } from "@/components/icons";
import { useRouter } from "next/navigation";
import SuccessAnimation from "@/public/animation_ln2wrsir.json";
import ErrorAnimation from "@/public/animation_ln2wumxq.json";

export default function EmailValidation({ dict, lang }) {
  const [animation, setAnimation] = useState(null);
  const [status, setStatus] = useState(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  const validateEmail = async (searchParams) => {
    const userId = searchParams.get("userId");
    const code = searchParams.get("code");

    if (userId && code) {
      const values = {
        userId,
        code,
      };
      const res = await postConfirmEmail(values);

      if (res?.status == "SUCCESS") {
        setStatus("success");
        setAnimation(SuccessAnimation);
      } else {
        setStatus("error");
        setAnimation(ErrorAnimation);
      }
    }
  };

  useEffect(() => {
    validateEmail(searchParams);
  }, [searchParams]);

  const goLogin = () => {
    router.push(`/${lang}/auth/login/`);
  };

  return (
    <>
      <Player
        autoplay
        loop
        src={animation}
        style={{ height: "300px" }}
      ></Player>
      <h1
        className={`mt-16 text-center text-2xl font-extrabold leading-9 tracking-tight text-gray-900 ${
          status ?? "hidden"
        }`}
      >
        {status == "error" && dict.public.error}
        {status == "success" && dict.public.success}
      </h1>
      <div className="flex h-full w-full flex-wrap justify-center p-4">
        <Button
          size="large"
          className="mt-8 w-96 border-solid"
          onClick={() => goLogin()}
        >
          <span className="mr-1">{dict.auth.loginSignTitle}</span>
          <IconArrowRight className={"float-right mt-0.5 h-5 w-5"} />
        </Button>
      </div>
    </>
  );
}
