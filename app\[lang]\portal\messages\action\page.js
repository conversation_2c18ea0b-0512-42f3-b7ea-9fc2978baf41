"use server";

import { getDictionary } from "@/dictionaries";
import Action from "./action";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.messages.title} | ${dict.public.title}`,
  };
}

export default async function ActionPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <Action dict={dict} lang={lang} />;
}
