"use client";

import { Modal, Table } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { formatDate, formatMoney } from "@/components/tools";
export default function ReviewHistoryModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  policyHistory,
  selectedData,
}) {
  const columns = [
    {
      title: `${dict.offers.offersListing.transactionExecutor}`,
      key: "lastUserName",
      dataIndex: "lastUserName",
    },
    {
      title: `${dict.offers.offersListing.transactionDate}`,
      key: "entryDate",
      dataIndex: "entryDate",
      render: (val) => formatDate(val, lang, true),
    },
    {
      title: `${dict.offers.offersListing.status}`,
      key: "policyStatus",
      dataIndex: "policyStatus",
      render: (val) => {
        return val === "T"
          ? `${dict.offers.offersListing.quotation}`
          : `${dict.offers.offersListing.policy}`;
      },
    },
    {
      title: `${dict.offers.offersListing.grossPremium}`,
      key: "grossPremium",
      dataIndex: "grossPremium",
      render: (val) => formatMoney({ value: val }),
    },
    {
      title: `${dict.public.description}`,
      key: "explanation",
      dataIndex: "explanation",
    },
  ];
  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1024}
      title={
        <div className="bg-azure flex h-12">
          <h3 className="mx-4 flex items-center justify-start">
            {selectedData?.policyNo} - {selectedData?.insured}
          </h3>
          <div className="flex flex-1 flex-wrap justify-end">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      {
        <div className="flex flex-col px-4 pt-2">
          <Table
            className="grid-table"
            columns={columns}
            dataSource={policyHistory}
            rowKey={(record, index) => `${record.policyNo}-${index}`}
            pagination={false}
            scroll={{
              x: 800,
            }}
          />
        </div>
      }
    </Modal>
  );
}
