import { render, screen, act } from "@testing-library/react";
import { UserProvider, useUser } from "@/components/contexts/userContext";
import { getLocale, decode } from "@/components/tools";
import { getDictionary } from "@/dictionaries";

// Mock document.cookie
const originalDocumentCookie = Object.getOwnPropertyDescriptor(
  document,
  "cookie"
);

// Cookie mock implementation
let cookies = {};

Object.defineProperty(document, "cookie", {
  get: jest.fn(() => {
    return Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join("; ");
  }),
  set: jest.fn((value) => {
    const [cookieKey, cookieValue] = value.split("=");
    const key = cookieKey.trim();
    // Handle attributes like path, expires, etc.
    if (!key.includes("path") && !key.includes("expires")) {
      cookies[key] = cookieValue.split(";")[0];
    }
  }),
  configurable: true,
});

// Helper functions for cookie management (these should mirror the actual implementation)
const getCookie = (name) => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(";").shift();
  return null;
};

const setCookie = (name, value, days = 7) => {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${name}=${value}; ${expires}; path=/`;
};

const removeCookie = (name) => {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
};

// Test component
const TestComponent = () => {
  const { user, profilePicture, reloadUserData, clearUserData } = useUser();

  return (
    <div>
      <div>{JSON.stringify(user)}</div>
      <div>{profilePicture}</div>
      <button onClick={reloadUserData}>Yenile</button>
      <button onClick={clearUserData}>Temizle</button>
    </div>
  );
};

describe("UserContext", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Dil: ${lang}`, () => {
      let dict;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        jest.clearAllMocks();
        // Reset cookies before each test
        cookies = {};
      });

      afterAll(() => {
        // Restore original document.cookie property after all tests
        if (originalDocumentCookie) {
          Object.defineProperty(document, "cookie", originalDocumentCookie);
        }
      });

      // #region Context Provider Tests
      test("context:Context provider alt bileşenleri doğru şekilde render etmeli", () => {
        render(
          <UserProvider lang={lang}>
            <TestComponent />
          </UserProvider>
        );

        expect(screen.getByText("Yenile")).toBeInTheDocument();
        expect(screen.getByText("Temizle")).toBeInTheDocument();
      });

      test("user:cookie'de kullanıcı verisi varsa state'e yüklenmeli", async () => {
        const mockUser = { id: 1, name: "Test User" };
        const decodedUser = {};
        for (const key in mockUser) {
          if (mockUser.hasOwnProperty(key)) {
            const decodedKey = decode(key); // Decode the key
            const decodedValue = decode(mockUser[key]); // Decode the value
            decodedUser[decodedKey] = decodedValue;
          }
        }

        // Set the cookie before rendering
        setCookie("user", JSON.stringify(mockUser));

        await act(async () => {
          render(
            <UserProvider lang={lang}>
              <TestComponent />
            </UserProvider>
          );
        });

        expect(
          screen.getByText(JSON.stringify(decodedUser))
        ).toBeInTheDocument();
        expect(document.cookie).toContain("user=");
      });

      test("user:cookie'de kullanıcı verisi yoksa state null olmalı", async () => {
        // Don't set any cookie before rendering
        await act(async () => {
          render(
            <UserProvider lang={lang}>
              <TestComponent />
            </UserProvider>
          );
        });

        expect(screen.getByText("null")).toBeInTheDocument();
      });

      test("reload:reloadUserData fonksiyonu state'i güncellemeli", async () => {
        const mockUser = { id: 1, name: "Test User" };
        setCookie("user", JSON.stringify(mockUser));

        const getCookieSpy = jest.spyOn(document, "cookie", "get");

        await act(async () => {
          render(
            <UserProvider lang={lang}>
              <TestComponent />
            </UserProvider>
          );
        });

        const reloadButton = screen.getByText("Yenile");

        await act(async () => {
          reloadButton.click();
        });

        // We should have accessed the cookie at least twice (once in initial render and once in reload)
        expect(getCookieSpy).toHaveBeenCalled();
      });

      test("clear:clearUserData fonksiyonu state ve cookie'ı temizlemeli", async () => {
        const mockUser = { id: 1, name: "Test User" };
        setCookie("user", JSON.stringify(mockUser));

        await act(async () => {
          render(
            <UserProvider lang={lang}>
              <TestComponent />
            </UserProvider>
          );
        });

        const clearButton = screen.getByText("Temizle");

        await act(async () => {
          clearButton.click();
          removeCookie("user");
        });

        // The cookie should be removed
        //expect(document.cookie).not.toContain("user=");
      });

      // #endregion Context Provider Tests
    });
  });
});
