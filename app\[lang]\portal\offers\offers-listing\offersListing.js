"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  getSpinIndicator,
  useResponseHandler,
  getDateFormat,
  handleJustNumber,
  formatDate,
  formatMoney,
  formatDateSimpleFormat,
  handleLocaleUpperCase,
  OFFERSTATUS,
  isStatusPolicy,
} from "@/components/tools";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Select,
  Spin,
  Table,
  Dropdown,
  Space,
  App,
} from "antd";
import { useEffect, useState, useRef } from "react";
import { IconArrowDown } from "@/components/icons";
import { getActiveProducts } from "@/services/product";
import { postPaginatedPolicies } from "@/services/policy";
import { postGetQueryPolicyDetail } from "@/services/insurance";
import { getPolicyHistories } from "@/services/policyHistory";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import {
  ClearOutlined,
  EyeOutlined,
  FilterOutlined,
  HistoryOutlined,
  Dollar<PERSON>ircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import ReviewHistoryModal from "./reviewHistoryModal";
import ReviewModal from "./reviewModal";
import PrintButtonCustom from "./printButton";
import Link from "next/link";
export default function OffersListing({ dict, lang }) {
  // #region sabit değişkenler
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [formLoading, setFormLoading] = useState(false);
  const [offersList, setOffersList] = useState([]);
  const [onlineProductsList, setOnlineProductsList] = useState([]);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const [isReviewHistoryModal, setIsReviewHistoryModal] = useState(false);
  const [showReviewModal, setShowReviewModel] = useState(false);
  const [policyHistory, setPolicyHistory] = useState([]);
  const [userDetailInfo, setUserDetailInfo] = useState();
  const { message } = App.useApp();
  const offerPolicyStatus = useRef("");
  const [nextDisabled, setNextDisabled] = useState(false);
  if (typeof window !== "undefined") {
    window.__form__ = form;
  }
  const defaultModel = {
    productNo: null,
    civilRegistiration: null,
    civilRegistirationNumber: null,
    policyNo: null,
    name: null,
    surname: null,
    title: null,
    plate: null,
    policyStatus: null,
    agentCode: null,
    channel: null,
    agencyName: null,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "id",
      ascending: false,
    },
  };
  const [requestBody, setRequestBody] = useState(defaultModel);
  const [selectedData, setSelectedData] = useState();
  const [idtype, setIdtype] = useState(null);
  const idtypeList = [
    {
      id: 1,
      idType: dict.public.tckn,
    },
    {
      id: 2,
      idType: dict.public.ykn,
    },
    {
      id: 3,
      idType: dict.public.vkn,
    },
    {
      id: 4,
      idType: dict.public.passportNumber,
    },
  ];
  const statusList = [
    {
      id: "3",
      name: dict.offers.offersListing.quotation,
    },
    {
      id: "1",
      name: dict.offers.offersListing.active,
    },
    {
      id: "2",
      name: dict.offers.offersListing.cancelled,
    },
    {
      id: "0",
      name: dict.offers.offersListing.expiryDate,
    },
    {
      id: "4",
      name: dict.offers.offersCreating.compulsoryTrafficInsurance.auth,
    },
  ];

  const columns = [
    {
      title: `${dict.offers.offersListing.offerPolicyNum}`,
      key: "policyNo",
      dataIndex: "policyNo",
    },
    {
      title: `${dict.offers.offersListing.product}`,
      key: "productName",
      dataIndex: "productName",
    },
    {
      title: `${dict.public.nameSurname} / ${dict.offers.offersListing.company}`,
      key: "insured",
      dataIndex: "insured",
      width: 300,
      sorter: {
        compare: (a, b) => {
          return a.insured.localeCompare(b.insured);
        },
      },
    },
    // {
    //   title: `${dict.offers.offersListing.insurancePeriod}`,
    //   key: "dateDifference",
    //   dataIndex: "dateDifference",
    //   render: (val) => {
    //     return `${val} ${dict.public.month}`;
    //   },
    //   sorter: {
    //     compare: (a, b) => {
    //       return a.dateDifference - b.dateDifference;
    //     },
    //   },
    // },
    {
      title: `${dict.offers.offersListing.renewalNumber}`,
      key: "renewalNo",
      dataIndex: "renewalNo",
    },
    {
      title: `${dict.offers.offersListing.addendumNumber}`,
      key: "endorsNo",
      dataIndex: "endorsNo",
    },
    {
      title: `${dict.offers.offersListing.grossPremium}`,
      key: "grossPremium",
      dataIndex: "grossPremium",
      render: (val) => formatMoney({ value: val }),
      sorter: (a, b) => {
        return a.grossPremium - b.grossPremium;
      },
    },
    //todo bakılacak (şu an da gerekmiyor Eren den haber bekleniyor)
    // {
    //   title: `${dict.offers.title}`,
    //   key: "policyStatus",
    //   dataIndex: ["authorizeCode", "policyStatus"],
    //   render: (val, k) => {
    //     if (k.a) {
    //     }
    //     return k.authorizeCode?.length > 0
    //       ? dict.offers.offersCreating.compulsoryTrafficInsurance.auth
    //       : k.policyStatus == "T"
    //         ? `${dict.offers.offersListing.quotation}`
    //         : k.policyStatus == "O"
    //           ? `${dict.offers.offersListing.policy}`
    //           : "";
    //   },
    // },
    {
      title: `${dict.offers.offersListing.startDate}`,
      key: "begDate",
      dataIndex: "begDate",
      render: (val) => formatDate(val, lang, false),
      sorter: (a, b) => new Date(a.begDate) - new Date(b.begDate),
    },
    {
      title: `${dict.offers.offersListing.endDate}`,
      key: "endDate",
      dataIndex: "endDate",
      render: (val) => formatDate(val, lang, false),
      sorter: (a, b) => new Date(a.endDate) - new Date(b.endDate),
    },
    // {
    //   title: `${dict.offers.offersListing.validityDate}`,
    //   key: "issueDate",
    //   dataIndex: "issueDate",
    //   render: (val) => formatDate(val, lang, false),
    //   sorter: (a, b) => new Date(a.issueDate) - new Date(b.issueDate),
    // },
    {
      title: `${dict.offers.offersListing.offerPolicyStatus}`,
      key: "policyStatus",
      dataIndex: "policyStatus",
      render: (val) => {
        const statusObj = statusList.find((a) => a.id == val);
        if (statusObj) {
          offerPolicyStatus.current = statusObj.name;
          return statusObj.name;
        }
      },
    },
    {
      title: `${dict.public.detail}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: "100px",
      render: (value, a) => {
        const myDisabled = (value, receipt = false) => {
          if (value?.authorizeCode && value?.authorizeCode.length > 0) {
            return true;
          }
          if (receipt == false) {
            if (
              value.policyStatus == OFFERSTATUS.OFFER &&
              value?.offerPrint == "H"
            ) {
              return true;
            }
            if (
              isStatusPolicy(value.policyStatus) &&
              value?.policyPrint == "H"
            ) {
              return true;
            }
          }
          if (receipt) {
            if (
              isStatusPolicy(value.policyStatus) &&
              value?.receiptPrint == "E"
            ) {
              return false;
            } else {
              return true;
            }
          }

          return false;
        };
        const items = [
          {
            label: (
              <Link
                href={""}
                onClick={async () => {
                  setSelectedData(value);
                  await startUserDetail(value);
                }}
                data-testid="reviewButton"
              >
                <div className="flex items-center gap-2">
                  <EyeOutlined className="!text-lg !text-blue-500" />
                  {dict.public.review}
                </div>
              </Link>
            ),
            key: "0",
          },
          {
            label: (
              <PrintButtonCustom
                label={dict.offers.offersListing.informationFormPrinting}
                icon={
                  <InfoCircleOutlined className="!text-lg !text-gray-700" />
                }
                printType={7}
                isdisabled={myDisabled(value, false)}
                value={value}
                dict={dict}
                lang={lang}
                setFormLoading={setFormLoading}
                setOffersList={setOffersList}
                offersList={offersList}
              />
            ),
            key: "1",
          },
          {
            label: (
              <PrintButtonCustom
                label={dict.offers.offersListing.quotationPolicyPrinting}
                icon={<FileTextOutlined className="!text-lg !text-gray-700" />}
                printType={1}
                isdisabled={myDisabled(value, false)}
                value={value}
                dict={dict}
                lang={lang}
                setFormLoading={setFormLoading}
                setOffersList={setOffersList}
                offersList={offersList}
              />
            ),
            key: "2",
          },
          {
            label: (
              <PrintButtonCustom
                label={dict.offers.offersListing.collectionReceiptPrinting}
                icon={
                  <DollarCircleOutlined className="!text-lg !text-gray-700" />
                }
                printType={2}
                isdisabled={myDisabled(value, true)}
                value={value}
                dict={dict}
                lang={lang}
                setFormLoading={setFormLoading}
                setOffersList={setOffersList}
                offersList={offersList}
              />
            ),
            key: "3",
          },
          {
            label: (
              <Link
                href={""}
                onClick={() => {
                  setSelectedData(value);
                  getPolicyHistoriesList(value.policyNo);
                }}
                data-testid="historyButton"
              >
                <div className="flex items-center gap-2">
                  <HistoryOutlined className="!text-lg !text-blue-500" />
                  {dict.public.history}
                </div>
              </Link>
            ),
            key: "4",
          },
        ];
        return (
          <div className="relative flex flex-wrap justify-center gap-1">
            <Dropdown
              onOpenChange={(a) => {}}
              menu={{ items }}
              trigger={["click"]}
              className="!h-10 !w-10"
            >
              <Button>
                <span>
                  <Space>
                    <IconArrowDown
                      className={"inline-block h-5 w-5 align-text-top"}
                    />
                  </Space>
                </span>
              </Button>
            </Dropdown>
          </div>
        );
      },
    },
  ];
  // #endregion sabit değişkenler

  // #region action kodları
  const startUserDetail = async (value) => {
    try {
      setFormLoading(true);
      const userDetail = await postGetQueryPolicyDetail(lang, {
        productNo: value?.productNo,
        policyNo: String(value?.policyNo),
      });

      handleResponse(userDetail);

      if (userDetail?.data) {
        setUserDetailInfo(userDetail?.data);
        setShowReviewModel(true);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  useEffect(() => {
    const x = localStorage.getItem("policyNo");
    const startFetching = async () => {
      try {
        await Promise.all([
          getProductsList(),
          x && getOffersList({ ...defaultModel, policyNo: x }),
        ]);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        localStorage.removeItem("policyNo");
      }
    };

    if (x) {
      form.setFieldsValue({ policyNo: x ?? null });
    }
    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getOffersList = async (requestBody) => {
    try {
      setFormLoading(true);
      requestBody.policyStatus = requestBody.policyStatus
        ? parseInt(requestBody.policyStatus)
        : null;
      requestBody.recaptchaToken = await executeRecaptcha("app");
      const res = await postPaginatedPolicies(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          //start eğer 2. sayfada veri yoksa 1. sayfaya dön
          if (res.data.length == 0 && requestBody.pagination.pageNumber > 1) {
            setCurrentPageNumber(requestBody.pagination.pageNumber - 1);
            setNextDisabled(true);
            return;
          }
          setNextDisabled(false);
          setOffersList(res.data);
          // end
          //start eğer ilk yükleme ve 10 dan az ise next i disabled
          if (res.data.length < 10) {
            setNextDisabled(true);
            return;
          }
          //end eğer ilk yükleme ve 10 dan az ise next i disabled
        } else {
          setOffersList([]);
        }

        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setOffersList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setOffersList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };
  const getProductsList = async () => {
    try {
      const res = await getActiveProducts(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setOnlineProductsList(res?.data);
      } else {
        setOnlineProductsList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };

  const getPolicyHistoriesList = async (id) => {
    try {
      setFormLoading(true);
      const res = await getPolicyHistories(id, lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setPolicyHistory(res?.data);
      } else {
        setPolicyHistory([]);
      }
      setIsReviewHistoryModal(true);
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const replaceUndefinedWithNull = (obj) => {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => {
        return [key, value === undefined ? null : value];
      })
    );
  };

  const onFinish = async (values) => {
    const hasMoreThanOneDefinedValue =
      Object.entries(values).filter(
        ([key, value]) =>
          value !== undefined &&
          value !== null &&
          value !== "" &&
          key != "civilRegistiration"
      ).length > 1;
    if (!hasMoreThanOneDefinedValue) {
      message.error(dict.error.filterRuleOfferlisting);
      return;
    }
    const { begDate, endDate } = values;
    if (begDate) {
      values.begDate = formatDateSimpleFormat(begDate);
    } else {
      delete values.begDate;
    }
    if (endDate) {
      values.endDate = formatDateSimpleFormat(endDate);
    } else {
      delete values.endDate;
    }
    if (values["policyNo"] != "") {
      values["policyNo"] = values["policyNo"]?.trim();
    }
    if (values["policyNo"] == "") {
      values["policyNo"] = null;
    }
    values["channel"] = values?.agentCode?.trim() ?? "1001";
    values["agencyName"] = values?.agencyName?.trim();

    const updatedBody = replaceUndefinedWithNull({
      ...defaultModel,
      ...values,
    });
    getOffersList(updatedBody);
    setCurrentPageNumber(1);
  };
  const handleReset = async () => {
    form.resetFields();
    setIdtype(null);
    form.setFieldsValue({
      policyStatus: "",
      policyNo: "",
    });
  };
  // #endregion action kodları

  return (
    <>
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <ReviewHistoryModal
          dict={dict}
          lang={lang}
          isModalOpen={isReviewHistoryModal}
          setIsModalOpen={setIsReviewHistoryModal}
          policyHistory={policyHistory}
          selectedData={selectedData}
        />
        {showReviewModal ? (
          <ReviewModal
            dict={dict}
            lang={lang}
            setIsModalOpen={setShowReviewModel}
            selectedData={selectedData}
            userDetailInfo={userDetailInfo}
            offerPolicyStatus={offerPolicyStatus}
          />
        ) : (
          <>
            <Form
              form={form}
              layout="vertical"
              className="w-full"
              onFinish={onFinish}
            >
              <div className="rounded-xl bg-white p-6 drop-shadow-md">
                <div className="flex flex-wrap gap-x-6">
                  <div className="!shrink !grow !basis-52">
                    <Form.Item
                      name="policyStatus"
                      data-testid="policyStatus"
                      className="!shrink !grow !basis-52"
                      label={dict.offers.title}
                      rules={[
                        {
                          required: true,
                          message: `${dict.public.requiredField}`,
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        allowClear
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.label.toLowerCase() ?? "").includes(
                            input.toLocaleLowerCase()
                          )
                        }
                        filterSort={(optionA, optionB) =>
                          (optionA?.label ?? "")
                            .toLowerCase()
                            .localeCompare((optionB?.label ?? "").toLowerCase())
                        }
                        options={statusList?.map((status) => {
                          return { value: status.id, label: status.name };
                        })}
                      />
                    </Form.Item>
                  </div>
                  <Form.Item
                    name="policyNo"
                    data-testid="policyNo"
                    className="!shrink !grow !basis-52"
                    label={
                      dict.requestManagement.requestManagementCreating
                        .offerPolicyNum
                    }
                  >
                    <Input
                      maxLength={50}
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="productNo"
                    data-testid="productNo"
                    className="!shrink !grow !basis-52"
                    label={dict.public.products}
                  >
                    <Select
                      mode="multiple"
                      showSearch
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        (option?.label.toLowerCase() ?? "").includes(
                          input.toLocaleLowerCase()
                        )
                      }
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? "")
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? "").toLowerCase())
                      }
                      options={onlineProductsList?.map((product) => {
                        return {
                          value: product.key,
                          label: `${product.key} - ${product.value}`,
                        };
                      })}
                    />
                  </Form.Item>
                  <Form.Item
                    name="civilRegistiration"
                    data-testid="civilRegistiration"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.idType}
                  >
                    <Select
                      showSearch
                      allowClear
                      onChange={(a) => {
                        let m = idtypeList.find((t) => t.id == a);
                        if (m) {
                          setIdtype(m);
                        } else {
                          setIdtype(1);
                        }
                      }}
                      options={idtypeList.map((a) => {
                        return {
                          value: a.id,
                          label: a.idType,
                        };
                      })}
                    />
                  </Form.Item>

                  {idtype?.id === 1 ? (
                    <Form.Item
                      name="civilRegistirationNumber"
                      data-testid="civilRegistirationNumber"
                      className="!shrink !grow !basis-52"
                      label={idtype.idType}
                    >
                      <Input maxLength={11} onInput={handleJustNumber} />
                    </Form.Item>
                  ) : idtype?.id === 2 ? (
                    <Form.Item
                      name="civilRegistirationNumber"
                      data-testid="civilRegistirationNumber"
                      className="!shrink !grow !basis-52"
                      label={idtype.idType}
                    >
                      <Input maxLength={11} onInput={handleJustNumber} />
                    </Form.Item>
                  ) : idtype?.id === 3 ? (
                    <Form.Item
                      name="civilRegistirationNumber"
                      data-testid="civilRegistirationNumber"
                      className="!shrink !grow !basis-52"
                      label={idtype.idType}
                    >
                      <Input maxLength={10} onInput={handleJustNumber} />
                    </Form.Item>
                  ) : idtype?.id === 4 ? (
                    <Form.Item
                      name="civilRegistirationNumber"
                      data-testid="civilRegistirationNumber"
                      className="!shrink !grow !basis-52"
                      label={idtype.idType}
                    >
                      <Input
                        maxLength={50}
                        onInput={(e) =>
                          (e.target.value = handleLocaleUpperCase(
                            e.target.value,
                            lang
                          ))
                        }
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name="tmp"
                      data-testid="tmp"
                      className="!shrink !grow !basis-52"
                      label="&nbsp;"
                    >
                      <Input disabled />
                    </Form.Item>
                  )}

                  <Form.Item
                    name="begDate"
                    data-testid="begDate"
                    className="!shrink !grow !basis-52"
                    label={dict.public.startDate}
                  >
                    <DatePicker
                      className="w-full"
                      format={getDateFormat[lang]}
                      disabledDate={(d) =>
                        formValues["endDate"]
                          ? !d || d.isAfter(formValues["endDate"])
                          : false
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="endDate"
                    data-testid="endDate"
                    className="!shrink !grow !basis-52"
                    label={dict.public.endDate}
                  >
                    <DatePicker
                      className="w-full"
                      format={getDateFormat[lang]}
                      disabledDate={(d) =>
                        formValues["begDate"]
                          ? !d || d.isBefore(formValues["begDate"])
                          : false
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="name"
                    data-testid="name"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.insuredName}
                  >
                    <Input
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="surname"
                    data-testid="surname"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.insuredSurname}
                  >
                    <Input
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>
                </div>
                <div className="flex flex-wrap gap-x-6">
                  <Form.Item
                    name="title"
                    data-testid="title"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.company}
                  >
                    <Input
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="plate"
                    data-testid="plate"
                    className="!shrink !grow !basis-52"
                    label={dict.public.plateNumber}
                  >
                    <Input
                      maxLength={50}
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    name="agencyName"
                    data-testid="agencyName"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.agentName}
                  >
                    <Input
                      onInput={(e) =>
                        (e.target.value = handleLocaleUpperCase(
                          e.target.value,
                          lang
                        ))
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name="agentCode"
                    data-testid="agentCode"
                    className="!shrink !grow !basis-52"
                    label={dict.offers.offersListing.agentCode}
                  >
                    <Input maxLength={50} onInput={handleJustNumber} />
                  </Form.Item>
                </div>
              </div>
              <div className="mt-5 flex flex-wrap justify-end gap-2">
                <div className="flex flex-1 flex-wrap justify-end gap-2">
                  <Button
                    type="primary"
                    onClick={() => {
                      handleReset();
                    }}
                    className="!bg-gray-500 !bg-opacity-75"
                    icon={<ClearOutlined />}
                    data-testid="resetButton"
                  >
                    {dict.public.clean}
                  </Button>
                  <Button
                    type="primary"
                    className="!bg-dark-gray"
                    htmlType="submit"
                    icon={<FilterOutlined />}
                    data-testid="filterButton"
                  >
                    {dict.public.view}
                  </Button>
                </div>
              </div>
            </Form>
            <div className="mt-5">
              <Table
                className="grid-table"
                columns={columns}
                dataSource={offersList}
                rowKey={(record) =>
                  `${record.policyNo}-${Math.random().toString(36)}`
                }
                scroll={{
                  x: 1000,
                }}
                pagination={{
                  current: currentPageNumber,
                  total: totalRecords,
                  defaultPageSize: 10,
                  showSizeChanger: false,
                  responsive: true,
                  // ek
                  itemRender: (current, type, originalElement) => {
                    if (type === "prev") {
                      return <a type="button">{originalElement}</a>;
                    }
                    if (type === "next") {
                      {
                        return (
                          <a disabled={nextDisabled} type="button">
                            {{
                              ...originalElement,
                              props: {
                                ...originalElement.props,
                                disabled: nextDisabled,
                                className: `${originalElement.props.className} ${
                                  nextDisabled
                                    ? "ant-pagination-disabled opacity-30 cursor-not-allowed"
                                    : ""
                                }`,
                              },
                            }}
                            {/* <IconChevronRight className={"h-5 w-5"} /> */}
                          </a>
                        );
                      }
                    }
                    return null; // For other types (like page numbers), return the default element
                  },
                  //ek
                  onChange: (pageNumber, pageSize) => {
                    const updatedBody = {
                      ...requestBody,
                      pagination: {
                        pageNumber: pageNumber,
                        pageSize: pageSize,
                        orderBy: "policyNo",
                        ascending: false,
                      },
                    };
                    setCurrentPageNumber(pageNumber);
                    getOffersList(updatedBody);
                  },
                }}
              />
            </div>
          </>
        )}
      </Spin>
    </>
  );
}
