"use server";

import { getDictionary } from "@/dictionaries";
import ChangePassword from "./changePassword";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.profile.changePassword.title} | ${dict.profile.title} | ${dict.public.title}`,
  };
}

export default async function ChangePasswordPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <ChangePassword dict={dict} lang={lang} />;
}
