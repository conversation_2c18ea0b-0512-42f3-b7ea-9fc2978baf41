import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import {
  ModalLoginProvider,
  useModalLogin,
} from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { setLogout } from "@/services/identityConnect";
import { useRouter } from "next/navigation";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";

// Mock tanımlamaları
jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/identityConnect", () => ({
  setLogout: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Test bileşeni
const TestComponent = ({ dict, lang }) => {
  const { showModal } = useModalLogin();
  return (
    <button onClick={showModal} data-testid="show-modal-button">
      Mo<PERSON> Gö<PERSON>
    </button>
  );
};

describe("ModalLoginContext", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Dil: ${lang}`, () => {
      let dict, mockRouter, mockReloadUserData, mockClearUserData;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        jest.clearAllMocks();
        mockReloadUserData = jest.fn();
        mockClearUserData = jest.fn();
        mockRouter = { push: jest.fn() };

        useUser.mockReturnValue({
          reloadUserData: mockReloadUserData,
          clearUserData: mockClearUserData,
        });

        useRouter.mockReturnValue(mockRouter);
        setLogout.mockResolvedValue({});
      });

      // #region Context Provider Testleri
      test("context:Context provider alt bileşenleri doğru şekilde render etmeli", () => {
        const { getByTestId } = render(
          <ModalLoginProvider dict={dict} lang={lang}>
            <TestComponent dict={dict} lang={lang} />
          </ModalLoginProvider>
        );

        expect(getByTestId("show-modal-button")).toBeInTheDocument();
      });

      test("modal:showModal fonksiyonu modal görünürlüğünü true yapmalı ve kullanıcı verilerini temizlemeli", () => {
        const { getByTestId } = render(
          <ModalLoginProvider dict={dict} lang={lang}>
            <TestComponent dict={dict} lang={lang} />
          </ModalLoginProvider>
        );

        fireEvent.click(getByTestId("show-modal-button"));

        expect(mockClearUserData).toHaveBeenCalled();
        expect(screen.getByTestId("login-modal")).toBeInTheDocument();
      });

      test("logout:handleLogOut fonksiyonu setLogout'u çağırmalı ve login sayfasına yönlendirmeli", async () => {
        render(
          <ModalLoginProvider dict={dict} lang={lang}>
            <TestComponent dict={dict} lang={lang} />
          </ModalLoginProvider>
        );

        fireEvent.click(screen.getByTestId("show-modal-button"));

        const closeButton = screen.getByTestId("modal-close-button");
        await act(async () => {
          fireEvent.click(closeButton);
        });

        await waitFor(() => {
          expect(setLogout).toHaveBeenCalledWith(lang);
          expect(mockRouter.push).toHaveBeenCalledWith(
            `/${lang}/auth/login/?reload=true`
          );
        });
      });

      // #endregion Context Provider Testleri
    });
  });
});
