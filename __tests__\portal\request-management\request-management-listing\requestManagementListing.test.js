import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useUser } from "@/components/contexts/userContext";
import {
  postPaginatedRequests,
  getRequestUnits,
  getRequestStatusListing,
} from "@/services/requests";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import RequestManagementListing from "@/app/[lang]/portal/request-management/request-management-listing/requestManagementListing";

jest.mock(
  "@/app/[lang]/portal/request-management/request-management-listing/messagingModal",
  () => ({
    __esModule: true,
    default: jest.fn(({ selectedData }) => (
      <div data-testid="mock-message-modal">
        {selectedData ? selectedData.id : null}
      </div>
    )),
  })
);

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/requests", () => ({
  postPaginatedRequests: jest.fn(),
  getRequestUnits: jest.fn(),
  getRequestStatusListing: jest.fn(),
}));

describe("RequestManagementListing", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        const originalQuerySelector = document.querySelectorAll;
        document.querySelectorAll = function (selector) {
          if (selector.includes(".ant-select-item-option-selected:not")) {
            return [];
          }
          return originalQuerySelector.call(this, selector);
        };
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        SetIdtype = jest.fn();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test" } });
        postPaginatedRequests.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 1,
        });

        getRequestUnits.mockResolvedValueOnce({ status: "SUCCESS", data: [] });
        getRequestStatusListing.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
        });
        await act(async () => {
          render(<RequestManagementListing dict={dict} lang={lang} />);
        });
      });

      test("table: Kolon başlıkları bulunmalı", () => {
        const texts = [
          dict.requestManagement.requestManagementListing.requestNumber,
          dict.requestManagement.requestManagementListing.agencyCode,
          dict.requestManagement.requestManagementListing.agencyName,
          dict.requestManagement.requestManagementListing.user,
          dict.requestManagement.requestManagementListing.unit,
          dict.requestManagement.requestManagementListing.productName,
          dict.requestManagement.requestManagementListing.requestReason,
          dict.requestManagement.requestManagementListing.subject,
          dict.requestManagement.requestManagementListing.requestDate,
          dict.public.status,
          dict.requestManagement.requestManagementListing.gmUser,
          dict.public.detail,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("form: Bileşenler filtre bloğunda bulunmalı", async () => {
        expect(screen.getByTestId("unit")).toBeInTheDocument();
        expect(screen.getByTestId("requestReason")).toBeInTheDocument();
        expect(screen.getByTestId("startDate")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("agentName")).toBeInTheDocument();
        expect(screen.getByTestId("products")).toBeInTheDocument();
        expect(screen.getByTestId("civilRegistiration")).toBeInTheDocument();
        expect(screen.getByTestId("policyNo")).toBeInTheDocument();
        expect(screen.getByTestId("requestNum")).toBeInTheDocument();
        expect(screen.getByTestId("insurantName")).toBeInTheDocument();
        expect(screen.getByTestId("insurantSurname")).toBeInTheDocument();
        expect(screen.getByTestId("plateNumber")).toBeInTheDocument();
        expect(screen.getByTestId("title")).toBeInTheDocument();
        expect(screen.getByTestId("status")).toBeInTheDocument();
      });

      // #region form: IdType(civilRegistiration)'a göre koşullu render işlemleri

      test("form: IdType === null (tckn) durumunda civilRegistirationNumber,insuredName,insuredSurname,company disabled olmalı", async () => {
        const select = screen
          .getByTestId("civilRegistiration")
          .querySelector("select");

        fireEvent.change(select, { target: { value: null } });

        const input = screen.getByTestId("title").querySelector("input");
        expect(input).toBeDisabled();

        const input2 = screen.getByTestId("tmp").querySelector("input");
        expect(input2).toBeInTheDocument();

        const input3 = screen
          .getByTestId("insurantName")
          .querySelector("input");
        expect(input3).toBeDisabled();

        const input4 = screen
          .getByTestId("insurantSurname")
          .querySelector("input");
        expect(input4).toBeDisabled();
      });

      test("form: IdType === 1 (tckn) durumunda company disabled olmalı", async () => {
        const select = screen
          .getByTestId("civilRegistiration")
          .querySelector("select");

        fireEvent.change(select, { target: { value: "1" } });

        const input = screen.getByTestId("title").querySelector("input");
        expect(input).toBeDisabled();
      });

      test("form: IdType === 2 (ykn) durumunda company disabled olmalı", async () => {
        const select = screen
          .getByTestId("civilRegistiration")
          .querySelector("select");

        fireEvent.change(select, { target: { value: "2" } });

        const input = screen.getByTestId("title").querySelector("input");
        expect(input).toBeDisabled();
      });

      test("form: IdType === 3 (tin) durumunda company disabled olmalı", async () => {
        const select = screen
          .getByTestId("civilRegistiration")
          .querySelector("select");

        fireEvent.change(select, { target: { value: "3" } });

        const input = screen.getByTestId("insurantName").querySelector("input");
        expect(input).toBeDisabled();

        const input2 = screen
          .getByTestId("insurantSurname")
          .querySelector("input");
        expect(input2).toBeDisabled();
      });

      test("form: IdType === 4 (passport) durumunda company disabled olmalı", async () => {
        const select = screen
          .getByTestId("civilRegistiration")
          .querySelector("select");

        fireEvent.change(select, { target: { value: "4" } });

        const input = screen.getByTestId("title").querySelector("input");
        expect(input).toBeDisabled();
      });

      // #region API kontrolü
      test("api:İlk yükleme sırasında API fonksiyonları çağırılmalı.", async () => {
        await waitFor(() => {
          expect(postPaginatedRequests).toHaveBeenCalled();
          expect(getRequestUnits).toHaveBeenCalled();
          expect(getRequestStatusListing).toHaveBeenCalled();
        });
      });
      // #endregion API kontrolü

      // #region Form işlevselliği kontrolü

      test("table:Tabloya veri yüklenmeli.", async () => {
        jest.clearAllMocks();
        const data = Array.from({ length: 10 }, (_, index) => ({
          id: index,
          requestNumber: index,
          name: "Request " + index,
        }));
        postPaginatedRequests.mockResolvedValue({
          status: "SUCCESS",
          data: data,
          totalNumberOfRecords: 10,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.view));
        });
        await waitFor(() => {
          expect(postPaginatedRequests).toHaveBeenCalledTimes(1);
        });
      });

      test("filter:Filtre formu submit edildiğinde ürünleri filtrelemeli.", async () => {
        postPaginatedRequests.mockResolvedValue({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 1,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.view));
        });
        await waitFor(() => {
          expect(postPaginatedRequests).toHaveBeenCalledTimes(2);
        });
      });

      // #endregion Form işlevselliği kontrolü

      // TODO : filter submit ve table detail action yapılacaktır
    });
  });
});
