import { getDictionary } from "@/dictionaries";
import SectionHeaderComponent from "@/components/containers/sectionHeader";

export default async function RequestManagement({ children, params }) {
  const lang = params.lang;
  const dict = await getDictionary(lang);

  return (
    <>
      <SectionHeaderComponent
        title={dict.requestManagement.title}
        dict={dict}
        lang={lang}
      />
      <div className="p-4">{children}</div>
    </>
  );
}
