"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { FileTextOutlined } from "@ant-design/icons";
import { IconArrowSmallLeft } from "@/components/icons";
import { postPrintDocument } from "@/services/insurance";
import { formatMoney } from "@/components/tools";
import { postGetQueryPolicyDetail } from "@/services/insurance";
import {
  useResponseHandler,
  getStringToArrayBuffer,
  getSpinIndicator,
  getModifiedProductNoTitles,
} from "@/components/tools";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
export default function ReviewModal({
  dict,
  lang,
  setIsModalOpen,
  selectedData,
  userDetailInfo,
  offerPolicyStatus,
}) {
  const t = {
    C: dict.public.tckn,
    F: dict.public.ykn,
    T: dict.public.vkn,
    I: dict.public.passportNumber,
  };
  const yesNoHelper = {
    H: dict.public.no,
    E: dict.public.yes,
  };
  const fuelTypeTitle = {
    1: dict.offers.offersCreating.otherType,
    2: dict.offers.offersCreating.electricalType,
  };

  const [formLoading, setFormLoading] = useState(false);
  const { handleResponse, handleError } = useResponseHandler();
  const router = useRouter();
  const [zeyilValue, setZeyilValue] = useState({
    ...(userDetailInfo?.policyEndors?.[0] || {}),
  });

  const zeyils =
    [
      ...(userDetailInfo?.policyEndors ?? []),
      {
        policyNo: userDetailInfo.policyNo,
        endorsNo: 0,
        endorsTypeName:
          dict.offers.offersListing.mainPolicy.toLocaleUpperCase(lang),
      },
    ] ?? [];

  const [myselectedData, setmySelectedData] = useState(null);
  const hasmyZelilData = (userDetailInfo?.policyEndors ?? []).length !== 0;
  const differentClass =
    "-my-1.5 -mr-4 place-content-center rounded-sm bg-red-400 px-2";

  useEffect(() => {
    const startFetching = async () => {
      try {
        await startUserDetail({
          policyNo: zeyils[0].policyNo,
          productNo: userDetailInfo.productNo,
          endorsNo: zeyils[0].endorsNo,
        });
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
      }
    };
    if (hasmyZelilData) {
      startFetching();
    }
  }, []);

  const startUserDetail = async (value) => {
    try {
      setFormLoading(true);
      const userDetail = await postGetQueryPolicyDetail(lang, {
        productNo: value?.productNo,
        policyNo: String(value?.policyNo),
        endorsNo: String(value?.endorsNo) ?? null,
      });

      handleResponse(userDetail);
      if (userDetail?.data) {
        setmySelectedData(userDetail?.data);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const getPrintDocument = async (value, printType) => {
    try {
      const tmp = {
        productNo: userDetailInfo?.productNo?.toString(),
        policyNumber: value?.policyNo?.toString(),
        printType: printType,
        endorsNo: value?.endorsNo?.toString() ?? null,
      };
      setFormLoading(true);
      const res = await postPrintDocument(lang, tmp);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        const firstDecodedString = atob(res?.data?.certificate);
        const arrayBuffer = getStringToArrayBuffer(firstDecodedString);
        const blob = new Blob([arrayBuffer], {
          type: "application/pdf",
        });
        const blobURL = URL.createObjectURL(blob);
        window.open(blobURL, "_blank");
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  // Custom logic when back button is pressed
  useEffect(() => {
    const handlePopState = (event) => {
      event.preventDefault();
      router.push(`/${lang}/agent/offers/offers-listing/`);
      setIsModalOpen(false);
    };

    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  const renderDescriptionsItem = (
    label,
    value = "",
    mySelectedDataValue = "",
    isdifferent = false
  ) => {
    return (
      <Descriptions.Item label={label} span={3}>
        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
          <span>{value}</span>
          {myselectedData ? (
            <span className={`${isdifferent ? differentClass : ""}`}>
              {mySelectedDataValue}
            </span>
          ) : null}
        </div>
        <span></span>
      </Descriptions.Item>
    );
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      <div className="rounded-xl bg-white p-6 drop-shadow-md">
        <h1 className="mb-4 text-2xl font-medium">
          {`${getModifiedProductNoTitles(dict)[userDetailInfo?.productNo] ?? ""}
               - 
              ${dict.offers.offersListing.offerPolicyNum}
              : 
              ${userDetailInfo?.policyNo}/${userDetailInfo?.renewalNo} 
               - 
              ${dict.offers.offersListing.offerPolicy}
              : 
              ${userDetailInfo?.policyStatus == "T" ? dict.offers.offersListing.quotation : dict.offers.offersListing.policy} `}
          {`- ${dict.offers.offersListing.offerPolicyStatus}: ${userDetailInfo?.policyStatus == "T" ? userDetailInfo?.offerStatus : offerPolicyStatus.current} `}
        </h1>
        <div className="flex flex-col pt-2">
          <div className="flex flex-col gap-4 md:flex-row">
            <Descriptions
              className="review-desc w-full"
              bordered
              size="small"
              layout="horizontal"
              styles={{ label: { width: "30%" } }}
            >
              {renderDescriptionsItem(
                <h1 className="font-bold">
                  {dict.offers.offersListing.previewScreen}
                </h1>,
                <h1 className="font-bold">
                  {dict.offers.offersListing.mainPolicy}
                </h1>,
                <h1 className="font-bold">
                  {dict.offers.offersListing.addendum}{" "}
                  {dict.offers.offersListing.lastVersion}
                </h1>,
                false
              )}

              {renderDescriptionsItem(
                dict.offers.offersListing.fullName,
                userDetailInfo?.insuredFullName,
                myselectedData && myselectedData?.insuredFullName,
                myselectedData?.insuredFullName?.trim() !=
                  userDetailInfo?.insuredFullName?.trim()
              )}
              {renderDescriptionsItem(
                t[userDetailInfo?.idType ?? "C"] ?? "",
                userDetailInfo?.idNumber,
                myselectedData?.idNumber,
                myselectedData &&
                  myselectedData?.idNumber?.trim() !=
                    userDetailInfo?.idNumber?.trim()
              )}

              {(userDetailInfo?.productNo == 101 ||
                userDetailInfo?.productNo == 102) && (
                <>
                  {renderDescriptionsItem(
                    dict.public.plateNumber,
                    userDetailInfo?.plateNo,
                    myselectedData?.plateNo,
                    myselectedData &&
                      myselectedData?.plateNo?.trim() !=
                        userDetailInfo?.plateNo?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersListing.grossPremium,
                    `₺ ${parseInt(userDetailInfo?.grossPremiumAmount).toFixed(
                      1
                    )}`,
                    `₺ ${parseInt(myselectedData?.grossPremiumAmount).toFixed(
                      1
                    )}`,
                    myselectedData &&
                      myselectedData?.grossPremiumAmount?.trim() !=
                        userDetailInfo?.grossPremiumAmount?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.compulsoryTrafficInsurance.brand,
                    `${userDetailInfo?.brandValue}`,
                    `${myselectedData?.brandValue}`,
                    myselectedData &&
                      myselectedData?.brandValue?.trim() !=
                        userDetailInfo?.brandValue?.trim()
                  )}
                </>
              )}
              {userDetailInfo?.productNo == 608 && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersListing.grossPremium,
                    `₺ ${parseInt(userDetailInfo?.grossPremium).toFixed(1)}`,
                    `₺ ${parseInt(myselectedData?.grossPremium).toFixed(1)}`,
                    myselectedData?.grossPremium?.trim() !=
                      userDetailInfo?.grossPremium?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.public.nationality,
                    `${userDetailInfo?.nationalityValue}`,
                    `${myselectedData?.nationalityValue}`,
                    myselectedData &&
                      myselectedData?.nationalityValue?.trim() !=
                        userDetailInfo?.nationalityValue?.trim()
                  )}
                  {renderDescriptionsItem(
                    dict.offers.offersCreating.foreignHealthInsurance
                      .isExistingDisease,
                    `${yesNoHelper[userDetailInfo?.anyExistingDisease]}`,
                    `${yesNoHelper[myselectedData?.anyExistingDisease]}`,
                    myselectedData &&
                      myselectedData?.anyExistingDisease?.trim() !=
                        userDetailInfo?.anyExistingDisease?.trim()
                  )}
                  {userDetailInfo?.explanation
                    ? renderDescriptionsItem(
                        dict.offers.offersListing.writeExplanation,
                        `${userDetailInfo?.explanation}`,
                        `${myselectedData?.explanation}`,
                        myselectedData &&
                          myselectedData?.explanation?.trim() !=
                            userDetailInfo?.explanation?.trim()
                      )
                    : null}
                  {renderDescriptionsItem(
                    dict.offers.offersCreating.foreignHealthInsurance
                      .IsKvkkApproved,
                    `${yesNoHelper[userDetailInfo?.anyKvkkAndApproval]}`,
                    `${yesNoHelper[myselectedData?.anyKvkkAndApproval]}`,
                    myselectedData &&
                      myselectedData?.anyKvkkAndApproval?.trim() !=
                        userDetailInfo?.anyKvkkAndApproval?.trim()
                  )}
                </>
              )}
              {userDetailInfo?.productNo == 101 &&
                renderDescriptionsItem(
                  dict.offers.offersCreating.compulsoryTrafficInsurance
                    .chassisNumber,
                  `${userDetailInfo?.chassisNumber}`,
                  `${myselectedData?.chassisNumber}`,
                  myselectedData &&
                    myselectedData?.chassisNumber?.trim() !=
                      userDetailInfo?.chassisNumber?.trim()
                )}
              {(userDetailInfo?.productNo == 101 ||
                userDetailInfo?.productNo == 102) &&
                renderDescriptionsItem(
                  dict.offers.offersCreating.compulsoryTrafficInsurance
                    .engineNumber,
                  `${userDetailInfo?.engineNumber}`,
                  `${myselectedData?.engineNumber}`,
                  myselectedData &&
                    myselectedData?.engineNumber?.trim() !=
                      userDetailInfo?.engineNumber?.trim()
                )}
              {userDetailInfo?.productNo == 101 &&
                renderDescriptionsItem(
                  dict.offers.offersCreating.compulsoryTrafficInsurance
                    .fuelType,
                  `${fuelTypeTitle[userDetailInfo?.fuelType]}`,
                  `${fuelTypeTitle[myselectedData?.fuelType]}`,
                  myselectedData &&
                    myselectedData?.fuelType?.trim() !=
                      userDetailInfo?.fuelType?.trim()
                )}
              {(userDetailInfo?.productNo == 101 ||
                userDetailInfo?.productNo == 102) && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersCreating.compulsoryTrafficInsurance.model,
                    `${userDetailInfo?.modelValue}`,
                    `${myselectedData?.modelValue}`,
                    myselectedData &&
                      myselectedData?.modelValue?.trim() !=
                        userDetailInfo?.modelValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.compulsoryTrafficInsurance
                      .modelYear,
                    `${userDetailInfo?.modelYear}`,
                    `${myselectedData?.modelYear}`,
                    myselectedData &&
                      myselectedData?.modelYear?.trim() !=
                        userDetailInfo?.modelYear?.trim()
                  )}

                  {userDetailInfo?.damageStep &&
                  userDetailInfo?.damageStep != ""
                    ? renderDescriptionsItem(
                        dict.offers.offersListing.damageStep,
                        userDetailInfo?.damageStep,
                        myselectedData?.damageStep,
                        myselectedData?.damageStep?.trim() !=
                          userDetailInfo?.damageStep?.trim()
                      )
                    : null}

                  {userDetailInfo?.delaySurprise &&
                  userDetailInfo?.delaySurprise != ""
                    ? renderDescriptionsItem(
                        dict.offers.offersListing.delaySurprise,
                        userDetailInfo?.delaySurprise,
                        myselectedData?.delaySurprise,
                        myselectedData?.delaySurprise?.trim() !=
                          userDetailInfo?.delaySurprise?.trim()
                      )
                    : null}
                  {userDetailInfo?.registrationDate &&
                  userDetailInfo?.registrationDate != ""
                    ? renderDescriptionsItem(
                        dict.offers.offersListing.registrationDate,
                        userDetailInfo?.registrationDate,
                        myselectedData?.registrationDate,
                        myselectedData?.registrationDate?.trim() !=
                          userDetailInfo?.registrationDate?.trim()
                      )
                    : null}
                </>
              )}

              {userDetailInfo?.productNo == 101 && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersCreating.compulsoryTrafficInsurance.usage,
                    userDetailInfo?.usageValue,
                    myselectedData?.usageValue,
                    myselectedData?.usageValue?.trim() !=
                      userDetailInfo?.usageValue?.trim()
                  )}
                </>
              )}

              {userDetailInfo?.productNo == 500 && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersListing.occupation,
                    userDetailInfo?.occupationValue,
                    myselectedData?.occupationValue,
                    myselectedData?.occupationValue?.trim() !=
                      userDetailInfo?.occupationValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersListing.grossPremium,
                    `₺ ${parseInt(userDetailInfo?.grossPremium).toFixed(1)}`,
                    `₺ ${parseInt(myselectedData?.grossPremium).toFixed(1)}`,
                    myselectedData?.grossPremium?.trim() !=
                      userDetailInfo?.grossPremium?.trim()
                  )}
                </>
              )}
              {(userDetailInfo?.productNo == 620 ||
                userDetailInfo?.productNo == 203) && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersListing.grossPremium,
                    `₺ ${parseInt(userDetailInfo?.grossPremium).toFixed(1)}`,
                    `₺ ${parseInt(myselectedData?.grossPremium).toFixed(1)}`,
                    myselectedData?.grossPremium?.trim() !=
                      userDetailInfo?.grossPremium?.trim()
                  )}
                </>
              )}
              {userDetailInfo?.productNo == 203 && (
                <>
                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.riskAddress,
                    `${userDetailInfo?.rizikoAdress}`,
                    `${myselectedData?.rizikoAdress}`,
                    myselectedData?.rizikoAdress?.trim() !=
                      userDetailInfo?.rizikoAdress?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.buildingStyle,
                    `${userDetailInfo?.buildingStyleValue}`,
                    `${myselectedData?.buildingStyleValue}`,
                    myselectedData?.buildingStyleValue?.trim() !=
                      userDetailInfo?.buildingStyleValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance
                      .buildingConstructionYear,
                    `${userDetailInfo?.buildingConstructionYear}`,
                    `${myselectedData?.buildingConstructionYear}`,
                    myselectedData?.buildingConstructionYear?.trim() !=
                      userDetailInfo?.buildingConstructionYear?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.buildingArea,
                    `${userDetailInfo?.buildingArea}`,
                    `${myselectedData?.buildingArea}`,
                    myselectedData?.buildingArea?.trim() !=
                      userDetailInfo?.buildingArea?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.buildingUsage,
                    `${userDetailInfo?.buildingUseStyleValue}`,
                    `${myselectedData?.buildingUseStyleValue}`,
                    myselectedData?.buildingUseStyleValue?.trim() !=
                      userDetailInfo?.buildingUseStyleValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.idleTime,
                    `${userDetailInfo?.idleTimeValue}`,
                    `${myselectedData?.idleTimeValue}`,
                    myselectedData?.idleTimeValue?.trim() !=
                      userDetailInfo?.idleTimeValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance
                      .numberOfFloorsExcludingBasement,
                    `${userDetailInfo?.numberOfFloorsExceptBasement}`,
                    `${myselectedData?.numberOfFloorsExceptBasement}`,
                    myselectedData?.numberOfFloorsExceptBasement?.trim() !=
                      userDetailInfo?.numberOfFloorsExceptBasement?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.floorCode,
                    `${userDetailInfo?.floorCodeValue}`,
                    `${myselectedData?.floorCodeValue}`,
                    myselectedData?.floorCodeValue?.trim() !=
                      userDetailInfo?.floorCodeValue?.trim()
                  )}

                  {renderDescriptionsItem(
                    dict.offers.offersCreating.houseInsurance.titleOfInsured,
                    `${userDetailInfo?.insuredAdjectiveValue}`,
                    `${myselectedData?.insuredAdjectiveValue}`,
                    myselectedData?.insuredAdjectiveValue?.trim() !=
                      userDetailInfo?.insuredAdjectiveValue?.trim()
                  )}
                </>
              )}
            </Descriptions>
          </div>

          {(userDetailInfo?.productNo == 101 ||
            userDetailInfo?.productNo == 102) &&
            (userDetailInfo?.previousAgencyInformation ||
              userDetailInfo?.previousCompanyInformation ||
              userDetailInfo?.previousDigitCode ||
              userDetailInfo?.previousPolicyNumber ||
              userDetailInfo?.previousRenewalNumber) && (
              <div className="my-4">
                <h1 className="text-lg font-bold">
                  {dict.offers.offersListing.policyReferenceInformation.toLocaleUpperCase(
                    lang
                  )}
                </h1>
                <Descriptions
                  bordered
                  size="small"
                  className="review-desc w-full"
                  layout="horizontal"
                  styles={{ label: { width: "30%" } }}
                >
                  {renderDescriptionsItem(
                    <h1 className="font-bold">
                      {dict.offers.offersListing.policyReferenceInformation}
                    </h1>,
                    `${dict.offers.offersListing.mainPolicy}`,
                    `${dict.offers.offersListing.addendum}`,
                    false
                  )}

                  {userDetailInfo?.previousAgencyInformation
                    ? renderDescriptionsItem(
                        <h1 className="font-bold">
                          {dict.offers.offersListing.previousAgencyInformation}
                        </h1>,
                        `${userDetailInfo?.previousAgencyInformation}`,
                        `${myselectedData?.previousAgencyInformation}`,
                        false
                      )
                    : null}
                  {userDetailInfo?.previousCompanyInformation
                    ? renderDescriptionsItem(
                        <h1 className="font-bold">
                          {dict.offers.offersListing.previousCompanyInformation}
                        </h1>,
                        `${userDetailInfo?.previousCompanyInformation}`,
                        `${myselectedData?.previousCompanyInformation}`,
                        myselectedData &&
                          myselectedData?.previousCompanyInformation?.trim() !=
                            userDetailInfo?.previousCompanyInformation?.trim()
                      )
                    : null}
                  {userDetailInfo?.previousDigitCode
                    ? renderDescriptionsItem(
                        <h1 className="font-bold">
                          {dict.offers.offersListing.previousDigitCode}
                        </h1>,
                        `${userDetailInfo?.previousDigitCode}`,
                        `${myselectedData?.previousDigitCode}`,
                        myselectedData &&
                          myselectedData?.previousDigitCode?.trim() !=
                            userDetailInfo?.previousDigitCode?.trim()
                      )
                    : null}
                  {userDetailInfo?.previousPolicyNumber
                    ? renderDescriptionsItem(
                        <h1 className="font-bold">
                          {dict.offers.offersListing.previousPolicyNumber}
                        </h1>,
                        `${userDetailInfo?.previousPolicyNumber}`,
                        `${myselectedData?.previousPolicyNumber}`,
                        myselectedData &&
                          myselectedData?.previousPolicyNumber?.trim() !=
                            userDetailInfo?.previousPolicyNumber?.trim()
                      )
                    : null}
                  {userDetailInfo?.previousRenewalNumber
                    ? renderDescriptionsItem(
                        <h1 className="font-bold">
                          {dict.offers.offersListing.previousRenewalNumber}
                        </h1>,
                        `${userDetailInfo?.previousRenewalNumber}`,
                        `${myselectedData?.previousRenewalNumber}`,
                        myselectedData &&
                          myselectedData?.previousRenewalNumber?.trim() !=
                            userDetailInfo?.previousRenewalNumber?.trim()
                      )
                    : null}
                </Descriptions>
              </div>
            )}
          <div className="mt-4 flex flex-col gap-1">
            <h3 className="ml-1 text-lg font-bold uppercase">
              {dict.offers.offersListing.guarantees}
            </h3>
            <Descriptions
              bordered
              size="small"
              className="review-desc w-full"
              layout="horizontal"
              styles={{ label: { width: "30%" } }}
            >
              {renderDescriptionsItem(
                <h1 className="font-bold">
                  {dict.offers.offersListing.policyCoverages}
                </h1>,
                <h1 className="font-bold">
                  {dict.offers.offersListing.limits}
                </h1>,
                <h1 className="font-bold">
                  {dict.offers.offersListing.addendum}
                </h1>,
                false
              )}
              {userDetailInfo?.coverages?.map((cover, i) => (
                <Descriptions.Item label={cover?.coverName} key={i} span={3}>
                  <div className={`${"grid grid-cols-1 gap-3 md:grid-cols-2"}`}>
                    <span>
                      {formatMoney({ value: Number(cover?.coverAmount) })}
                    </span>
                    {myselectedData &&
                      myselectedData?.coverages.map((item, index) => {
                        if (item.coverName == cover.coverName) {
                          return (
                            <span
                              key={index}
                              className={`${item.coverAmount != cover.coverAmount ? differentClass : ""}`}
                            >
                              {formatMoney({ value: Number(item.coverAmount) })}
                            </span>
                          );
                        }
                      })}
                  </div>
                </Descriptions.Item>
              ))}
              {userDetailInfo?.coverDeductions?.map((cover, i) => (
                <Descriptions.Item label={cover?.coverName} key={i} span={3}>
                  <div className={`${"grid grid-cols-1 gap-3 md:grid-cols-2"}`}>
                    <span className="">
                      {formatMoney({ value: Number(cover?.coverAmount) })}
                    </span>
                    {myselectedData &&
                      myselectedData?.coverDeductions.map((item, index) => {
                        if (item.coverName == cover.coverName) {
                          return (
                            <span
                              key={index}
                              className={`${item.coverAmount != cover.coverAmount ? differentClass : ""}`}
                            >
                              {formatMoney({
                                value: Number(item.coverAmount),
                              })}
                            </span>
                          );
                        }
                      })}
                  </div>
                </Descriptions.Item>
              ))}
            </Descriptions>
            {userDetailInfo?.authorizeCode &&
              userDetailInfo.authorizeCode.length > 0 && (
                <div className="my-3">
                  <h3 className="ml-1 text-lg font-bold">
                    {dict.offers.offersListing.authReason}
                  </h3>
                  <p className="ml-2 font-medium">
                    {userDetailInfo?.authorizationMessage}
                  </p>
                </div>
              )}
          </div>
          {myselectedData && (
            <>
              <h1 className="my-3 text-lg font-bold uppercase">
                {dict.offers.offersListing.addendumPast}
              </h1>
              <Descriptions
                bordered
                size="small"
                className="review-desc w-full"
                layout="horizontal"
                styles={{ label: { width: "30%" } }}
              >
                {renderDescriptionsItem(
                  <h1 className="font-bold">
                    {dict.offers.offersListing.addendumNumber}
                  </h1>,
                  <h1 className="font-bold">
                    {dict.offers.offersListing.addendumType}
                  </h1>,
                  <h1 className="font-bold">
                    {dict.offers.offersListing.addendumDetail}
                  </h1>,
                  false
                )}

                {zeyils &&
                  zeyils?.map((zeyil, i) => (
                    <Descriptions.Item
                      key={i}
                      label={zeyil.endorsNo ?? ""}
                      span={3}
                    >
                      <div
                        className={`${"grid grid-cols-1 items-center gap-3 md:grid-cols-2"}`}
                      >
                        <h1 className="font-bold">
                          {zeyil?.endorsTypeName ?? ""}
                        </h1>
                        <div className={`flex flex-wrap items-center gap-2`}>
                          {zeyil.endorsNo != 0 ? (
                            <Button
                              type={
                                zeyilValue?.endorsNo == zeyil?.endorsNo
                                  ? "primary"
                                  : "default"
                              }
                              onClick={async () => {
                                setZeyilValue(zeyil);
                                zeyilValue?.endorsNo != zeyil?.endorsNo &&
                                  (await startUserDetail({
                                    policyNo: zeyil?.policyNo,
                                    productNo: userDetailInfo?.productNo,
                                    endorsNo: zeyil?.endorsNo,
                                  }));
                              }}
                            >
                              {dict.public.select}
                            </Button>
                          ) : null}

                          <Button
                            disabled={
                              zeyilValue?.endorsNo != zeyil?.endorsNo &&
                              zeyil.endorsNo != 0
                            }
                            onClick={async () => {
                              await getPrintDocument(zeyil, 1);
                            }}
                          >
                            {dict.public.takePrint}
                          </Button>
                          {zeyil.endorsNo != 0 ? (
                            <Button
                              onClick={async () => {
                                await getPrintDocument(zeyil, 2);
                              }}
                            >
                              {dict.offers.offersListing.collectionReceipt}
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    </Descriptions.Item>
                  ))}
              </Descriptions>
            </>
          )}
        </div>

        <div className="mt-4 flex justify-between">
          <Button
            type="primary"
            className="!flex items-center gap-1 !bg-gray-500 !bg-opacity-75"
            onClick={() => setIsModalOpen(false)}
          >
            <IconArrowSmallLeft className={"h-4 w-4"} />
            {dict.public.back}
          </Button>
          <div className="flex gap-2">
            <Button
              // admin tarafta herhangi engel olmadan indirebilmesi istendi
              // disabled={
              //   (selectedData?.authorizeCode &&
              //     selectedData?.authorizeCode.length > 0) ||
              //   (userDetailInfo?.policyStatus == "T" &&
              //     userDetailInfo?.offerPrint == "H") ||
              //   (userDetailInfo?.policyStatus == "O" &&
              //     userDetailInfo?.policyPrint == "H")
              // }
              icon={<FileTextOutlined className="!text-lg !text-gray-700" />}
              onClick={async () => {
                // if (selectedData.informationFormReadCheck) {
                //   await getPrintDocument(selectedData, 1);
                // } else {
                //   message.error(
                //     dict.error.informationFormPrintingFirstToBeDownloaded
                //   );
                // }
                await getPrintDocument(selectedData, 1);
              }}
            >
              {dict.offers.offersListing.quotationPolicyPrinting}
            </Button>
          </div>
        </div>
      </div>
    </Spin>
  );
}
