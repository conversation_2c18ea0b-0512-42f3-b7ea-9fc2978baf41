import { render, waitFor, fireEvent } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { useUser } from "@/components/contexts/userContext";
import ChangePassword from "@/app/[lang]/portal/profile/change-password/changePassword";

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

describe("ChangePassword", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, changePassword;
      beforeAll(async () => {
        dict = await getDictionary(lang);
      });
      beforeEach(() => {
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });

        changePassword = render(<ChangePassword dict={dict} lang={lang} />);
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("input:currentPassword olmalı", () => {
        const { getByTestId } = changePassword;
        const currentPassword = getByTestId("currentPassword");
        expect(currentPassword).toBeInTheDocument();
      });

      test("input:newPassword olmalı", () => {
        const { getByTestId } = changePassword;
        const currentPassword = getByTestId("newPassword");
        expect(currentPassword).toBeInTheDocument();
      });

      test("input:confirmationPassword olmalı", () => {
        const { getByTestId } = changePassword;
        const currentPassword = getByTestId("confirmationPassword");
        expect(currentPassword).toBeInTheDocument();
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region Form validasyon kontrolü
      test(`form:currentPassword alanı boş geçilemez olmalı`, async () => {
        const { getByTestId, findByText } = changePassword;
        const saveButton = getByTestId("saveButton");
        fireEvent.click(saveButton);
        await waitFor(async () => {
          const newPassword = getByTestId("currentPassword");
          expect(newPassword).toBeInTheDocument();
          const errorMessage = await findByText(dict.public.requiredField);
          expect(errorMessage).toBeInTheDocument();
        });
      });

      test(`form:newPassword alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId, findByText } = changePassword;
        const saveButton = getByTestId("saveButton");
        fireEvent.click(saveButton);
        await waitFor(async () => {
          const newPassword = getByTestId("newPassword");
          expect(newPassword).toBeInTheDocument();
          const errorMessage = await findByText(dict.public.passwordPattern);
          expect(errorMessage).toBeInTheDocument();
        });
      });
      test(`form:confirmationPassword alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId, findByText } = changePassword;
        const saveButton = getByTestId("saveButton");
        fireEvent.click(saveButton);
        await waitFor(async () => {
          const confirmationPassword = getByTestId("confirmationPassword");
          expect(confirmationPassword).toBeInTheDocument();
          const errorMessage = await findByText(dict.public.passwordPattern);
          expect(errorMessage).toBeInTheDocument();
        });
      });

      test("password ve confirmPassword eşleşmediğinde hata mesajı gösterilmeli", async () => {
        const { getByTestId, findByText } = changePassword;
        const newPassword = getByTestId("newPassword").querySelector("input");
        const confirmationPassword = getByTestId(
          "confirmationPassword"
        ).querySelector("input");

        const saveButton = getByTestId("saveButton");
        fireEvent.change(newPassword, { target: { value: "diff1" } });
        fireEvent.change(confirmationPassword, {
          target: { value: "diff2" },
        });
        fireEvent.click(saveButton);
        const errorMessage = await findByText(
          dict.profile.changePassword.doNotMatchPassword
        );
        expect(errorMessage).toBeInTheDocument();
      });

      test(`button:submit olmalı ve başlık içermeli. ${lang}`, () => {
        const { getByTestId } = changePassword;
        const saveButton = getByTestId("saveButton");
        expect(saveButton).toBeInTheDocument();
        expect(saveButton.textContent).toBe(dict.public.save);
      });
      // #endregion Form validasyon kontrolü
    });
  });
});
