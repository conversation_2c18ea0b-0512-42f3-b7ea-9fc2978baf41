{"public": {"title": "Digital Insurance Operational Portal", "login": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "add": "Add", "edit": "Edit", "delete": "Delete", "update": "Update", "save": "Save", "cancel": "Cancel", "success": "Success.", "sureDelete": "Are you sure to delete?", "requiredField": "Required field.", "description": "Description", "name": "Name", "surname": "Surname", "nameSurname": "Name Surname", "nationality": "Nationality", "email": "E-mail", "address": "Address", "registerDate": "Register Date", "startDate": "Start Date", "endDate": "End Date", "birthDate": "Birth Date", "requestDate": "Request Date", "approvalDate": "Approval Date", "passportNumber": "Passport Number", "citizenshipNumber": "Citizenship Number", "tckn": "Identification Number", "clickToUpload": "Click To Upload", "gender": "Gender", "note": "Note", "age": "Age", "back": "Back", "saveAndExit": "Save and Exit", "saveAndNext": "Save and Next", "totalAmount": "Total Amount", "download": "Download", "showInBrowser": "Show in Browser", "username": "Username", "password": "Password", "passwordPattern": "Your password must contain at least one digit, at least one lowercase letter, at least one uppercase letter, should not contain spaces, and must be 8-32 characters in length.", "createdBy": "Created By", "updatedBy": "Updated By", "export": "Export", "error": "Error", "companyName": "Company Name", "taxNumber": "Tax Number", "phoneNumber": "Phone Number", "phoneNumberCode": "Code", "confirmPassword": "Verify Password", "okay": "Okay", "continue": "Continue", "total": "Total", "close": "Close", "createReport": "Create Report", "clean": "Clean", "filter": "Filter", "action": "Action", "submitForApproval": "Submit for Approval", "files": "Files", "status": "Status", "documents": "Documents", "country": "Country", "city": "City", "date": "Date", "submitTheRequestForApproval": "Submit The Request For Approval.", "reasonForRejection": "Reason for Rejection", "rejectionDate": "Rejection Date", "giveUp": "Give Up", "selectFile": "Select File", "validExtensions": "Valid extensions: pdf, jpeg, jpg, png", "validExtensionsJustImage": "Valid extensions: jpeg, jpg, png", "validFileSize": "Valid file size: 5MB", "recommendedSize": "The recommended size is 800x400 pixels and its multiples.", "person": "Person", "validateSpecialCharacters": "Should not contain special characters.", "kvkkPolicy": "KVKK Policy", "userAgreement": "User Agreement", "kvkkClarificationText": "KVKK Ilumination Text", "explicitConsentText": "Explicit Consent Text", "readAndAccept": "i agree", "code": "Code", "location": "Location", "verify": "Verify", "receiveOffer": "Receive Offer", "newOffer": "<PERSON> Offer", "vkn": "TIN", "ykn": "YKN", "products": "Products", "insurant": "Insurant", "plateNumber": "Plate Number", "review": "Review", "print": "Print", "issueAPolicy": "Issue a Policy", "history": "History", "active": "Active", "passive": "Passive", "warning": "Warning", "month": "Month", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "iAcceptSale": "I accept the pre-information form and the distance sales agreement.", "payment": "Payment", "revise": "Update", "view": "View", "detail": "Detail", "validation": "Validation", "select": "Select", "takePrint": "Take Print"}, "dashboard": {"title": "Dashboard", "hello": "Hello", "emaaAgency": "EMAA SİGORTA A.Ş", "pendingOffers": "Pending Offers", "expiredOrExpiringPolicies": "Expired or Expiring Policies", "totalCommissionsByProduct": "Total Commissions by Product", "salesAmountsByProduct": "Sales Amounts by Product", "salesTrendInformationByDate": "Sales Trend Information by Date", "salesVolumesByProduct": "Sales Volumes by Product"}, "offers": {"title": "Offer/Policy", "offersListing": {"title": "Offers Listing", "report": "Appointment Report", "appointmentCategory": "Appointment Category", "product": "Product", "nameSurname": "Name Surname", "offer": "Offer/Policy", "offerPolicy": "Offer/Policy", "insurancePeriod": "Insurance Period", "grossPremium": "Gross Premium", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "validityDate": "Validity Date", "search": "Search", "newOffer": "<PERSON> Offer", "legalInstitutionLimit": "Legal Institution limit", "guarantees": "Guarantees", "isPhysicalDisability": "Is There a Physical Disability?", "permanentDisability": "Permanent Disability", "treatmentCostInc": "Are treatment costs included?", "additionalGuarantees": "Additional Guarantees", "earthquakeFloodVolcanoEruptionLandslide": "Earthquake, Flood, Volcano Eruption, Landslide", "terror": "Terror", "emaaClubServices": "Emaa Club Services", "death": "Death", "writeExplanation": "Write an explanation", "idType": "Id Type", "idNum": "Id Number", "company": "Company", "insuredName": "Insured Name", "insuredSurname": "Insured Surname", "channel": "Channel", "authorizeCode": "Authorize Code", "offerPolicyNum": "Offer/Policy Number", "policyStatus": "Policy Status", "offerPolicyStatus": "Offer/Policy Status", "policyCoverages": "Policy Coverages", "limits": "Limits", "dateDifference": "Date Difference", "issueDate": "Issue Date", "quotation": "Quotation", "policy": "Policy", "transactionExecutor": "Transaction Executor", "transactionDate": "Transaction Date", "quotationDate": "Quotation Date", "quotationPolicyPrinting": "Quotation/Policy Printing", "collectionReceiptPrinting": "Collection Receipt Printing", "informationFormPrinting": "Information Form Printing", "turkishClausePrintingQuotation": "Turkish Clause (Quotation) Printing", "cardHolder": "Card Holder Name", "cardNumber": "Credit Card Number", "cvv": "CVV", "expirationDate": "Expiration Date", "payNow": "Make Payment", "validateCard": "{{field}} is not correct", "validateLength": "{{field}} must be longer than {{length}}", "cvvTooltip": "The CVV/CVC is located on the back of your credit/debit card on the right side of the white signature", "previewConfirm": "I confirm that all the information entered is correct", "addNewMember": "Add New Member", "removeMemberConfirm": "If you remove member from basket, her/his application will be canceled. Do you want to continue?", "paymentDetail": "Payment Details", "paymentSummary": "Payment Summary", "visaFee": "Visa Fee", "visaFeeDescription": "(visa fee is tax free)", "serviceFee": "Service Fee", "total": "Total (inc. tax)", "taxFee": "Tax Fee", "creditCard": "Credit Card", "paymentType": "Payment Type", "installementNumber": "Installments Number", "installment": "Installment", "advancePayment": "Advance", "choseInstallementNumber": "Chose intallements number", "previewScreen": "Preview Screen", "offerValidityPeriod": "Offer validity period has expired.", "agentCode": "Agent Code", "agentName": "Agent Name", "policyOfferSendMail": "Policy/offer send e-mail", "successfulEmail": "pdf has been sent to you via email", "beneficiary": "Beneficiary", "beneficiaryDetail": "Beneficiary Detail", "creditInstitutionType": "Credit Institution Type", "authReason": "Authorization Reason", "yourPaymentSuccessful": "Your policy has been created successfully", "yourPolicyNum": "Your Policy Number", "occupation": "Occupation", "fullName": "Customer Name Surname", "offerStatus": "Offer Status", "addendum": "Addendum", "lastVersion": "last Version", "damageStep": "Damage Step", "delaySurprise": "Delay Surprise Ratio", "registrationDate": "Registration Date", "policyReferenceInformation": "Policy Reference Information", "previousAgencyInformation": "Previous Agency Information", "previousCompanyInformation": "Previous Company Information", "previousDigitCode": "Previous Digit Code", "previousPolicyNumber": "Previous Policy Number", "previousRenewalNumber": "Previous Renewal Number", "mainPolicy": "Main Policy", "addendumPast": "Addendum Past", "addendumNumber": "Addendum Number", "addendumType": "Addendum Type", "addendumDetail": "Addendum Detail", "active": "Active", "cancelled": "Cancelled", "expiryDate": "Expiry Date", "renewalNumber": "Renewal Number", "collectionReceipt": "Collection Receipt"}, "offersCreating": {"title": "Creating an Offer", "branchSelection": "Branch Selection", "personalInformation": "Personal Information", "slotSelection": "Slot Selection", "summary": "Summary", "prepayment": "Prepayment", "appointmentRequest": "Appointment Request", "countryToVisit": "Country to Visit", "applicantsFutureCountry": "Applicant's Future Country", "branch": "Branch", "appointmentCategory": "Appointment Category", "applicationType": "Application Type", "numberOfApplicants": "Number of Applicants", "visaCategory": "Visa Category", "descOk": "Your appointment request has been forwarded to Gateway Users.", "mustBeEmailFormat": "It must be in the email format.", "otherType": "Other", "electricalType": "Electrical", "compulsoryTrafficInsurance": {"title101": "101 - Traffic Insurance", "title102": "102 - Foreign Traffic", "title203": "203 - My Home Is Safe", "title500": "500 - Personal Accident", "title620": "620 - Critical Ilnesses", "title608": "608 - Foreign Health Insurance", "tckn": "TCKN/VKN/YKN", "insuranceTransactions": "Insurance Transactions", "plateNumber": "Plate number", "licenseRegistrationSerialNumber": "License registration serial number", "additionalGuarantee": "Additional Guarantee", "fuelType": "Fuel Type", "usage": "Usage", "modelYear": "Model Year", "brand": "Brand", "model": "Model", "engineNumber": "Engine Number", "chassisNumber": "Chassi<PERSON> number", "getOffer": "Get Offer", "PasaportNoYKNVKNTCKN": "Passport Number / YKN", "insuranceDuration": "Insurance duration", "hasCarPlate": "I have carplate", "placeToTravel": "City To Travel", "vehicleTypes": "Vehicle Types", "openRequest": "Open Request", "doNotOpenRequest": "Don't Open Request", "authCode": "Auth Code", "authMessageTitle": "Auth Message", "auth": "Authorization", "warning": "Warning", "AuthMessageText": "Please Create a Request to forward the situation to the General Directorate!"}, "houseInsurance": {"saveNewCustomer": "Save a new customer", "uavtQuery": "Query UAVT", "uavtInquiry": "UAVT Inquiry", "getRiskAddress": "Get Risk Address", "riskAddress": "Risk Address", "coverageLimit": "Coverage Limit", "buildingStyle": "Building Style", "buildingConstructionYear": "Building Construction Year", "buildingArea": "Building Area (m2)", "buildingUsage": "Building Usage", "enterYearofConstruction": "Enter year of construction", "enterBuildingArea": "Enter Building Area", "enteruavtCode": "Enter UAVT Code", "idleTime": "Idle Time", "enterNumberOfFloors": "Enter Number Of Floors", "numberOfFloorsExcludingBasement": "Number of Floors Excluding Basement", "floorCode": "Floor Code", "titleOfInsured": "Title of Insured", "legalProtectionLimit": "Legal Protection Limit", "selectProvince": "Select Province", "selectDistrict": "Select District", "selectVillage": "Select Village", "selectNeighborhood": "Select Neighborhood", "selectStreet": "Select street/boulevard/square", "selectInteriorDoorNumber": "Please Select Interior Door Number", "selectExteriorDoorNumber": "Please Select Exterior Door Number", "objectCost": "Object Cost", "buildingCost": "Building Cost"}, "foreignHealthInsurance": {"isExistingDisease": "Is there any existing disease?", "IsKvkkApproved": "Is there KVKK approval?"}}}, "requestManagement": {"title": "Request Management", "requestManagementListing": {"title": "Request Management Listing", "unit": "Unit", "requestReason": "Reason for request", "search": "Search", "newRequest": "New Request", "requestsWaitingForMe": "Requests waiting for me", "requesting": "Requesting", "policyType": "Policy Type", "subject": "Subject", "requestDate": "Request Date", "description": "Description", "insuredNameSurname": "Insured Name Surname", "agency": "Agency", "productName": "Product Name", "offerDetails": "Offer Details", "additionalInformation": "Additional Information", "viewOffer": "View Offer", "additionalDocs": "Additional Documents", "typeMessage": "Type Message", "submitMessage": "Submit Message", "requestNumber": "Request Number", "relatedUnit": "Related Unit", "requestId": "Request Id", "fault": "<PERSON><PERSON>", "InsuranceQuote": "Insurance quote", "messaging": "Messaging", "reviewRequest": "Review Request", "messageHistory": "Message History", "sendNewMessage": "Send New Message", "requestProcesses": "Request Processes", "closeRequest": "Close Request", "endRequest": "End Request", "requestWillBeEnded": "Request Will Be Ended!", "requestWillBeEndedPermanently": "The request will be ended permanently and cannot be reopened.", "fileHistory": "File History", "requestWillBeUpdated": "The request will be updated", "requestReview": "Request Review", "requestClosedDate": "Request Closed Date", "agencyCode": "Agency Code", "agencyName": "Agency Name", "user": "User", "gmUser": "<PERSON><PERSON><PERSON><PERSON>", "requestUpdateError": "Unit, Request Reason and Request Status are mandatory fields for updating.", "exportToExcel": "Export to Excel", "general": "General", "admin": "Admin", "messageGroup": "Message Group", "includeClosedOffers": "Include Closed Requests"}, "requestManagementCreating": {"title": "Creating a Request Management", "agentThatOpenRequest": "Request Filing Agency", "requestPerson": "Request Opener", "requestStatus": "Request Status", "offerPolicyNum": "Offer/Policy Number", "product": "Product", "requestReason": "Request Reason", "detail": "Detail", "successCreateRequest": "Your request has been created successfully", "updateStatus": "Update Status", "takeRequestOnMe": "Take Request on Me"}}, "messages": {"title": "Messages", "deliveries": "Deliveries", "sender": "Sender", "type": "Message/Announcement Type", "summaryMessage": "Message Summary", "message": "Message", "announcement": "Announcement", "messageBox": "Message Box", "mainScreen": "Main Screen", "action": {"add": "Add Messages", "edit": "Edit Messages", "message": "Message", "messageChannel": "Message/Announcement Channel", "releaseDate": "Release Date", "messageReleasePlace": "Message/Announcement Release Place", "addPic": "Add Picture", "releaseRemoveDate": "Release Remove Date"}}, "configs": {"title": "Configs", "productManagement": {"title": "Product Management", "productNo": "Product No", "productName": "Product Name", "offerPrint": "Offer Printing", "policyPrint": "Policy Printing", "receiptPrint": "Receipt Printing", "b2b": "B2B", "b2c": "B2C", "releaseDate": "Release Date", "endDate": "End Date", "status": "Status", "publishedProducts": "Published Products", "agencies": "Agencies", "published": "Published", "notPublished": "Not Published", "action": {"title": "Product Management", "add": "Add Product", "edit": "Edit Product", "registerOnProposal": "Proposals do not require registration", "informingPrint": "Information Form", "productViewingAgency": "Product Viewing Agency", "productViewingAgencyUsers": "Product Viewing Agency Users"}}, "unitManagement": {"title": "Unit Management", "unitName": "Unit Name", "unitMail": "Unit E-Mail", "unitTel": "Unit Phone", "isActive": "Unit Passive/Active", "action": {"add": "Add Unit", "edit": "Edit Unit"}}, "agencyManagement": {"title": "Agency Management", "agencyName": "Agency Name", "agencyCode": "Agency Code", "agencySelection": "Agency Selection", "addAgency": "Add Agency", "editAgency": "Edit Agency", "selectedAgencies": "Selected Agencies"}}, "profile": {"title": "My Profile", "profileInformation": {"title": "Your Profile Information", "profilePhoto": "Profile Photo", "upload": "Upload"}, "changePassword": {"title": "Change Your Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmationPassword": "Confirmation Password", "doNotMatchPassword": "The new password that you entered do not match!"}}, "auth": {"loginSignTitle": "<PERSON><PERSON>", "loginSignTitleDesc": "Log in for Insurance Transactions.", "logout": "Logout", "userinformations": "User Informations", "forgotPasswordTitle": "Forgot Password", "forgotPasswordTitleDesc": "Please enter your email address", "activationMail": "Send Activation Email", "doYouHaveAccount": "Do you have an active account that you use?", "changePasswordTitle": "Password Reset", "changePasswordTitleDesc": "An email containing a password reset code has been sent to your email address. Please fill out the following sections to set your new password.", "changePassword": "Change Password"}, "error": {"invalid_username_or_password": "Invalid Username or Password.", "licenseRegistrationSerialNumberValidation": "License registration number must be six digit number", "enterValue": "Please enter a value", "passportNotValid": "Passport is not valid", "enterValidName": "Please enter a valid name", "enterValidSurName": "Please enter a valid surname", "yknNotValid": "ykn is not valid", "reCaptchaFailed": "An error occurred while checking the robot.", "phoneNum10": "Phone number must be at least 10 digits!", "chassisNum17": "chassis number must be at least 17 characters long", "tcknoValid": "tckno is not valid", "creditCardNotNumValid": "Credit Card Number is not valid", "informationFormPrintingFirstToBeDownloaded": "The Offer/Policy and Collection Receipt cannot be downloaded or sent by e-mail without downloading the Information Form.", "filterRuleOfferlisting": "Please specify one more criterion along with the Status field."}}