"use server";

import { getDictionary } from "@/dictionaries";
import Action from "./action";

export async function generateMetadata({ params: { lang }, searchParams }) {
  const dict = await getDictionary(lang);
  const isEdit = Boolean(searchParams?.id);

  return {
    title: isEdit
      ? `${dict.configs.unitManagement.action.edit} | ${dict.configs.unitManagement.title} | ${dict.configs.title} | ${dict.public.title}`
      : `${dict.configs.unitManagement.action.add} | ${dict.configs.unitManagement.title} | ${dict.configs.title} | ${dict.public.title}`,
  };
}

export default async function ActionPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <Action dict={dict} lang={lang} />;
}
