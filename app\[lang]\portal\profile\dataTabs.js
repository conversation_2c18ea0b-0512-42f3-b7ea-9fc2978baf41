"use client";

import { Tabs } from "antd";
import { usePathname } from "next/navigation";
import Link from "next/link";

export default function DataTabs({ dict, lang }) {
  const pathName = usePathname();
  const changePasswordUrl = `/${lang}/portal/profile/change-password/`;

  const items = [
    {
      key: changePasswordUrl,
      label: (
        <Link href={changePasswordUrl}>
          {dict.profile.changePassword.title}
        </Link>
      ),
    },
  ];

  return (
    <Tabs
      className="slf-tabs-card sticky top-16 z-10 shadow-sm"
      defaultActiveKey={pathName}
      destroyOnHidden
      items={items}
    />
  );
}
