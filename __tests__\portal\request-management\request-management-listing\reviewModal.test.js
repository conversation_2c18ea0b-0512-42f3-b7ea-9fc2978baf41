import { render, screen, act } from "@testing-library/react";
import ReviewModal from "@/app/[lang]/portal/request-management/request-management-listing/reviewModal";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";

// #region Mock tanımlamaları

// requestReviewModal'ın içeriği olmadan mocklandı

jest.mock("@/components/containers/requestReviewModal", () => ({
  __esModule: true,
  default: jest.fn(({ selectedData }) => (
    <div data-testid="mock-request-review-modal">
      {selectedData ? selectedData.id : "ID Yok"}
    </div>
  )),
}));

const mockSelectedData = {
  id: 1,
  policyNo: "123456",
  channel: "Test Channel",
  userName: "Test User",
  productNo: "999",
  requestReason: "Test Request Reason",
  subject: "Test Subject",
  requestDate: "2024-03-26",
  detail: "Test Detail",
  status: "Test Status",
  productName: "Test Product",
  unit: "Test Unit",
  agencyName: "Test Agency",
  adminName: "Test Admin",
  closureDate: "2024-03-26",
  closureDateTime: "2024-03-26T15:30:00",
};

describe("ReviewModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language ${lang}`, () => {
      let dict, mockSetIsModalOpen;

      // #region Sayfa render olmadan önceki tanımlamalar

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockSetIsModalOpen = jest.fn();
      });

      //#endregion Sayfa render olmadan önceki tanımlamalar

      // #region Modal bileşenleri ve prop kontrolü
      test("Modal açıkken başlığı ve içeriği render edilmeli", async () => {
        render(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );

        expect(
          screen.getByText(
            dict.requestManagement.requestManagementListing.reviewRequest
          )
        ).toBeInTheDocument();

        const requestReviewModal = screen.getByTestId(
          "mock-request-review-modal"
        );
        expect(requestReviewModal).toBeInTheDocument();

        // selectedData prop kontrolü
        expect(requestReviewModal).toHaveTextContent(
          mockSelectedData.id.toString()
        );

        expect(mockSetIsModalOpen).not.toHaveBeenCalled();
      });
      // #region Modal bileşenleri ve prop kontrolü
    });
  });
});
