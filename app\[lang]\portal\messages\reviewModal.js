"use client";

import { Descriptions, Modal } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { formatDate } from "@/components/tools";

export default function ReviewModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
}) {
  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {dict.requestManagement.requestManagementListing.reviewRequest}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <div className="flex flex-col px-6 pb-1 pt-4">
        <Descriptions
          className="review-desc"
          bordered
          column={{
            xs: 1,
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            xxl: 1,
          }}
          size="small"
        >
          <Descriptions.Item label={dict.messages.sender}>
            {selectedData?.userName}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.type}>
            {selectedData?.type}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.action.releaseDate}>
            {formatDate(`${selectedData?.broadcastDate}+00:00`, lang)}
          </Descriptions.Item>

          <Descriptions.Item label={dict.messages.deliveries}>
            {selectedData?.broadcastChannel}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.summaryMessage}>
            {selectedData?.title}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.action.message}>
            {selectedData?.content}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Modal>
  );
}
