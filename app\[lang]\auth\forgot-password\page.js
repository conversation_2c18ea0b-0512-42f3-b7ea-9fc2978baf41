"use server";

import { getDictionary } from "@/dictionaries";
import ForgotPasswordForm from "./forgotPasswordForm";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.auth.forgotPasswordTitle} | ${dict.public.title}`,
  };
}

export default async function ForgotPassword({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <ForgotPasswordForm dict={dict} lang={lang}></ForgotPasswordForm>;
}
