"use client";

import { <PERSON>, Spin, Input, But<PERSON>, Switch } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getSpinIndicator, useResponseHand<PERSON> } from "@/components/tools";
import { useUser } from "@/components/contexts/userContext";
import { IconArrowSmallLeft } from "@/components/icons";
import {
  getUnitManagementSingle,
  postUnitManagement,
  putUnitManagement,
} from "@/services/unitManagement";

export default function Action({ dict, lang }) {
  const [form] = Form.useForm();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const router = useRouter();
  const id = useSearchParams().get("id");
  const actionStatus = id === null ? "add" : "edit";

  useEffect(() => {
    const startFetching = async () => {
      setFormLoading(true);
      try {
        if (id) await getUnitManagement(id);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, actionStatus]);

  const getUnitManagement = async (id) => {
    setFormLoading(true);
    try {
      let res = await getUnitManagementSingle(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        let data = res?.data;
        data.isActive = toBoolean(data.isActive);
        form.setFieldsValue(data);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const toBoolean = (value) => {
    return value === 1;
  };

  const onFinish = async (values) => {
    values.isActive = values.isActive === true ? 1 : 0;
    if (actionStatus == "edit") {
      updateUnitManagement(values);
    } else {
      values.id = 0;
      addUnitManagement(values);
    }
  };

  const addUnitManagement = async (values) => {
    setFormLoading(true);
    try {
      const res = await postUnitManagement(lang, values);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        router.push(`/${lang}/portal/configs/unit-management/`);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const updateUnitManagement = async (values) => {
    setFormLoading(true);
    try {
      const res = await putUnitManagement(lang, values);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        router.push(`/${lang}/portal/configs/unit-management/`);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      <Form
        form={form}
        layout="vertical"
        className="w-full"
        onFinish={onFinish}
      >
        <div className="rounded-xl bg-white p-6 pb-0 drop-shadow-md">
          <h1 className="mb-4 text-2xl font-medium">
            {actionStatus === "add" ? (
              <span>{dict.configs.unitManagement.action.add}</span>
            ) : (
              <span>{dict.configs.unitManagement.action.edit}</span>
            )}
          </h1>
          <div className="flex flex-wrap gap-x-6">
            <Form.Item name="id" data-testid="id" hidden="true">
              <Input />
            </Form.Item>
            <Form.Item
              name="unitName"
              data-testid="unitName"
              className="!shrink !grow !basis-52"
              label={dict.configs.unitManagement.unitName}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="unitMail"
              data-testid="unitMail"
              className="!shrink !grow !basis-52"
              label={dict.configs.unitManagement.unitMail}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="unitTel"
              data-testid="unitTel"
              className="!shrink !grow !basis-52"
              label={dict.configs.unitManagement.unitTel}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <Input />
            </Form.Item>
          </div>

          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="isActive"
              data-testid="isActive"
              className="!shrink !grow !basis-52"
              label={dict.configs.unitManagement.isActive}
              initialValue={true}
            >
              <Switch />
            </Form.Item>
            <div className="!shrink !grow !basis-52"></div>
            <div className="!shrink !grow !basis-52"></div>
          </div>
        </div>

        <div className="mt-6 flex justify-between">
          <Button
            type="primary"
            onClick={() => {
              router.push(`/${lang}/portal/configs/unit-management/`);
            }}
            className="!flex items-center gap-1 !bg-gray-500 !bg-opacity-75"
            data-testid="backButton"
          >
            <IconArrowSmallLeft className={"h-4 w-4"} />
            {dict.public.back}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            data-testid="saveButton"
          >
            {dict.public.save}
          </Button>
        </div>
      </Form>
    </Spin>
  );
}
