import { render, waitFor, screen } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { useRouter, useSearchParams } from "next/navigation";
import { postConfirmEmail } from "@/services/identityConnect";
import EmailValidation from "@/app/[lang]/auth/validation/emailValidation";

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("@/services/identityConnect", () => ({
  postConfirmEmail: jest.fn(),
}));

describe("EmailValidation", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, emailValidation;
      let mockPush;
      let mockSearchParams;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockPush = jest.fn();
        mockSearchParams = new URLSearchParams();
        useRouter.mockReturnValue({ push: mockPush });
        useSearchParams.mockReturnValue(mockSearchParams);
        emailValidation = render(<EmailValidation dict={dict} lang={lang} />);
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("Sayfa başlık içermeli ve başlangıçta görünmemeli.", () => {
        const { container } = emailValidation;
        const heading = container.querySelector("h1");
        expect(heading).toBeInTheDocument();
        expect(heading).toHaveClass("hidden");
      });

      test("Başlık error durumunda hata mesajını göstermeli.", async () => {
        mockSearchParams.set("userId", "123");
        mockSearchParams.set("code", "abc");
        postConfirmEmail.mockResolvedValue({ status: "ERROR" });
        emailValidation = render(<EmailValidation dict={dict} lang={lang} />);
        await waitFor(() => {
          const heading = screen.getByText(dict.public.error);
          expect(heading).toBeInTheDocument();
        });
      });

      test("Başlık success durumunda başarı mesajını göstermeli.", async () => {
        mockSearchParams.set("userId", "123");
        mockSearchParams.set("code", "abc");
        postConfirmEmail.mockResolvedValue({ status: "SUCCESS" });
        emailValidation = render(<EmailValidation dict={dict} lang={lang} />);
        await waitFor(() => {
          const heading = screen.getByText(dict.public.success);
          expect(heading).toBeInTheDocument();
        });
      });

      test("api:Doğru API çağrısı yapılmalı", async () => {
        mockSearchParams.set("userId", "123");
        mockSearchParams.set("code", "abc");
        emailValidation = render(<EmailValidation dict={dict} lang={lang} />);
        await waitFor(() => {
          expect(postConfirmEmail).toHaveBeenCalledWith({
            userId: "123",
            code: "abc",
          });
        });
      });

      test("route:Login butonu yönlendirmeyi doğru yapmalı.", async () => {
        const loginButton = emailValidation.container.querySelector("button");
        loginButton?.click();
        expect(mockPush).toHaveBeenCalledWith(`/${lang}/auth/login/`);
      });
      // #endregion Sayfa bileşenlerinin kontrolü
    });
  });
});
