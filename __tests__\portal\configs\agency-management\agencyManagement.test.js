import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  postPaginatedAgencyDefinitions,
  deleteAgencyDefinition,
} from "@/services/agencyManagement";
import { getActiveProducts } from "@/services/product";
import { getBroadcastChannel } from "@/services/messages";
import { getDictionary } from "@/dictionaries";
import AgencyManagement from "@/app/[lang]/portal/configs/agency-management/agencyManagement";

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/agencyManagement", () => ({
  postPaginatedAgencyDefinitions: jest.fn(),
  deleteAgencyDefinition: jest.fn(),
}));

jest.mock("@/services/product", () => ({
  getActiveProducts: jest.fn(),
}));

jest.mock("@/services/messages", () => ({
  getBroadcastChannel: jest.fn(),
}));

describe("AgencyManagement", () => {
  Object.keys({ en: "en", tr: "tr" }).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        const originalQuerySelector = document.querySelectorAll;
        document.querySelectorAll = function (selector) {
          if (selector.includes(".ant-select-item-option-selected:not")) {
            return [];
          }
          return originalQuerySelector.call(this, selector);
        };
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        postPaginatedAgencyDefinitions.mockResolvedValue({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 0,
        });
        getActiveProducts.mockResolvedValue({ status: "SUCCESS", data: [] });
        getBroadcastChannel.mockResolvedValue({ status: "SUCCESS", data: [] });

        await act(async () => {
          render(<AgencyManagement dict={dict} lang={lang} />);
        });
      });

      test("form:Components should be present in the filter block", async () => {
        expect(screen.getByTestId("productNo")).toBeInTheDocument();
        expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        expect(screen.getByTestId("AgencyName")).toBeInTheDocument();
        expect(screen.getByTestId("addButton")).toBeInTheDocument();
        expect(screen.getByTestId("resetButton")).toBeInTheDocument();
        expect(screen.getByTestId("filterButton")).toBeInTheDocument();
      });

      test("form:Text fields should be correct", () => {
        const texts = [
          dict.configs.productManagement.productNo,
          dict.configs.agencyManagement.agencyCode,
          dict.configs.agencyManagement.agencyName,
          dict.public.startDate,
          dict.public.endDate,
          dict.public.action,
          dict.public.add,
          dict.public.clean,
          dict.public.filter,
          dict.public.products,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("api:API functions should be called on initial load", async () => {
        await waitFor(() => {
          expect(getActiveProducts).toHaveBeenCalled();
          expect(getBroadcastChannel).toHaveBeenCalled();
          expect(postPaginatedAgencyDefinitions).toHaveBeenCalled();
        });
      });

      test("filter:Filter form submission should fetch filtered agencies", async () => {
        postPaginatedAgencyDefinitions.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 0,
        });
        await act(async () => {
          fireEvent.click(screen.getByTestId("filterButton"));
        });
        await waitFor(() => {
          expect(postPaginatedAgencyDefinitions).toHaveBeenCalledTimes(2);
        });
      });

      test("form:Filter button should send correct form values", async () => {
        const agencyCodeInput = screen
          .getByTestId("agencyCode")
          .querySelector("input");
        await act(async () => {
          fireEvent.change(agencyCodeInput, { target: { value: "TEST_CODE" } });
        });
        await act(async () => {
          fireEvent.click(screen.getByTestId("filterButton"));
        });
        await waitFor(() => {
          expect(postPaginatedAgencyDefinitions).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
              agencyCode: "TEST_CODE",
              pagination: expect.objectContaining({
                pageNumber: 1,
                pageSize: 10,
              }),
            })
          );
        });
      });

      test("table:Table should load data", async () => {
        const data = Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          productNo: `P${index}`,
          channel: `CH${index}`,
          agencyName: `Agency ${index}`,
        }));
        postPaginatedAgencyDefinitions.mockResolvedValueOnce({
          status: "SUCCESS",
          data,
          totalNumberOfRecords: 5,
        });
        await act(async () => {
          fireEvent.click(screen.getByTestId("filterButton"));
        });
        await waitFor(() => {
          expect(postPaginatedAgencyDefinitions).toHaveBeenCalledTimes(2);
          data.forEach((item) => {
            expect(screen.getByText(item.agencyName)).toBeInTheDocument();
          });
        });
      });

      test("form:Reset button should clear form fields", async () => {
        const agencyCodeInput = screen
          .getByTestId("agencyCode")
          .querySelector("input");
        const agencyNameInput = screen
          .getByTestId("AgencyName")
          .querySelector("input");
        await act(async () => {
          fireEvent.change(agencyCodeInput, { target: { value: "TEST_CODE" } });
          fireEvent.change(agencyNameInput, {
            target: { value: "Test Agency" },
          });
        });
      });

      test("modal:Add button should open modal", async () => {
        await act(async () => {
          fireEvent.click(screen.getByTestId("addButton"));
        });
        expect(screen.getByText(dict.public.add)).toBeInTheDocument();
      });

      // test("delete:Delete action should call delete API", async () => {
      //   const data = [
      //     { id: 1, productNo: "P1", channel: "CH1", agencyName: "Agency 1" },
      //   ];
      //   postPaginatedAgencyDefinitions.mockResolvedValueOnce({
      //     status: "SUCCESS",
      //     data,
      //     totalNumberOfRecords: 1,
      //   });
      //   deleteAgencyDefinition.mockResolvedValue({ status: "SUCCESS" });
      //   await act(async () => {
      //     render(<AgencyManagement dict={dict} lang={lang} />);
      //   });
      //   await waitFor(() => {
      //     expect(postPaginatedAgencyDefinitions).toHaveBeenCalled();
      //   });
      //   //const deleteButton = screen.getByText(dict.public.delete);
      //   //const deleteButton = screen.getByTestId("deleteIcon");
      //   await act(async () => {
      //     //fireEvent.click(deleteButton);
      //     fireEvent.click(screen.getByTestId("confirmYesButton"));
      //   });
      //   await waitFor(() => {
      //     expect(deleteAgencyDefinition).toHaveBeenCalledWith(lang, 1);
      //     expect(postPaginatedAgencyDefinitions).toHaveBeenCalledTimes(2);
      //   });
      // });
    });
  });
});
