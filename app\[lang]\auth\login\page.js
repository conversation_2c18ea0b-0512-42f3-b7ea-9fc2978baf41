"use server";

import { getDictionary } from "@/dictionaries";
import LoginForm from "./loginForm";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.auth.loginSignTitle} | ${dict.public.title}`,
  };
}

export default async function Login({ params: { lang }, searchParams }) {
  const dict = await getDictionary(lang);

  return (
    <LoginForm
      dict={dict}
      lang={lang}
      response={false}
      searchParams={searchParams}
    ></LoginForm>
  );
}
