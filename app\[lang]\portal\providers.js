"use client";

import { ConfigProvider } from "antd";
import { createCache, extractStyle, StyleProvider } from "@ant-design/cssinjs";
import { useMemo, useRef } from "react";
import { usePathname, useServerInsertedHTML } from "next/navigation";
import { AppProgressBar as ProgressBar } from "next-nprogress-bar";
import { getLocale } from "@/components/tools";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";

export function ThemeProviders({ children, fontFamily, lang }) {
  const cache = useMemo(() => createCache(), []);
  const isServerInserted = useRef(false);
  const pathname = usePathname();
  const language = pathname.split("/")[1];
  useServerInsertedHTML(() => {
    if (isServerInserted.current) {
      return;
    }
    isServerInserted.current = true;
    return (
      <style
        id="portal"
        dangerouslySetInnerHTML={{ __html: extractStyle(cache, true) }}
      />
    );
  });

  return (
    <StyleProvider hashPriority="high" cache={cache}>
      <ConfigProvider
        locale={getLocale[lang]}
        theme={{
          token: {
            fontFamily: fontFamily,
            colorPrimary: "#84754e",
            controlHeight: 40,
            borderRadius: 8,
            colorBgBase: "#F9FAFB",
            colorBorder: "#D1D5DB",
            colorBgContainerDisabled: "#00000017",
          },
          components: {
            Button: {
              fontWeight: 600,
              lineHeight: 1.25,
              controlOutlineWidth: 0,
              controlOutline: 0,
            },
            Input: {
              controlOutlineWidth: 0,
              controlOutline: 0,
            },
            Select: {
              controlOutlineWidth: 0,
              controlOutline: 0,
            },
            Calendar: {
              fullBg: "#ffffff",
            },
            Tooltip: {
              lineHeight: "28px",
            },
          },
        }}
      >
        <GoogleReCaptchaProvider
          language={language}
          reCaptchaKey={process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHAV3_SITE_KEY}
        >
          {children}
        </GoogleReCaptchaProvider>
        <ProgressBar
          height="2px"
          color="#84754e"
          options={{ showSpinner: false }}
          shallowRouting
        />
      </ConfigProvider>
    </StyleProvider>
  );
}
