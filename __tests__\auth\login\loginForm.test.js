import { fireEvent, render, waitFor } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { postConnectToken } from "@/services/identityConnect";
import LoginForm from "@/app/[lang]/auth/login/loginForm";

jest.mock("react-google-recaptcha-v3", () => ({
  useGoogleReCaptcha: () => ({
    executeRecaptcha: jest.fn(() => Promise.resolve("test-token")),
  }),
}));

jest.mock("@/services/identityConnect", () => ({
  postConnectToken: jest.fn(),
}));

describe("LoginForm", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, loginForm;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        loginForm = render(
          <LoginForm
            dict={dict}
            lang={lang}
            response={false}
            onResponse={() => {}}
            searchParams={{}}
          />
        );
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("Sayfa başlık içermeli.", () => {
        const { container } = loginForm;
        const heading = container.querySelector("h1");
        expect(heading).toBeInTheDocument();
        expect(heading.textContent).toBe(dict.auth.loginSignTitle);
      });

      test("input:username olmalı ve placeholder içermeli.", () => {
        const { getByPlaceholderText } = loginForm;
        const username = getByPlaceholderText(dict.public.username);
        expect(username).toBeInTheDocument();
      });

      test("input:password olmalı ve placeholder içermeli.", () => {
        const { getByPlaceholderText } = loginForm;
        const password = getByPlaceholderText(dict.public.password);
        expect(password).toBeInTheDocument();
      });

      test(`button:submit olmalı ve başlık içermeli. ${lang}`, () => {
        const { getByTestId } = loginForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        expect(buttonSubmit).toBeInTheDocument();
        expect(buttonSubmit.textContent).toBe(dict.public.login);
      });

      test(`link:forgotPassword olmalı, doğru url ve başlık içermeli.`, () => {
        const { getByTestId } = loginForm;
        const linkForgotPassword = getByTestId("linkForgotPassword");
        expect(linkForgotPassword).toBeInTheDocument();
        expect(linkForgotPassword).toHaveAttribute(
          "href",
          `/${lang}/auth/forgot-password`
        );
        expect(linkForgotPassword.textContent).toBe(
          dict.auth.forgotPasswordTitle
        );
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region Form validasyon kontrolü
      test(`form:username alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = loginForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const username = getByTestId("username");
          expect(username).toBeInTheDocument();
          expect(username.textContent).toBe(dict.public.requiredField);
        });
      });

      test(`form:password alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = loginForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const password = getByTestId("password");
          expect(password).toBeInTheDocument();
          expect(password.textContent).toBe(dict.public.passwordPattern);
        });
      });

      test("form:Başarılı giriş sonrası yönlendirme yapılmalı.", async () => {
        const fakeResponse = {
          access_token:
            "header." +
            btoa(JSON.stringify({ name: "User", email: "<EMAIL>" })) +
            ".signature",
        };
        postConnectToken.mockResolvedValueOnce(fakeResponse);
        const { getByTestId, getByPlaceholderText } = loginForm;
        fireEvent.change(getByPlaceholderText(dict.public.username), {
          target: { value: "<EMAIL>" },
        });
        fireEvent.change(getByPlaceholderText(dict.public.password), {
          target: { value: "Password123*" },
        });
        fireEvent.click(getByTestId("buttonSubmit"));
        await waitFor(() => {
          expect(postConnectToken).toHaveBeenCalled();
        });
      });

      test("form:Hatalı login sonrası hata mesajı görünmeli.", async () => {
        postConnectToken.mockResolvedValueOnce({
          error: true,
          error_description: "invalid_grant",
        });
        const { getByTestId, getByPlaceholderText } = loginForm;
        fireEvent.change(getByPlaceholderText(dict.public.username), {
          target: { value: "<EMAIL>" },
        });
        fireEvent.change(getByPlaceholderText(dict.public.password), {
          target: { value: "Password123*" },
        });
        fireEvent.click(getByTestId("buttonSubmit"));
        await waitFor(() => {
          expect(postConnectToken).toHaveBeenCalled();
        });
      });
      // #endregion Form validasyon kontrolü
    });
  });
});
