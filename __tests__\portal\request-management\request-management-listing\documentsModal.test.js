import {
  render,
  screen,
  act,
  waitFor,
  fireEvent,
} from "@testing-library/react";
import { getLocale, formatDate } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import { getRequestFiles, getRequestDownloadFile } from "@/services/requests";
import { useUser } from "@/components/contexts/userContext";
import DocumentsModal from "@/app/[lang]/portal/request-management/request-management-listing/documentsModal";

// requestReviewModal'ın içeriği olmadan mocklandı

jest.mock("@/components/containers/requestReviewModal", () => ({
  __esModule: true,
  default: jest.fn(({ selectedData }) => (
    <div data-testid="mock-request-review-modal">
      {selectedData ? selectedData.id : "ID Yok"}
    </div>
  )),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/components/tools", () => {
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();
  const mockFormatDate = jest.fn(() => "26/09/2024, 14:16:21");
  const mockGetSpinIndicator = jest.fn();
  const mockHandleDownloadFile = jest.fn();

  jest.mock("@/services/requests", () => ({
    getRequestFiles: jest.fn(),
    getRequestDownloadFile: jest.fn(),
  }));

  return {
    useResponseHandler: () => ({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    }),
    formatDate: mockFormatDate,
    getLocale: { en: "en", tr: "tr" },
    __mocks: {
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
      formatDate: mockFormatDate,
      getSpinIndicator: mockGetSpinIndicator,
      handleDownloadFile: mockHandleDownloadFile,
    },
  };
});

Object.defineProperty(window, "location", {
  value: { reload: jest.fn() },
  writable: true,
});

// #region Mock Data

const mockSelectedData = {
  id: 103,
  policyNo: null,
  channel: "1",
  userName: "GW DIJITAL",
  productNo: "101",
  requestReason: "Acenteler-Acenteler servisi talepleri",
  subject: "DENEME",
  requestDate: "2025-03-27T14:14:38",
  detail: "EDEDEDEDE",
  status: "COZUM_BEKLENIYOR",
  productName: "TRAFİK SİGORTASI",
  unit: "Acenteler Servisi",
  agencyName: "EMAA SİGORTA ANONİM ŞİRKETİ",
  adminName: null,
  closureDate: null,
  closureDateTime: null,
};

const mockRequestFiles = [
  {
    id: 75,
    requestId: 72,
    fileName: "trilogy (1).png",
    fileExtension: ".png",
    messageId: 0,
    isEmaa: true,
    createdAt: "26/09/2024, 14:16:21",
    createdBy: "EREN FILIZ",
    updatedAt: null,
    updatedBy: null,
    deletedAt: null,
    deletedBy: null,
    isDeleted: false,
  },
];

// #endregion mock data

describe("DocumentsModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, setIsModalOpen, mockHandleError;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });

        setIsModalOpen = jest.fn();
        const tools = require("@/components/tools");
        mockHandleError = tools.__mocks.handleError;
        tools.__mocks.handleResponse.mockClear();
        tools.__mocks.handleError.mockClear();
        tools.__mocks.formatDate.mockClear();
        window.location.reload.mockClear();

        getRequestFiles.mockResolvedValue({
          status: "SUCCESS",
          data: mockRequestFiles,
        });

        getRequestDownloadFile.mockResolvedValue({
          status: "SUCCESS",
          data: [],
        });

        await act(async () => {
          render(
            <DocumentsModal
              dict={dict}
              lang={lang}
              isModalOpen={true}
              setIsModalOpen={setIsModalOpen}
              selectedData={mockSelectedData}
            />
          );
        });
      });

      // #region Modal kontrolü

      test("modal:Başlığı ve içeriği doğru şekilde render edilmeli.", () => {
        expect(screen.getByText(dict.public.files)).toBeInTheDocument();

        const requestReviewModal = screen.getByTestId(
          "mock-request-review-modal"
        );
        expect(requestReviewModal).toBeInTheDocument();

        expect(requestReviewModal).toHaveTextContent(
          mockSelectedData.id.toString()
        );
      });

      test("Modal:Kapama butonu çalışmalı.", () => {
        fireEvent.click(screen.getByRole("img", { name: "close" }));
        expect(setIsModalOpen).toHaveBeenCalledWith(false);
      });

      // #endregion Modal kontrolü

      // #region Documents kontrolü

      test("Documents: Dosya adına tıklanıldığında dosya indirme fonksiyonu", async () => {
        const mockFileLink = {
          requestId: mockSelectedData.id,
          fileId: mockRequestFiles[0].id,
        };

        const downloadLink = screen.getByText("trilogy (1).png");
        await act(async () => {
          fireEvent.click(downloadLink);
        });

        await waitFor(() =>
          expect(getRequestDownloadFile).toHaveBeenCalledWith(
            lang,
            mockFileLink.requestId, // requestId
            mockFileLink.fileId // fileId
          )
        );
      });

      test("Documents: Veriler doğru yüklenmeli", async () => {
        await waitFor(() => expect(getRequestFiles).toHaveBeenCalled());

        for (const doc of mockRequestFiles) {
          await waitFor(() =>
            expect(screen.getByText(doc.fileName)).toBeInTheDocument()
          );
          await waitFor(() =>
            expect(screen.getByText(doc.createdBy)).toBeInTheDocument()
          );
          await waitFor(() =>
            expect(screen.getByText(doc.createdAt)).toBeInTheDocument()
          );
        }

        const emaaImage = screen.getByAltText("EmaaPicture");
        expect(emaaImage).toBeInTheDocument();
      });

      // #endregion Documents kontrolü
    });
  });
});
