import { render } from "@testing-library/react";
import * as Icons from "@/components/icons";

describe("Icon Components", () => {
  Object.entries(Icons).forEach(([name, Component]) => {
    if (typeof Component === "function") {
      test(`${name} başarıyla render edilmeli`, () => {
        const { getByTestId } = render(<Component className="test-class" />);
        const icon = getByTestId(name);
        expect(icon).toBeInTheDocument();
        expect(icon).toHaveClass("test-class");
      });
    }
  });
});
