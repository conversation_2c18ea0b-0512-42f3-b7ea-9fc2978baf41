"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const postSendMessage = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/admin-message-announcement/send-announcement-message`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: values,
    }
  );

  res = await validResponse(res);
  return JSON.parse(res);
};

const getBroadcastChannel = async (lang) => {
  let res = await fetch(
    `${process.env.API_URL}offers/admin-message-announcement/get-broadcast-channel`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );

  res = await validResponse(res);
  return JSON.parse(res);
};

const getMessageAnnouncement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/admin-message-announcement/get-message-announcement`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );

  res = await validResponse(res);
  return JSON.parse(res);
};

const deleteMessageOrAnnouncement = async (lang, id) => {
  let res = await fetch(`${process.env.API_URL}offers/admin-message-announcement/${id}/message-announcement-delete`, {
    cache: "no-store",
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

export { postSendMessage, getBroadcastChannel, getMessageAnnouncement, deleteMessageOrAnnouncement };
