"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const postSendMessage = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/admin-message-announcement/send-announcement-message`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: values,
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postSendMessage", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getBroadcastChannel = async (
  lang,
  allChannel = true,
  productNo = "''"
) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/admin-message-announcement/${allChannel}/get-broadcast-channel/${productNo ? productNo : encodeURIComponent(productNo)}`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getBroadcastChannel", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getMessageAnnouncement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/admin-message-announcement/get-message-announcement`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getMessageAnnouncement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const deleteMessageOrAnnouncement = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/admin-message-announcement/${id}/message-announcement-delete`,
    options: {
      cache: "no-store",
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "deleteMessageOrAnnouncement",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

export {
  postSendMessage,
  getBroadcastChannel,
  getMessageAnnouncement,
  deleteMessageOrAnnouncement,
};
