"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Spin, Checkbox, Switch } from "antd";
import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import { useResponseHandler, getSpinIndicator } from "@/components/tools";
import { useState, useEffect } from "react";

import {
  postAddUpdateCrossProductSettings,
  getCrossProductSettings,
} from "@/services/productManagement";

export default function CrossProductSettingsModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
  products,
}) {
  const { handleResponse, handleError } = useResponseHandler();
  const [formLoading, setFormLoading] = useState(false);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [productsList, setProductsList] = useState([]);
  const [switchStatus, setSwitchStatus] = useState(false);
  useEffect(() => {
    const newProducts = products.filter(
      (item) => item.key !== selectedData?.productNo
    );
    setProductsList(newProducts);
    getCrossProductFunc(selectedData?.productNo);
  }, [selectedData]);
  const addUpdateCrossProductFunc = async (values) => {
    try {
      setFormLoading(true);
      const res = await postAddUpdateCrossProductSettings(lang, values);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setIsModalOpen(false);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const getCrossProductFunc = async (id) => {
    try {
      setFormLoading(true);
      const res = await getCrossProductSettings(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setSwitchStatus(res?.data?.length > 0 ?? false);
        form.setFieldsValue({
          crossSale: res?.data?.length > 0 ?? false,
        });
        if (res?.data?.length > 0) {
          res.data.forEach((item) => {
            form.setFieldsValue({
              [item]: true,
            });
          });
        }
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const onFinish = async (values) => {
    delete values.crossSale;
    const trueKeys = Object.keys(values).filter((key) => values[key] === true);
    const formData = {
      products: trueKeys,
      productNo: selectedData?.productNo,
    };
    delete formData.crossSale;
    await addUpdateCrossProductFunc(formData);
  };

  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {selectedData?.productNo} - {selectedData?.productName}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl p-6">
            <div className="flex flex-wrap items-center gap-x-6">
              <h1 className="text-xl font-bold">
                {dict.configs.productManagement.setupCrossSelling}
              </h1>

              <Form.Item
                name="crossSale"
                data-testid="crossSale"
                className="!shrink !grow !basis-52"
                layout="horizontal"
              >
                <Switch
                  className="!mt-5"
                  onChange={(checked) => {
                    setSwitchStatus(checked);
                    if (!checked) {
                      form.resetFields();
                    }
                  }}
                />
              </Form.Item>
            </div>
            <h1 className="text-xl font-bold">
              {dict.configs.productManagement.productsToCrossSell}
            </h1>
            <div className="mt-5 flex flex-wrap gap-x-2">
              {productsList.map((item) => (
                <Form.Item
                  name={item.key}
                  data-testid={item.key}
                  className="flex !shrink !grow"
                  label={item.key + "-" + item.value}
                  key={item.key}
                  valuePropName="checked"
                  layout="horizontal"
                  // labelAlign="right"
                  colon={false}
                  labelCol={{ style: { order: 2, marginLeft: "10px" } }}
                >
                  <Checkbox disabled={!switchStatus} />
                </Form.Item>
              ))}
            </div>

            <div className="flex justify-end gap-x-6">
              <Button
                data-testid="saveButton"
                type="primary"
                htmlType="submit"
                onClick={() => {}}
                icon={
                  <SaveOutlined
                    className={"inline-block h-5 w-5 align-text-top"}
                  />
                }
              >
                {dict.public.save}
              </Button>
            </div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
}
