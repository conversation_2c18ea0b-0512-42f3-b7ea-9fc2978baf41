NODE_TLS_REJECT_UNAUTHORIZED=0

# DigitalInsurancePortal.PrivateApi
API_URL=#{API_URL}#

# Identity Server
IDENTITY_API_URL=#{IS_API_URL}#
IDENTITY_CLIENT_ID=#{CLIENT_ID}#
IDENTITY_CLIENT_SECRET=#{CLIENT_SECRET}#
IDENTITY_APPLICATION_RESOURCE_ID=#{APP_RESOURCE_ID}#
IDENTITY_GRANT_TYPE="password"
IDENTITY_SCOPE="openid offline_access email roles IdentityServerApi"

# Others
NEXT_PUBLIC_USER_TYPE="PORTAL"
APP_ENV=#{APP_ENV}#

# ReCaptcha
NEXT_PUBLIC_GOOGLE_RECAPTCHAV3_SITE_KEY=#{CAPTCHA_KEY}#
GOOGLE_RECAPTCHAV3_SECRET=#{CAPTCHA_SECRET}#