import { render } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import DataTabs from "@/app/[lang]/portal/profile/dataTabs";

jest.mock("next/navigation", () => ({
  usePathname: jest.fn(),
}));

describe("DataTabs", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, dataTabs;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        dataTabs = render(<DataTabs dict={dict} lang={lang} />);
      });

      test("Sekmeler doğru başlıkları içermeli", () => {
        const { getByText } = dataTabs;
        expect(
          getByText(dict.profile.changePassword.title)
        ).toBeInTheDocument();
      });

      test("link: <PERSON><PERSON>re Değiştirme Sekmesi doğru URL'i içermeli", () => {
        const { getByText } = dataTabs;
        const changePasswordTab = getByText(
          dict.profile.changePassword.title
        ).closest("a");
        expect(changePasswordTab).toHaveAttribute(
          "href",
          `/${lang}/portal/profile/change-password`
        );
      });

      test("tabs: Aktif sekme doğru olmalı", () => {
        const { container } = dataTabs;
        const activeTab = container.querySelector(".ant-tabs-tab-active");
        expect(activeTab).toHaveTextContent(dict.profile.changePassword.title);
      });
    });
  });
});
