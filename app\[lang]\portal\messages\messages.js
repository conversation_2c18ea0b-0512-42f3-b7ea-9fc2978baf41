"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  formatDate,
  getSpinIndicator,
  useResponseHandler,
} from "@/components/tools";
import {
  Button,
  Form,
  Select,
  Spin,
  Table,
  Tooltip,
  DatePicker,
  Input,
} from "antd";
import { useEffect, useState } from "react";
import { IconPlus } from "@/components/icons";
import {
  ClearOutlined,
  FilterOutlined,
  EyeOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { getDateFormat } from "@/components/tools";
import { useRouter } from "next/navigation";
import {
  getBroadcastChannel,
  getMessageAnnouncement,
} from "@/services/messages";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import ReviewModal from "./reviewModal";
import DeleteModal from "./deleteModal";

export default function Messages({ dict, lang }) {
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const [messageList, setMessageList] = useState([]);
  const [form] = Form.useForm();
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const router = useRouter();
  const [broadcastChannel, setBroadcastChannel] = useState([]);
  const [showReviewModal, setShowReviewModel] = useState(false);
  const [showDeleteModal, setShowDeleteModel] = useState(false);
  const [selectedData, setSelectedData] = useState();
  dayjs.extend(utc);
  const defaultModel = {
    date: null,
    broadcastChannel: null,
    messageUser: null,
    type: null,
    title: null,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "id",
      ascending: false,
    },
  };
  const [requestBody, setRequestBody] = useState(defaultModel);

  const types = [
    { key: 1, value: dict.messages.message },
    { key: 2, value: dict.messages.announcement },
  ];

  useEffect(() => {
    const startFetching = async () => {
      try {
        setFormLoading(true);
        const res = await getBroadcastChannel(lang);
        setBroadcastChannel(res?.data);
        getMessageList(requestBody);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getMessageList = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await getMessageAnnouncement(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setMessageList(res?.data);
        } else {
          setMessageList([]);
        }

        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setMessageList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setMessageList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };

  // #region table için kodlar
  const columns = [
    {
      title: `${dict.public.date}`,
      key: "broadcastDate",
      render: (value) => (
        <>{formatDate(`${value.broadcastDate}+00:00`, lang, true)}</>
      ),
    },
    {
      title: `${dict.messages.summaryMessage}`,
      key: "title",
      dataIndex: "title",
    },
    {
      title: `${dict.messages.type}`,
      key: "type",
      dataIndex: "type",
    },
    {
      title: `${dict.messages.deliveries}`,
      key: "broadcastChannel",
      dataIndex: "broadcastChannel",
    },
    {
      title: `${dict.messages.sender}`,
      key: "userName",
      dataIndex: "userName",
    },
    {
      title: `${dict.public.action}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: 100,
      render: (value) => (
        <div className="flex flex-wrap justify-center gap-1">
          <Tooltip placement="top" title={dict.public.review}>
            <Button
              onClick={() => {
                setSelectedData(value);
                setShowReviewModel(true);
              }}
              size="small"
              type="text"
              className="!flex items-center justify-center !border-none !px-1.5"
              icon={<EyeOutlined className="!text-lg !text-blue-500" />}
              data-testid="reviewButton"
            />
          </Tooltip>
          <Tooltip placement="top" title={dict.public.delete}>
            <Button
              onClick={() => {
                setSelectedData(value);
                setShowDeleteModel(true);
              }}
              size="small"
              type="text"
              className="!flex items-center justify-center !border-none !px-1.5"
              icon={<DeleteOutlined className="!text-lg !text-red-500" />}
              data-testid="deleteButton"
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  // #endregion table için kodlar

  // #region filtre kodlar
  const onFinish = async (values) => {
    values.date = dayjs
      .utc(
        dayjs(values?.date).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();
    const updatedBody = {
      ...requestBody,
      ...values,
    };
    getMessageList(updatedBody);
  };

  const handleReset = () => {
    form.resetFields();
    getMessageList(defaultModel);
  };
  // #endregion filtre ve rapor için kodlar

  return (
    <>
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        {showReviewModal && (
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={showReviewModal}
            setIsModalOpen={setShowReviewModel}
            selectedData={selectedData}
          />
        )}
        {showDeleteModal && (
          <DeleteModal
            dict={dict}
            lang={lang}
            isModalOpen={showDeleteModal}
            setIsModalOpen={setShowDeleteModel}
            selectedData={selectedData}
          />
        )}
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl bg-white p-6 drop-shadow-md">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="date"
                data-testid="date"
                className="!shrink !grow !basis-52"
                label={dict.public.date}
              >
                <DatePicker className="w-full" format={getDateFormat[lang]} />
              </Form.Item>
              <Form.Item
                name="broadcastChannel"
                data-testid="broadcastChannel"
                className="!shrink !grow !basis-52"
                label={
                  <span className="!line-clamp-1 !overflow-hidden">
                    {dict.messages.action.messageChannel}
                  </span>
                }
              >
                <Select
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={broadcastChannel?.map((channel) => {
                    return {
                      value: channel.key,
                      label: channel.value,
                    };
                  })}
                />
              </Form.Item>
              <Form.Item
                name="messageUser"
                data-testid="messageUser"
                className="!shrink !grow !basis-52"
                label={dict.messages.sender}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="type"
                data-testid="type"
                className="!shrink !grow !basis-52"
                label={dict.messages.type}
              >
                <Select
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={types?.map((channel) => {
                    return {
                      value: channel.key,
                      label: channel.value,
                    };
                  })}
                />
              </Form.Item>
            </div>
          </div>
          <div className="mt-5 flex flex-wrap justify-end gap-2">
            <Button
              type="primary"
              onClick={() => {
                router.push(`/${lang}/portal/messages/action/`);
              }}
              data-testid="addButton"
            >
              <IconPlus className={"inline-block h-5 w-5 align-text-top"} />
              <span className="ml-1">{dict.public.add}</span>
            </Button>
            <div className="flex flex-1 flex-wrap justify-end gap-2">
              <Button
                type="primary"
                onClick={() => {
                  handleReset();
                }}
                className="!bg-gray-500 !bg-opacity-75"
                icon={<ClearOutlined />}
                data-testid="resetButton"
              >
                {dict.public.clean}
              </Button>
              <Button
                type="primary"
                className="!bg-dark-gray"
                htmlType="submit"
                icon={<FilterOutlined />}
                data-testid="filterButton"
              >
                {dict.public.filter}
              </Button>
            </div>
          </div>
        </Form>
        <div className="mt-5">
          <Table
            className="grid-table"
            columns={columns}
            dataSource={messageList}
            rowKey={"id"}
            scroll={{
              x: 1000,
            }}
            pagination={{
              current: currentPageNumber,
              total: totalRecords,
              defaultPageSize: 10,
              showSizeChanger: false,
              responsive: true,
              onChange: (pageNumber, pageSize) => {
                const updatedBody = {
                  ...requestBody,
                  pagination: {
                    pageNumber: pageNumber,
                    pageSize: pageSize,
                    orderBy: "id",
                    ascending: false,
                  },
                };
                setCurrentPageNumber(pageNumber);
                getMessageList(updatedBody);
              },
            }}
          />
        </div>
      </Spin>
    </>
  );
}
