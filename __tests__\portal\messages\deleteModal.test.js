import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { deleteMessageOrAnnouncement } from "@/services/messages";
import { getLocale } from "@/components/tools";
import DeleteModal from "@/app/[lang]/portal/messages/deleteModal";

// Mock tanımlamaları
jest.mock("@/services/messages", () => ({
  deleteMessageOrAnnouncement: jest.fn(),
}));

jest.mock("@/components/tools", () => {
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();
  const mockFormatDate = jest.fn();

  return {
    useResponseHandler: () => ({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    }),
    formatDate: mockFormatDate,
    getLocale: { en: "en", tr: "tr" },
    __mocks: {
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
      formatDate: mockFormatDate,
    },
  };
});

Object.defineProperty(window, "location", {
  value: { reload: jest.fn() },
  writable: true,
});

const defaultProps = (dict, lang, setIsModalOpen, selectedData) => ({
  dict,
  lang,
  isModalOpen: true,
  setIsModalOpen,
  selectedData,
});

const mockSelectedData = {
  id: 1,
  userName: "Test User",
  type: "Test Type",
  broadcastDate: "2024-03-20",
  broadcastChannel: "Test Channel",
};

describe("DeleteModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, setIsModalOpen, mockHandleError;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        setIsModalOpen = jest.fn();
        const tools = require("@/components/tools");
        mockHandleError = tools.__mocks.handleError;
        tools.__mocks.handleResponse.mockClear();
        tools.__mocks.handleError.mockClear();
        tools.__mocks.formatDate.mockClear();
        window.location.reload.mockClear();
      });

      const renderModal = () =>
        render(
          <DeleteModal
            {...defaultProps(dict, lang, setIsModalOpen, mockSelectedData)}
          />
        );

      describe("Modal Bileşenleri", () => {
        test("modal:Başlığı ve içeriği doğru şekilde render edilmeli.", () => {
          renderModal();

          expect(screen.getByText(dict.public.sureDelete)).toBeInTheDocument();
          expect(
            screen.getByText(mockSelectedData.userName)
          ).toBeInTheDocument();
          expect(screen.getByText(mockSelectedData.type)).toBeInTheDocument();
          expect(
            screen.getByText(mockSelectedData.broadcastChannel)
          ).toBeInTheDocument();
          expect(screen.getByText(dict.public.delete)).toBeInTheDocument();
        });

        test("modal:Açık/kapalı durumu doğru şekilde kontrol edilmeli.", () => {
          const { rerender } = renderModal();
          expect(screen.getByRole("dialog")).toBeInTheDocument();

          rerender(
            <DeleteModal
              {...defaultProps(dict, lang, setIsModalOpen, mockSelectedData)}
              isModalOpen={false}
            />
          );
          expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
        });
      });

      describe("Kullanıcı Etkileşimleri", () => {
        test("Modal:Kapama butonu çalışmalı.", () => {
          renderModal();
          fireEvent.click(screen.getByRole("img", { name: "close" }));
          expect(setIsModalOpen).toHaveBeenCalledWith(false);
        });

        test("Modal:Silme butonu tıklandığında API çağrısı yapılmalı.", async () => {
          deleteMessageOrAnnouncement.mockResolvedValueOnce({
            status: "SUCCESS",
          });
          renderModal();

          fireEvent.click(screen.getByText(dict.public.delete));

          await waitFor(() => {
            expect(deleteMessageOrAnnouncement).toHaveBeenCalledWith(
              lang,
              mockSelectedData.id
            );
          });
        });
      });

      describe("API Yanıtları", () => {
        test("Modal:Silme işlemi başarılı olduğunda sayfa yenilenmeli ve modal kapanmalı.", async () => {
          deleteMessageOrAnnouncement.mockResolvedValueOnce({
            status: "SUCCESS",
          });
          renderModal();

          fireEvent.click(screen.getByText(dict.public.delete));

          await waitFor(() => {
            expect(window.location.reload).toHaveBeenCalled();
            expect(setIsModalOpen).toHaveBeenCalledWith(false);
          });
        });

        test("Modal:Silme işlemi başarısız olduğunda hata mesajı gösterilmeli.", async () => {
          const error = new Error("Test error");
          deleteMessageOrAnnouncement.mockRejectedValueOnce(error);
          renderModal();

          fireEvent.click(screen.getByText(dict.public.delete));

          await waitFor(() => {
            expect(mockHandleError).toHaveBeenCalledWith(
              error,
              dict.public.error
            );
          });
        });
      });
    });
  });
});
