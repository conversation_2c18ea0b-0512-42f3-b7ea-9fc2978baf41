"use client";

import { Form, Input, Button } from "antd";
import { useState } from "react";
import { getPasswordPattern, useResponse<PERSON>and<PERSON> } from "@/components/tools";
import { postUsersChangePassword } from "@/services/identityConnect";
import { SaveOutlined } from "@ant-design/icons";
import { useUser } from "@/components/contexts/userContext";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
export default function ChangePassword({ dict, lang }) {
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [form] = Form.useForm();
  const { executeRecaptcha } = useGoogleReCaptcha();
  const onFinish = async (values) => {
    try {
      setButtonLoading(true);
      values.email = user?.email;
      values.recaptchaToken = await executeRecaptcha("post");
      const res = await postUsersChangePassword(values, lang);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        form.resetFields();
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setButtonLoading(false);
    }
  };

  return (
    <>
      <Form
        form={form}
        onFinish={onFinish}
        layout="vertical"
        className="w-full"
      >
        <div className="rounded-xl bg-white p-6 drop-shadow-md">
          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="currentPassword"
              data-testid="currentPassword"
              className="!shrink !grow !basis-52"
              label={dict.profile.changePassword.currentPassword}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <Input.Password autoFocus />
            </Form.Item>
            <Form.Item
              name="newPassword"
              data-testid="newPassword"
              className="!shrink !grow !basis-52"
              label={dict.profile.changePassword.newPassword}
              rules={[
                {
                  required: true,
                  pattern: getPasswordPattern,
                  message: `${dict.public.passwordPattern}`,
                },
              ]}
            >
              <Input.Password />
            </Form.Item>
            <Form.Item
              name="confirmationPassword"
              data-testid="confirmationPassword"
              className="!shrink !grow !basis-52"
              label={dict.profile.changePassword.confirmationPassword}
              rules={[
                {
                  required: true,
                  pattern: getPasswordPattern,
                  message: `${dict.public.passwordPattern}`,
                },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("newPassword") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(
                      new Error(dict.profile.changePassword.doNotMatchPassword)
                    );
                  },
                }),
              ]}
            >
              <Input.Password />
            </Form.Item>
          </div>
        </div>
        <div className="mt-4 text-xs font-normal leading-4 text-gray-500 text-opacity-80">
          {dict.public.passwordPattern}
        </div>
        <Form.Item className="!mt-6 text-right">
          <Button
            type="primary"
            htmlType="submit"
            loading={buttonLoading}
            icon={<SaveOutlined />}
            data-testid="saveButton"
          >
            {dict.public.save}
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}
