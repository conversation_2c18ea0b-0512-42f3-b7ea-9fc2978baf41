"use client";

import { Form, Input, Button, message } from "antd";
import { useEffect, useState } from "react";
import { postConnectToken } from "@/services/identityConnect";
import { useRouter } from "next/navigation";
import { getPasswordPattern, removeLoginCookie } from "@/components/tools";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import AuthLayout from "@/components/containers/authLayout";
import Link from "next/link";

export default function LoginForm({
  dict,
  lang,
  response,
  onResponse,
  searchParams,
}) {
  const [form] = Form.useForm();
  const [loadingAction, setLoadingAction] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { executeRecaptcha } = useGoogleReCaptcha();
  const router = useRouter();

  useEffect(() => {
    removeLoginCookie();
  }, [lang]);

  useEffect(() => {
    if (searchParams?.reload) {
      router.push(`/${lang}/auth/login/`);
    }
  }, [searchParams]);

  const showMessage = (type, message) => {
    messageApi.open({
      type: type,
      content: message,
    });
  };

  const isError = (message) => {
    setLoadingAction(false);
    showMessage("error", dict.error[message] || message);
  };

  const isSuccess = () => {
    showMessage("success", dict.public.success);
  };

  const onFinish = async (values) => {
    try {
      setLoadingAction(true);
      values.recaptchaToken = await executeRecaptcha("login");
      const res = await postConnectToken(values, lang);
      if (res?.error) {
        isError(res?.error_description);
        if (response) onResponse(false);
      } else {
        isSuccess();

        if (response) {
          setLoadingAction(false);
          onResponse(true);
        } else router.push(`/${lang}/portal/`);
      }
    } catch (error) {
      if (error?.message != undefined) isError(error?.message);
    }
  };

  const LoginForm = () => {
    return (
      <Form form={form} onFinish={onFinish} layout="vertical" className="!mt-6">
        <Form.Item
          name="username"
          data-testid="username"
          rules={[
            {
              required: true,
              message: `${dict.public.requiredField}`,
            },
          ]}
        >
          <Input autoFocus placeholder={dict.public.username} />
        </Form.Item>
        <Form.Item
          name="password"
          data-testid="password"
          rules={[
            {
              required: true,
              pattern: getPasswordPattern,
              message: `${dict.public.passwordPattern}`,
            },
          ]}
        >
          <Input.Password placeholder={dict.public.password} />
        </Form.Item>
        <Form.Item className="!mb-0">
          <Button
            data-testid="buttonSubmit"
            type="primary"
            className="w-full"
            htmlType="submit"
            loading={loadingAction}
          >
            {dict.public.login}
          </Button>
        </Form.Item>
      </Form>
    );
  };

  return (
    <>
      {contextHolder}
      {!response ? (
        <AuthLayout
          dict={dict}
          lang={lang}
          title={dict.auth.loginSignTitle}
          description={dict.auth.loginSignTitleDesc}
          minHeight={"659px"}
        >
          <LoginForm />
          <Link
            data-testid="linkForgotPassword"
            href={`/${lang}/auth/forgot-password/`}
            className="my-6 block text-center text-sm font-bold text-primary-color"
          >
            {dict.auth.forgotPasswordTitle}
          </Link>
        </AuthLayout>
      ) : (
        <LoginForm />
      )}
    </>
  );
}
