"use client";

import { Descriptions, Modal, Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { formatDate, useResponseHandler } from "@/components/tools";
import { IconCheck } from "@/components/icons";
import { useState } from "react";
import {
  deleteMessageOrAnnouncement
} from "@/services/messages";
export default function DeleteModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
}) {
  const { handleResponse, handleError } = useResponseHandler();
  const [formLoading, setFormLoading] = useState(false);

  const deleteMessage = async (value) => {
    try {
      setFormLoading(true);
      const res = await deleteMessageOrAnnouncement(lang, value.id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        window.location.reload();
        setIsModalOpen(false); 
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {dict.public.sureDelete}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <div className="flex flex-col px-6 pb-1 pt-4">
        <Descriptions
          className="review-desc"
          bordered
          column={{
            xs: 1,
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            xxl: 1,
          }}
          size="small"
        >
          <Descriptions.Item label={dict.messages.sender}>
            {selectedData?.userName}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.type}>
            {selectedData?.type}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.action.releaseDate}>
            {formatDate(`${selectedData?.broadcastDate}+00:00`, lang)}
          </Descriptions.Item>
          <Descriptions.Item label={dict.messages.deliveries}>
            {selectedData?.broadcastChannel}
          </Descriptions.Item>
         
        </Descriptions>
      </div>
      <div className="flex flex-col px-6 pb-1 pt-4">
        <Button 
              danger
              onClick={() => deleteMessage(selectedData)}
            >
              <IconCheck className={"h-5 w-5"} />
              {dict.public.delete}
            </Button>
      </div>
    </Modal>
  );
}
