const menus = [
  // #region offers
  {
    path: "offers/",
    roles: ["AdminUser"],
  },
  {
    path: "offers/offers-listing/",
    roles: ["AdminUser"],
  },
  // #endregion offers

  // #region requestManagement
  {
    path: "request-management/",
    roles: ["AdminUser"],
  },
  {
    path: "request-management/request-management-listing/",
    roles: ["AdminUser"],
  },
  // #endregion requestManagement

  // #region messages
  {
    path: "messages/",
    roles: ["AdminUser"],
  },
  {
    path: "messages/action/",
    roles: ["AdminUser"],
  },
  // #endregion messages

  // #region configs
  {
    path: "configs/",
    roles: ["AdminUser"],
  },
  {
    path: "configs/product-management/",
    roles: ["AdminUser"],
  },
  {
    path: "configs/product-management/action/",
    roles: ["AdminUser"],
  },
  {
    path: "configs/agency-management/",
    roles: ["AdminUser"],
  },
  {
    path: "configs/unit-management/",
    roles: ["AdminUser"],
  },
  {
    path: "configs/unit-management/action/",
    roles: ["AdminUser"],
  },
  // #endregion configs

  // #region profile
  {
    path: "profile/",
    roles: ["AdminUser"],
  },
  {
    path: "profile/change-password/",
    roles: ["AdminUser"],
  },
  // #endregion profile
];

const hasPermission = (path, roles) => {
  const menuItem = menus.find((menu) => {
    if (menu.dynamic) {
      return path.startsWith(menu.path);
    }
    return path === menu.path;
  });
  if (!menuItem) return false;
  const hasRole = menuItem.roles.some((role) => roles?.includes(role));
  return hasRole;
};

export { hasPermission };
