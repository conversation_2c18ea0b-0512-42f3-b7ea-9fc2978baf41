"use client";

import { Form, Spin, Input, But<PERSON>, DatePicker, Select, Upload } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { getSpinIndicator, useResponse<PERSON>and<PERSON> } from "@/components/tools";
import { useUser } from "@/components/contexts/userContext";
import { getDateFormat } from "@/components/tools";
import { IconArrowSmallLeft, IconImage } from "@/components/icons";
import { getBroadcastChannel, postSendMessage } from "@/services/messages";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

export default function Action({ dict, lang }) {
  const [form] = Form.useForm();
  const { handleError, handleResponse } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const router = useRouter();
  const [file, setFile] = useState([]);
  const [selectedType, setSelectedType] = useState([]);
  const [broadcastChannel, setBroadcastChannel] = useState([]);
  dayjs.extend(utc);

  const types = [
    { key: 1, value: dict.messages.message },
    { key: 2, value: dict.messages.announcement },
  ];

  const messageOptions = [
    { key: "msgBox", value: dict.messages.messageBox },
    { key: "mainScreen", value: dict.messages.mainScreen },
  ];

  useEffect(() => {
    const startFetching = async () => {
      setFormLoading(true);
      try {
        const res = await getBroadcastChannel(lang);
        handleResponse(res);
        if (res?.status === "SUCCESS") {
          setBroadcastChannel(res?.data);
        } else {
          setBroadcastChannel([]);
        }
      } catch (error) {
        setBroadcastChannel([]);
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user]);

  const onFinish = async (values) => {
    values.releaseDate = dayjs
      .utc(
        dayjs(values?.releaseDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();
    values.releaseRemoveDate = dayjs
      .utc(
        dayjs(values?.releaseRemoveDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();

    addMessage(values);
  };

  const addMessage = async (values) => {
    setFormLoading(true);
    const formData = new FormData();
    formData.append("file", values?.files?.file);
    formData.append("title", values?.title || "");
    formData.append("contents", values?.contents || "");
    formData.append("type", values?.type || "");
    formData.append("broadcastChannel", values?.broadcastChannel || "");
    formData.append("broadcastDate", values?.broadcastDate || "");
    formData.append("endDate", values?.endDate || "");

    try {
      const res = await postSendMessage(lang, formData);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        router.push(`/${lang}/portal/messages/`);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const handleBeforeUpload = (file) => {
    setFile([file]);
    return false;
  };

  const handleRemove = () => {
    setFile([]);
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      <Form
        form={form}
        layout="vertical"
        className="w-full"
        onFinish={onFinish}
      >
        <div className="rounded-xl bg-white p-6 pb-0 drop-shadow-md">
          <h1 className="mb-4 text-2xl font-medium">
            <span>{dict.messages.action.add}</span>
          </h1>
          <div className="flex flex-wrap gap-x-6">
            <div className="w-full">
              <Form.Item
                name="title"
                data-testid="title"
                className="!shrink !grow !basis-52"
                label={dict.messages.summaryMessage}
                rules={[
                  {
                    required: true,
                    message: `${dict.public.requiredField}`,
                  },
                ]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="contents"
                data-testid="contents"
                className="!shrink !grow !basis-52"
                label={dict.messages.action.message}
                rules={[
                  {
                    required: true,
                    message: `${dict.public.requiredField}`,
                  },
                ]}
              >
                <Input.TextArea rows={4} />
              </Form.Item>
            </div>
          </div>
          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="broadcastChannel"
              data-testid="broadcastChannel"
              className="!shrink !grow !basis-52"
              label={
                <span className="!line-clamp-1 !overflow-hidden">
                  {dict.messages.action.messageChannel}
                </span>
              }
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <Select
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={broadcastChannel?.map((channel) => {
                  return {
                    value: channel.key,
                    label: channel.value,
                  };
                })}
              />
            </Form.Item>
            <Form.Item
              name="endDate"
              data-testid="endDate"
              className="!shrink !grow !basis-52"
              label={dict.messages.action.releaseDate}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <DatePicker className="w-full" format={getDateFormat[lang]} />
            </Form.Item>

            <Form.Item
              name="messageReleasePlace"
              data-testid="messageReleasePlace"
              className="!shrink !grow !basis-52"
              label={
                <span className="!line-clamp-1 !overflow-hidden">
                  {dict.messages.action.messageReleasePlace}
                </span>
              }
            >
              <Select
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={messageOptions?.map((channel) => {
                  return {
                    value: channel.key,
                    label: channel.value,
                  };
                })}
              />
            </Form.Item>
            <Form.Item
              name="type"
              data-testid="type"
              className="!shrink !grow !basis-52"
              label={dict.messages.type}
            >
              <Select
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={types?.map((channel) => {
                  return {
                    value: channel.key,
                    label: channel.value,
                  };
                })}
              />
            </Form.Item>
          </div>
          {selectedType === "2" && (
            <Form.Item
              name="releaseRemoveDate"
              data-testid="releaseRemoveDate"
              className="!shrink !grow !basis-52"
              label={dict.messages.action.releaseRemoveDate}
              rules={[
                {
                  required: true,
                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              <DatePicker className="w-full" format={getDateFormat[lang]} />
            </Form.Item>
          )}
          <div className="pb-4">
            <Form.Item
              name="files"
              data-testid="files"
              label={dict.messages.action.addPic}
            >
              <Upload.Dragger
                accept=".jpg, .png, .jpeg"
                beforeUpload={handleBeforeUpload}
                onRemove={handleRemove}
                fileList={file}
              >
                <div className="flex flex-col items-center justify-center gap-4 py-2">
                  <div className="bg-medium-purple rounded-full border-4 border-indigo-50 p-1">
                    <IconImage className={"h-7 w-7"} />
                  </div>
                  <div className="!text-gray-500">
                    <div>{dict.public.validExtensionsJustImage}</div>
                    <div>{dict.public.validFileSize}</div>
                    <div>{dict.public.recommendedSize}</div>
                  </div>
                  <Button
                    type="primary"
                    className="!bg-medium-purple !text-medium-blue"
                  >
                    {dict.public.selectFile}
                  </Button>
                </div>
              </Upload.Dragger>
            </Form.Item>
          </div>
        </div>
        <div className="mt-6 flex justify-between">
          <Button
            type="primary"
            onClick={() => {
              router.push(`/${lang}/portal/messages/`);
            }}
            className="!flex items-center gap-1 !bg-gray-500 !bg-opacity-75"
            data-testid="backButton"
          >
            <IconArrowSmallLeft className={"h-4 w-4"} />
            {dict.public.back}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            data-testid="saveButton"
          >
            {dict.public.save}
          </Button>
        </div>
      </Form>
    </Spin>
  );
}
