import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import {
  postPrintDocument,
  postGetQueryPolicyDetail,
} from "@/services/insurance";
import ReviewModal from "@/app/[lang]/portal/offers/offers-listing/reviewModal";

// Mock the insurance services
jest.mock("@/services/insurance", () => ({
  postPrintDocument: jest.fn(),
  postGetQueryPolicyDetail: jest.fn(),
}));

describe("ReviewModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;
      let mockSetIsModalOpen;
      let mockSelectedData;
      let mockUserDetailInfo;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockSetIsModalOpen = jest.fn();
        mockSelectedData = {
          policyNo: "123456",
          productNo: "101",
          endorsNo: "0",
          grossPremiumAmount: "1000",
          insuredFullName: "Test User",
          idType: "C",
          idNumber: "12345678901",
          plateNo: "34ABC123",
          brand: "Toyota",
          model: "Corolla",
          modelYear: "2020",
          fuelType: "1",
          chassisNumber: "CH123456",
          engineNumber: "EN123456",
          usageValue: "Private",
          damageStep: "A",
          delaySurprise: "N",
          registrationDate: "2020-01-01",
          coverages: [{ coverName: "Test Coverage", coverAmount: "1000" }],
          coverDeductions: [
            { coverName: "Test Deduction", coverAmount: "100" },
          ],
        };

        mockUserDetailInfo = {
          ...mockSelectedData,
          policyStatus: "T",
          offerStatus: "Active",
          policyEndors: [
            {
              policyNo: "123456",
              endorsNo: "1",
              endorsTypeName: "Test Endorsement",
            },
          ],
        };

        // Reset all mocks before each test
        jest.clearAllMocks();
      });

      test("modal:Zeyil bilgileri doğru şekilde render edilmeli.", async () => {
        postGetQueryPolicyDetail.mockResolvedValueOnce({
          status: "SUCCESS",
          data: {
            ...mockSelectedData,
            insuredFullName: "Updated User",
          },
        });

        await act(async () => {
          render(
            <ReviewModal
              dict={dict}
              lang={lang}
              setIsModalOpen={mockSetIsModalOpen}
              selectedData={mockSelectedData}
              userDetailInfo={mockUserDetailInfo}
            />
          );
        });

        // Wait for the zeyil data to be loaded
        await waitFor(() => {
          expect(screen.getByText("Test Endorsement")).toBeInTheDocument();
        });
      });

      test("modal:Yazdırma butonu çalışmalı.", async () => {
        postPrintDocument.mockResolvedValueOnce({
          status: "SUCCESS",
          data: {
            certificate: "test-certificate-data",
          },
        });

        await act(async () => {
          render(
            <ReviewModal
              dict={dict}
              lang={lang}
              setIsModalOpen={mockSetIsModalOpen}
              selectedData={mockSelectedData}
              userDetailInfo={mockUserDetailInfo}
            />
          );
        });

        const printButton = screen.getByText(
          dict.offers.offersListing.quotationPolicyPrinting
        );

        await act(async () => {
          fireEvent.click(printButton);
        });

        await waitFor(() => {
          expect(postPrintDocument).toHaveBeenCalled();
        });
      });

      test("modal:Geri butonu çalışmalı.", async () => {
        await act(async () => {
          render(
            <ReviewModal
              dict={dict}
              lang={lang}
              setIsModalOpen={mockSetIsModalOpen}
              selectedData={mockSelectedData}
              userDetailInfo={mockUserDetailInfo}
            />
          );
        });

        const backButton = screen.getByText(dict.public.back);

        await act(async () => {
          fireEvent.click(backButton);
        });

        expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
      });

      test("modal:Zeyil seçimi ve detay görüntüleme çalışmalı.", async () => {
        postGetQueryPolicyDetail.mockResolvedValueOnce({
          status: "SUCCESS",
          data: {
            ...mockSelectedData,
            insuredFullName: "Updated User",
          },
        });

        await act(async () => {
          render(
            <ReviewModal
              dict={dict}
              lang={lang}
              setIsModalOpen={mockSetIsModalOpen}
              selectedData={mockSelectedData}
              userDetailInfo={mockUserDetailInfo}
            />
          );
        });

        // Wait for the zeyil data to be loaded
        await waitFor(async () => {
          const selectButton = screen.getByText(dict.public.select);

          await act(async () => {
            fireEvent.click(selectButton);
          });
        });

        await waitFor(() => {
          expect(postGetQueryPolicyDetail).toHaveBeenCalled();
        });
      });
    });
  });
});
