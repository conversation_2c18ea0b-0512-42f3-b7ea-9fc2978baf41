"use server";

import { getDictionary } from "@/dictionaries";
import RequestManagementListing from "./requestManagementListing";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.requestManagement.title} | ${dict.public.title}`,
  };
}

export default async function RequestManagementListingPage({
  params: { lang },
}) {
  const dict = await getDictionary(lang);
  return <RequestManagementListing dict={dict} lang={lang} />;
}
