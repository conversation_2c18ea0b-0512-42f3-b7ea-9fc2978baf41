import { getDictionary } from "@/dictionaries";
import SectionHeaderComponent from "@/components/containers/sectionHeader";

export default async function Messages({ children, params }) {
  const lang = params.lang;
  const dict = await getDictionary(lang);

  return (
    <>
      <SectionHeaderComponent
        title={dict.messages.title}
        dict={dict}
        lang={lang}
      />
      <div className="p-4">{children}</div>
    </>
  );
}
