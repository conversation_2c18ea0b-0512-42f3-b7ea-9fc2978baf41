const fs = require("fs");
const path = require("path");

const reportDir = "./coverage/lcov-report";

function inlineCSS(htmlFilePath, cssContentMap) {
  let html = fs.readFileSync(htmlFilePath, "utf8");
  html = html.replace(
    /<link\s+rel=["']stylesheet["']\s+href=["'](.*?)["']\s*\/?>/g,
    (match, href) => {
      const fileName = path.basename(href);
      const cssContent = cssContentMap[fileName];
      if (cssContent) {
        return `<style>${cssContent}</style>`;
      }
      return match;
    }
  );
  fs.writeFileSync(htmlFilePath, html, "utf8");
}

function getAllHtmlFiles(dir) {
  let files = [];
  const items = fs.readdirSync(dir, { withFileTypes: true });
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory()) {
      files = files.concat(getAllHtmlFiles(fullPath));
    } else if (item.isFile() && item.name.endsWith(".html")) {
      files.push(fullPath);
    }
  }
  return files;
}

function getCSSContentMap(cssFiles) {
  const cssContentMap = {};
  cssFiles.forEach((fileName) => {
    const cssPath = path.join(reportDir, fileName);
    if (fs.existsSync(cssPath)) {
      cssContentMap[fileName] = fs.readFileSync(cssPath, "utf8");
    } else {
      console.warn(`${fileName} bulunamadı.`);
    }
  });
  return cssContentMap;
}

const cssFiles = ["base.css", "prettify.css"];
const cssContentMap = getCSSContentMap(cssFiles);
const htmlFiles = getAllHtmlFiles(reportDir);
htmlFiles.forEach((htmlFile) => {
  inlineCSS(htmlFile, cssContentMap);
});
console.log(`${htmlFiles.length} HTML dosyasında CSS inline edildi.`);
