"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const getUnitManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/paginated-unit-management`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getUnitManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postUnitManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/add-unit-management`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postUnitManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const putUnitManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/update-unit-management`,
    options: {
      cache: "no-store",
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("putUnitManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getUnitManagementSingle = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${id}/settings/get-unit-management`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getUnitManagementSingle", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

export {
  getUnitManagement,
  postUnitManagement,
  putUnitManagement,
  getUnitManagementSingle,
};
