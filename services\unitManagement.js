"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const getUnitManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/paginated-unit-management`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const postUnitManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/add-unit-management`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const putUnitManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/update-unit-management`,
    {
      cache: "no-store",
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getUnitManagementSingle = async (lang, id) => {
  let res = await fetch(
    `${process.env.API_URL}offers/${id}/settings/get-unit-management`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

export {
  getUnitManagement,
  postUnitManagement,
  putUnitManagement,
  getUnitManagementSingle,
};
