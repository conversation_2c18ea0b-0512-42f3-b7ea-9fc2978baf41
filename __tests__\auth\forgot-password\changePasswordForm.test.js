import { fireEvent, render, waitFor } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import ChangePasswordForm from "@/app/[lang]/auth/forgot-password/changePasswordForm";

describe("ChangePasswordForm", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, changePasswordForm;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        changePasswordForm = render(
          <ChangePasswordForm dict={dict} lang={lang} mailAddress={""} />
        );
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("input:email olmalı ve placeholder içermeli.", () => {
        const { getByPlaceholderText } = changePasswordForm;
        const email = getByPlaceholderText(dict.public.username);
        expect(email).toBeInTheDocument();
      });

      test("input:code olmalı ve placeholder içermeli ve maxLength 8 olmalı.", () => {
        const { getByPlaceholderText } = changePasswordForm;
        const code = getByPlaceholderText(dict.public.code);
        expect(code).toBeInTheDocument();
        expect(code).toHaveAttribute("maxLength", "8");
      });

      test("input:password olmalı ve placeholder içermeli ve maxLength 32 olmalı.", () => {
        const { getByPlaceholderText } = changePasswordForm;
        const password = getByPlaceholderText(dict.public.password);
        expect(password).toBeInTheDocument();
        expect(password).toHaveAttribute("maxLength", "32");
      });

      test("input:confirmPassword olmalı ve placeholder içermeli ve maxLength 32 olmalı.", () => {
        const { getByPlaceholderText } = changePasswordForm;
        const confirmPassword = getByPlaceholderText(
          dict.public.confirmPassword
        );
        expect(confirmPassword).toBeInTheDocument();
        expect(confirmPassword).toHaveAttribute("maxLength", "32");
      });

      test(`button:submit olmalı ve başlık içermeli. ${lang}`, () => {
        const { getByTestId } = changePasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        expect(buttonSubmit).toBeInTheDocument();
        expect(buttonSubmit.textContent).toBe(dict.auth.changePassword);
      });

      test(`link:authLogin olmalı, doğru url ve başlık içermeli.`, () => {
        const { getByTestId } = changePasswordForm;
        const linkAuthLogin = getByTestId("linkAuthLogin");
        expect(linkAuthLogin).toBeInTheDocument();
        expect(linkAuthLogin).toHaveAttribute("href", `/${lang}/auth/login`);
        expect(linkAuthLogin.textContent).toBe(dict.auth.loginSignTitle);
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region Form validasyon kontrolü
      test(`form:email alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = changePasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const email = getByTestId("email");
          expect(email).toBeInTheDocument();
          expect(email.textContent).toBe(dict.public.requiredField);
        });
      });

      test(`form:code alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = changePasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const code = getByTestId("code");
          expect(code).toBeInTheDocument();
          expect(code.textContent).toBe(dict.public.requiredField);
        });
      });

      test(`form:password alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = changePasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const password = getByTestId("password");
          expect(password).toBeInTheDocument();
          expect(password.textContent).toBe(dict.public.passwordPattern);
        });
      });

      test(`form:confirmPassword alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = changePasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const confirmPassword = getByTestId("confirmPassword");
          expect(confirmPassword).toBeInTheDocument();
          expect(confirmPassword.textContent).toBe(dict.public.passwordPattern);
        });
      });

      test("password ve confirmPassword eşleşmediğinde hata mesajı gösterilmeli", async () => {
        const { getByPlaceholderText, getByTestId, findByText } =
          changePasswordForm;
        const passwordInput = getByPlaceholderText(dict.public.password);
        const confirmPasswordInput = getByPlaceholderText(
          dict.public.confirmPassword
        );
        const submitButton = getByTestId("buttonSubmit");
        fireEvent.change(passwordInput, { target: { value: "Password123!" } });
        fireEvent.change(confirmPasswordInput, {
          target: { value: "Different123!" },
        });
        fireEvent.click(submitButton);
        const errorMessage = await findByText(
          dict.profile.changePassword.doNotMatchPassword
        );
        expect(errorMessage).toBeInTheDocument();
      });
      // #endregion Form validasyon kontrolü
    });
  });
});
