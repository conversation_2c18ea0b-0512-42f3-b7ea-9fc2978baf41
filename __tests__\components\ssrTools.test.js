import {
  getToken,
  getAcceptLanguage,
  getTokenValue,
  validResponse,
  isSiteVerified,
} from "@/components/ssrTools";
import { cookies } from "next/headers";

// Mock next/headers
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

describe("ssrTools", () => {
  // #region Token işlemleri
  describe("getToken", () => {
    test("Token mevcut olduğunda token değerini dönmeli", () => {
      const mockCookieStore = {
        get: jest.fn().mockReturnValue({ value: "test-token" }),
      };
      cookies.mockReturnValue(mockCookieStore);

      const result = getToken();
      expect(result).toBe("test-token");
      expect(cookies).toHaveBeenCalled();
      expect(mockCookieStore.get).toHaveBeenCalledWith("token");
    });

    test("Token mevcut olmadığında undefined dönmeli", () => {
      const mockCookieStore = {
        get: jest.fn().mockReturnValue(undefined),
      };
      cookies.mockReturnValue(mockCookieStore);

      const result = getToken();
      expect(result).toBeUndefined();
    });
  });

  describe("getTokenValue", () => {
    test("Token mevcut olduğunda decode edilmiş token değerini dönmeli", () => {
      const mockCookieStore = {
        get: jest.fn().mockReturnValue({
          value:
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
        }),
      };
      cookies.mockReturnValue(mockCookieStore);

      const result = getTokenValue();
      expect(result).toEqual({
        sub: "1234567890",
        name: "John Doe",
        iat: 1516239022,
      });
    });

    test("Token mevcut olmadığında undefined dönmeli", () => {
      const mockCookieStore = {
        get: jest.fn().mockReturnValue(undefined),
      };
      cookies.mockReturnValue(mockCookieStore);

      const result = getTokenValue();
      expect(result).toBeUndefined();
    });
  });
  // #endregion Token işlemleri

  // #region Dil işlemleri
  describe("getAcceptLanguage", () => {
    test("Dil kodu verildiğinde formatlanmış dil stringini dönmeli", () => {
      const result = getAcceptLanguage("tr");
      expect(result).toBe("tr-TR");
    });
  });
  // #endregion Dil işlemleri

  // #region Response işlemleri
  describe("validResponse", () => {
    test("Response başarılı olduğunda success response dönmeli", async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ status: "SUCCESS", data: "test" }),
      };

      const result = await validResponse(mockResponse);
      expect(result).toBe(JSON.stringify({ status: "SUCCESS", data: "test" }));
    });

    test("Response başarısız olduğunda error response dönmeli", async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        json: jest
          .fn()
          .mockResolvedValue({ status: "ERROR", message: "Test error" }),
      };

      const result = await validResponse(mockResponse);
      expect(result).toBe(
        JSON.stringify({ status: "ERROR", message: "Test error" })
      );
    });

    test("401 unauthorized durumunda token temizlenmeli ve 401 response dönmeli", async () => {
      const mockResponse = {
        ok: false,
        status: 401,
      };
      const mockCookieStore = {
        set: jest.fn(),
      };
      cookies.mockReturnValue(mockCookieStore);

      const result = await validResponse(mockResponse);
      expect(result).toBe(
        JSON.stringify({
          status: "401",
          code: "",
          message: "",
          validationMessages: [],
          errorMessages: [],
          data: [],
        })
      );
      expect(mockCookieStore.set).toHaveBeenCalledWith("token", "");
    });
  });
  // #endregion Response işlemleri

  // #region reCAPTCHA işlemleri
  describe("isSiteVerified", () => {
    test("reCAPTCHA doğrulaması başarılı olduğunda true dönmeli", async () => {
      const mockResponse = {
        success: true,
        score: 0.8,
      };
      global.fetch.mockResolvedValueOnce({
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await isSiteVerified("test-token");
      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        "https://www.google.com/recaptcha/api/siteverify",
        expect.any(Object)
      );
    });

    test("reCAPTCHA doğrulaması başarısız olduğunda false dönmeli", async () => {
      const mockResponse = {
        success: false,
        score: 0.3,
      };
      global.fetch.mockResolvedValueOnce({
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await isSiteVerified("test-token");
      expect(result).toBe(false);
    });
  });
  // #endregion reCAPTCHA işlemleri
});
