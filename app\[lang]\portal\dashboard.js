"use client";

import { DatePicker, Skeleton, Table } from "antd";
import { IconBuildingOffice, IconCheckBadge } from "@/components/icons";
import { useUser } from "@/components/contexts/userContext";
import {
  formatDate,
  formatMoney,
  getDateFormat,
  useResponseHandler,
} from "@/components/tools";
import ReactEcharts from "echarts-for-react";
import dayjs from "dayjs";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { getActiveProducts } from "@/services/product";
import { useEffect } from "react";

export default function DashboardPage({ dict, lang }) {
  const { user } = useUser();
  const todayDate = dayjs(new Date());

  // TODO: BUNLAR GEÇİCİ KODLAR, DASHBOARD DA HERHANGİ BİR API YE ÇIKILDIĞINDA BU BLOĞU SİLECEKSİNİZ GÜVENLİK TESTLERİNDEN GEÇEBİLMESİ İÇİN KODLANDI
  // #region geçici blok
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse } = useResponseHandler();
  useEffect(() => {
    const startFetching = async () => {
      const res = await getActiveProducts(lang);
      handleResponse(res);
    };
    startFetching();
  }, [lang, reloadByNewToken]);
  // #endregion geçici blok

  const salesByProductOption = {
    title: {
      text: `${dict.dashboard.salesVolumesByProduct}`,
    },
    tooltip: {},
    xAxis: {
      type: "category",
      data: [
        "101 - Trafik Sigortası",
        "102 - Yurtdışı Trafik",
        "500 - Ferdi Kaza",
      ],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: [
          { value: 5, itemStyle: { color: "#FFB6C1" } }, // Light Pink
          { value: 20, itemStyle: { color: "#87CEFA" } }, // Light Sky Blue
          { value: 36, itemStyle: { color: "#98FB98" } }, // Pale Green
        ],
        type: "bar",
      },
    ],
  };

  const trendByDateOption = {
    title: {
      text: `${dict.dashboard.salesTrendInformationByDate}`,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        return formatMoney({ value: params[0].value });
      },
    },
    xAxis: {
      type: "category",
      data: [
        "26.05.2024",
        "27.05.2024",
        "28.05.2024",
        "29.05.2024",
        "30.05.2024",
      ],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return formatMoney({ value: value });
        },
      },
    },
    series: [
      {
        data: [40000, 60000, 50000, 70000, 80000],
        type: "line",
        smooth: true,
        itemStyle: {
          color: "#6A5ACD", // Soft Slate Blue
        },
        lineStyle: {
          color: "#6A5ACD", // Soft Slate Blue
        },
      },
    ],
  };

  const salesAmountOption = {
    title: {
      text: `${dict.dashboard.salesAmountsByProduct}`,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        const value = params[0].value;
        return formatMoney({ value: value });
      },
    },
    xAxis: {
      type: "category",
      data: [
        "101 - Trafik Sigortası",
        "102 - Yurtdışı Trafik",
        "500 - Ferdi Kaza",
      ],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return formatMoney({ value: value });
        },
      },
    },
    series: [
      {
        data: [
          { value: 15000, itemStyle: { color: "#FFB6C1" } }, // Light Pink
          { value: 25000, itemStyle: { color: "#87CEFA" } }, // Light Sky Blue
          { value: 40000, itemStyle: { color: "#98FB98" } }, // Pale Green
        ],
        type: "bar",
      },
    ],
  };

  const totalCommissionsOption = {
    title: {
      text: `${dict.dashboard.totalCommissionsByProduct}`,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        const value = params[0].value;
        return formatMoney({ value: value });
      },
    },
    xAxis: {
      type: "category",
      data: [
        "101 - Trafik Sigortası",
        "102 - Yurtdışı Trafik",
        "500 - Ferdi Kaza",
      ],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return formatMoney({ value: value });
        },
      },
    },
    series: [
      {
        data: [
          { value: 15000 * 0.3, itemStyle: { color: "#FFB6C1" } }, // Light Pink
          { value: 25000 * 0.3, itemStyle: { color: "#87CEFA" } }, // Light Sky Blue
          { value: 40000 * 0.3, itemStyle: { color: "#98FB98" } }, // Pale Green
        ],
        type: "bar",
      },
    ],
  };

  const pendingProposalsData = [
    {
      key: "1",
      policyNo: "501681",
      productName: "Trafik Sigortası",
      insured: "Hüseyin Erdoğan",
      quotationDate: "2024-05-29",
      amount: 10200.55,
    },
    {
      key: "2",
      policyNo: "501682",
      productName: "Yurtdışı Trafik",
      insured: "Olgu Can Keser",
      quotationDate: "2024-05-29",
      amount: 6000,
    },
    {
      key: "3",
      policyNo: "501683",
      productName: "Ferdi Kaza",
      insured: "Eren Filiz",
      quotationDate: "2024-05-29",
      amount: 7500,
    },
    {
      key: "4",
      policyNo: "501681",
      productName: "Trafik Sigortası",
      insured: "Hüseyin Erdoğan",
      quotationDate: "2024-05-29",
      amount: 10200,
    },
    {
      key: "5",
      policyNo: "501682",
      productName: "Yurtdışı Trafik",
      insured: "Olgu Can Keser",
      quotationDate: "2024-05-29",
      amount: 6000,
    },
    {
      key: "6",
      policyNo: "501683",
      productName: "Ferdi Kaza",
      insured: "Eren Filiz",
      quotationDate: "2024-05-29",
      amount: 7500,
    },
    {
      key: "7",
      policyNo: "501681",
      productName: "Trafik Sigortası",
      insured: "Hüseyin Erdoğan",
      quotationDate: "2024-05-29",
      amount: 10200,
    },
    {
      key: "8",
      policyNo: "501682",
      productName: "Yurtdışı Trafik",
      insured: "Olgu Can Keser",
      quotationDate: "2024-05-29",
      amount: 6000,
    },
    {
      key: "9",
      policyNo: "501683",
      productName: "Ferdi Kaza",
      insured: "Eren Filiz",
      quotationDate: "2024-05-29",
      amount: 7500,
    },
  ];

  const pendingProposalsColumns = [
    {
      title: `${dict.offers.offersListing.offerPolicyNum}`,
      dataIndex: "policyNo",
      key: "policyNo",
    },
    {
      title: `${dict.offers.offersListing.product}`,
      key: "productName",
      dataIndex: "productName",
    },
    {
      title: `${dict.public.nameSurname} / ${dict.offers.offersListing.company}`,
      key: "insured",
      dataIndex: "insured",
    },
    {
      title: `${dict.offers.offersListing.quotationDate}`,
      key: "quotationDate",
      dataIndex: "quotationDate",
      render: (val) => formatDate(val, lang, false),
    },
    {
      title: `${dict.public.totalAmount}`,
      dataIndex: "amount",
      key: "amount",
      render: (text) => formatMoney({ value: text }),
    },
  ];

  return (
    <>
      <div className="flex flex-wrap items-center gap-5">
        {user?.name ? (
          <>
            <div className="ml-1 flex flex-1 flex-col justify-center">
              <div className="text-xl font-medium text-gray-900 md:text-3xl">
                {dict.dashboard.hello},{" "}
                <span className="inline-flex font-bold">
                  {user?.name && (
                    <>
                      {`${user?.name}`}
                      <IconCheckBadge className={"h-4 w-4 text-green-500"} />
                    </>
                  )}
                </span>
              </div>
              <div className="flex items-center gap-1 text-sm font-semibold leading-4 text-gray-800">
                <IconBuildingOffice className={"h-6 w-6"} />
                <div>{dict.dashboard.emaaAgency}</div>
              </div>
            </div>
            <div className="basis-full self-center md:basis-48">
              <DatePicker
                className="w-full"
                format={getDateFormat[lang]}
                defaultValue={todayDate}
                disabledDate={(d) => !d || d.isAfter(todayDate)}
              />
            </div>
          </>
        ) : (
          <div className="flex h-16 w-full items-center gap-2">
            <Skeleton.Input active size={"large"} />
          </div>
        )}
      </div>
      <div className="mt-6 grid grid-cols-1 gap-4 xl:grid-cols-2">
        <div className="h-80 rounded-xl bg-white p-6 drop-shadow-md lg:h-96">
          <ReactEcharts
            option={salesByProductOption}
            style={{ height: "100%", width: "100%" }}
          />
        </div>
        <div className="h-80 rounded-xl bg-white p-6 drop-shadow-md lg:h-96">
          <ReactEcharts
            option={trendByDateOption}
            style={{ height: "100%", width: "100%" }}
          />
        </div>
        <div className="h-80 rounded-xl bg-white p-6 drop-shadow-md lg:h-96">
          <ReactEcharts
            option={salesAmountOption}
            style={{ height: "100%", width: "100%" }}
          />
        </div>
        <div className="h-80 rounded-xl bg-white p-6 drop-shadow-md lg:h-96">
          <ReactEcharts
            option={totalCommissionsOption}
            style={{ height: "100%", width: "100%" }}
          />
        </div>
        <div className="h-96 rounded-xl bg-white p-6 drop-shadow-md">
          <h2 className="mb-4 text-lg font-bold">
            {dict.dashboard.pendingOffers}
          </h2>
          <Table
            className="grid-table"
            size="small"
            dataSource={pendingProposalsData}
            columns={pendingProposalsColumns}
            pagination={false}
            scroll={{ x: "600px", y: "231px" }}
          />
        </div>
        <div className="h-96 rounded-xl bg-white p-6 drop-shadow-md">
          <h2 className="mb-4 text-lg font-bold">
            {dict.dashboard.expiredOrExpiringPolicies}
          </h2>
          <Table
            className="grid-table"
            size="small"
            dataSource={pendingProposalsData}
            columns={pendingProposalsColumns}
            pagination={false}
            scroll={{ x: "600px", y: "231px" }}
          />
        </div>
      </div>
    </>
  );
}
