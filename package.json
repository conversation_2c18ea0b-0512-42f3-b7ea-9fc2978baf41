{"name": "digital-insurance-operational-portal", "version": "0.1.0", "private": true, "scripts": {"lint": "next lint", "lint:es": "eslint --ext .js,.jsx .", "lint:fix": "eslint --fix --ext .js,.jsx .", "dev": "next dev -p 3007", "build": "next build", "start": "next start", "export": "yarn build && next export -o _static", "clear-all": "rm -rf .next _static node_modules", "re-start": "rm -rf .next node_modules && yarn install && yarn dev", "re-build": "rm -rf .next node_modules && yarn install && yarn build", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@lottiefiles/react-lottie-player": "^3.5.4", "antd": "^5.20.6", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "next": "^14.2.11", "next-nprogress-bar": "^2.3.13", "pino": "^9.6.0", "react": "18.3.1", "react-countup": "^6.5.3", "react-dom": "18.3.1", "react-google-recaptcha-v3": "^1.10.1", "react-input-mask": "^2.0.4", "sharp": "^0.32.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-next": "14.2.11", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "pino-pretty": "^13.0.0", "postcss": "8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "3.4.11"}}