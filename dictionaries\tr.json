{"public": {"title": "Dijital Sigortacılık Operasyon Portalı", "login": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "update": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "İptal", "success": "Başarılı.", "sureDelete": "<PERSON><PERSON>ek istediğinizden emin misiniz?", "requiredField": "<PERSON><PERSON><PERSON><PERSON> alan.", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "name": "Ad<PERSON>", "surname": "Soyadı", "nameSurname": "Adı Soyadı", "nationality": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "E-Posta", "address": "<PERSON><PERSON>", "registerDate": "<PERSON><PERSON><PERSON>", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "birthDate": "<PERSON><PERSON><PERSON>", "requestDate": "<PERSON><PERSON>", "approvalDate": "<PERSON><PERSON><PERSON><PERSON>", "passportNumber": "Pasaport Numarası", "citizenshipNumber": "Vatandaşlık Numarası", "tckn": "TC Numarası", "clickToUpload": "Yüklemek İçin Tıklayın", "gender": "Cinsiyeti", "note": "Not", "age": "Yaş<PERSON>", "back": "<PERSON><PERSON>", "saveAndExit": "<PERSON><PERSON> ve Çık", "saveAndNext": "<PERSON><PERSON>", "totalAmount": "Toplam Tutar", "download": "<PERSON><PERSON><PERSON>", "showInBrowser": "<PERSON><PERSON>ı<PERSON><PERSON><PERSON>", "username": "Kullanıcı Adı", "password": "Şifre", "passwordPattern": "En az bir rakam, en az bir küç<PERSON>k harf, en az bir bü<PERSON><PERSON><PERSON> harf, boşluk içermemeli ve 8-32 karakter uzunluğunda olmalıdır.", "createdBy": "<PERSON><PERSON><PERSON>", "updatedBy": "G<PERSON><PERSON><PERSON><PERSON>n", "export": "Dışa Aktar", "error": "<PERSON><PERSON>", "companyName": "Şirket Adı", "taxNumber": "<PERSON><PERSON><PERSON>", "phoneNumber": "Telefon Numarası", "phoneNumberCode": "Kodu", "confirmPassword": "<PERSON><PERSON><PERSON>", "okay": "<PERSON><PERSON>", "continue": "<PERSON><PERSON>", "total": "Toplam", "close": "Ka<PERSON><PERSON>", "createReport": "<PERSON><PERSON>", "clean": "<PERSON><PERSON><PERSON>", "filter": "Filtrele", "action": "Aksiyon", "submitForApproval": "<PERSON><PERSON>", "files": "<PERSON><PERSON><PERSON><PERSON>", "status": "Durum", "documents": "<PERSON><PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "city": "Şehir", "date": "<PERSON><PERSON><PERSON>", "submitTheRequestForApproval": "<PERSON><PERSON>", "reasonForRejection": "<PERSON><PERSON><PERSON><PERSON>", "rejectionDate": "<PERSON><PERSON><PERSON><PERSON>", "giveUp": "Vazgeç", "selectFile": "<PERSON><PERSON><PERSON>", "validExtensions": "Geçerli uzantılar: pdf, jpeg, jpg, png", "validExtensionsJustImage": "Geçerli uzantılar: jpeg, jpg, png", "validFileSize": "Geçerli dosya boyutu: 5MB", "recommendedSize": "Önerilen boyut 800x400 pixel ve katlarıdır.", "person": "<PERSON><PERSON><PERSON>", "validateSpecialCharacters": "Özel karakter içermemelidir.", "kvkkPolicy": "KVKK Politakası", "userAgreement": "Kullanıcı Sözleşmesi", "kvkkClarificationText": "KVKK Aydınlatma Metni", "explicitConsentText": "Açık Rı<PERSON>", "readAndAccept": "kabul ediyorum", "code": "Kod", "location": "<PERSON><PERSON>", "verify": "Doğrulama", "receiveOffer": "Tek<PERSON><PERSON> Al", "newOffer": "<PERSON><PERSON>", "vkn": "VKN", "ykn": "YKN", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insurant": "Sigortalı", "plateNumber": "<PERSON><PERSON><PERSON>", "review": "<PERSON><PERSON><PERSON>", "print": "Yazdır", "issueAPolicy": "Poliçeleştir", "history": "Geçmiş", "active": "Aktif", "passive": "<PERSON><PERSON><PERSON>", "warning": "Uyarı", "month": "Ay", "months": {"january": "Ocak", "february": "Ş<PERSON><PERSON>", "march": "Mart", "april": "<PERSON><PERSON>", "may": "<PERSON><PERSON><PERSON>", "june": "Haziran", "july": "Temmuz", "august": "<PERSON><PERSON><PERSON><PERSON>", "september": "<PERSON><PERSON><PERSON><PERSON>", "october": "Ekik", "november": "Kasım", "december": "Aralık"}, "iAcceptSale": "Ön bilgilendirme formunu ve Mesafeli satış sözleşmesini onaylıyorum.", "payment": "Ödeme", "revise": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detail": "Detay", "validation": "Doğrulama", "select": "<PERSON><PERSON><PERSON><PERSON>", "takePrint": "Basım Al"}, "dashboard": {"title": "<PERSON><PERSON><PERSON>", "hello": "<PERSON><PERSON><PERSON><PERSON>", "emaaAgency": "EMAA SİGORTA A.Ş", "pendingOffers": "<PERSON><PERSON><PERSON>", "expiredOrExpiringPolicies": "Poliçesi Biten veya Bitmek Üzere Olanlar", "totalCommissionsByProduct": "Ürün Bazında Toplam Komisyonlar", "salesAmountsByProduct": "Ürün Bazında Satış Tutarları", "salesTrendInformationByDate": "Tarih Bazlı Satış Trend Bilgisi", "salesVolumesByProduct": "Ürün Bazında Satış Adetleri"}, "offers": {"title": "Teklif/Poliçe", "offersListing": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "report": "<PERSON><PERSON><PERSON>", "appointmentCategory": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "nameSurname": "Adı Soyadı", "offer": "Teklif/Poliçe", "offerPolicy": "Teklif/Poliçe", "insurancePeriod": "<PERSON><PERSON><PERSON>", "grossPremium": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statü", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "validityDate": "Geçerlilik <PERSON>hi", "search": "Ara", "newOffer": "<PERSON><PERSON>", "legalInstitutionLimit": "<PERSON><PERSON><PERSON><PERSON>", "guarantees": "Teminatlar", "isPhysicalDisability": "Fiziksel Özür Var mı ?", "permanentDisability": "Sürekli Sakatlık", "treatmentCostInc": "<PERSON><PERSON> dahil mi", "additionalGuarantees": "<PERSON><PERSON>", "earthquakeFloodVolcanoEruptionLandslide": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> , <PERSON><PERSON>", "terror": "<PERSON><PERSON><PERSON><PERSON>", "emaaClubServices": "Emaa Club Hizmetleri", "death": "<PERSON><PERSON><PERSON><PERSON>", "writeExplanation": "Açıklama yazınız", "idType": "<PERSON><PERSON>", "idNum": "<PERSON><PERSON>", "company": "Ünvan", "insuredName": "Sigortalı Adı", "insuredSurname": "Sigortalı Soyadı", "channel": "<PERSON><PERSON>", "authorizeCode": "<PERSON><PERSON>", "offerPolicyNum": "Teklif/Poliçe Numarası", "policyStatus": "Poliçe  Durumu", "offerPolicyStatus": "Teklif/Poliçe Durumu", "policyCoverages": "Poliçe Teminatları", "limits": "<PERSON><PERSON><PERSON>", "dateDifference": "<PERSON><PERSON><PERSON>", "issueDate": "Veriliş Tarihi", "quotation": "<PERSON><PERSON><PERSON><PERSON>", "policy": "Poliçe", "transactionExecutor": "İşlemi Yapan", "transactionDate": "İşlem Tarihi", "quotationDate": "<PERSON><PERSON><PERSON><PERSON>", "quotationPolicyPrinting": "Teklif/Poliçe Basımı", "collectionReceiptPrinting": "Tahsilat Mahbuzu Basımı", "informationFormPrinting": "Bilgilendirme Formu Basımı", "turkishClausePrintingQuotation": "Türkçe Kloz (Teklif) Basımı", "cardHolder": "<PERSON><PERSON> <PERSON>", "cardNumber": "<PERSON><PERSON><PERSON>", "cvv": "CVV", "expirationDate": "Geçerlilik <PERSON>hi", "payNow": "<PERSON><PERSON><PERSON>", "validateCard": "{{field}} alanı doğru değil", "validateLength": "{{field}} alanı en az {{length}} karakter veya daha fazla olmalı", "cvvTooltip": "CVV/CVC kredi/banka kartınızın arka yüzünde, beyaz imza alanının sağ tarafındaki rakamlardır", "previewConfirm": "Tüm bilgilerin doğruluğunu onaylıyorum", "addNewMember": "<PERSON><PERSON> k<PERSON>şi ekle", "removeMemberConfirm": "Kişiyi sepetten çıkarırsanız başvurusu iptal edilecektir. Onaylıyor musunuz?", "paymentDetail": "Ödeme Ayrıntısı", "paymentSummary": "<PERSON><PERSON><PERSON>", "visaFee": "<PERSON><PERSON>", "visaFeeDescription": "", "serviceFee": "<PERSON><PERSON><PERSON>", "total": "Toplam (KDV Dahil)", "taxFee": "KDV", "creditCard": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "installementNumber": "<PERSON><PERSON><PERSON>", "installment": "Taks<PERSON>", "advancePayment": "<PERSON><PERSON><PERSON><PERSON>", "choseInstallementNumber": "<PERSON>ksit <PERSON> se<PERSON>", "previewScreen": "Ön <PERSON>zleme Ekranı", "offerValidityPeriod": " Teklif geçerlilik süresi dolmuştur.", "agentCode": "<PERSON><PERSON>", "agentName": "Acente Adı", "policyOfferSendMail": "Teklif/Poliçe mail gönder", "successfulEmail": "pdf mailinize gönderilmiştir.", "beneficiary": "<PERSON><PERSON><PERSON>", "beneficiaryDetail": "<PERSON><PERSON><PERSON>", "creditInstitutionType": "<PERSON><PERSON><PERSON> Türü", "authReason": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourPaymentSuccessful": "Poliçeniz başarı ile oluşturuldu", "yourPolicyNum": "Poliçe Numaranız", "occupation": "Mesleği", "fullName": "Müşteri Adı Soyadı", "offerStatus": "<PERSON><PERSON><PERSON><PERSON>", "addendum": "<PERSON><PERSON><PERSON>", "lastVersion": "<PERSON>", "damageStep": "Hasarsızlık Basamağı", "delaySurprise": "Gecikme Sürprim Oranı", "registrationDate": "<PERSON><PERSON><PERSON><PERSON>", "policyReferenceInformation": "Poliçe Referans Bilgileri", "previousAgencyInformation": "Önceki Acente Bilgileri", "previousCompanyInformation": "Önceki Şirket Bilgileri", "previousDigitCode": "Önceki Basamak <PERSON>", "previousPolicyNumber": "Önceki Poliçe Numarası", "previousRenewalNumber": "Önceki <PERSON>nilem<PERSON>", "mainPolicy": "<PERSON>", "addendumPast": "<PERSON><PERSON><PERSON>", "addendumNumber": "<PERSON><PERSON><PERSON>", "addendumType": "<PERSON><PERSON><PERSON>", "addendumDetail": "<PERSON><PERSON><PERSON>", "active": "Aktif", "cancelled": "İptal", "expiryDate": "<PERSON><PERSON><PERSON>", "renewalNumber": "<PERSON><PERSON><PERSON><PERSON>", "collectionReceipt": "Tahsilat Makbuzu"}, "offersCreating": {"title": "<PERSON><PERSON><PERSON><PERSON>", "branchSelection": "<PERSON><PERSON>", "personalInformation": "<PERSON><PERSON><PERSON><PERSON>", "slotSelection": "<PERSON><PERSON> Seçimi", "summary": "Özet", "prepayment": "<PERSON><PERSON>", "appointmentRequest": "<PERSON><PERSON><PERSON>", "countryToVisit": "<PERSON><PERSON><PERSON>", "applicantsFutureCountry": "Başvuru Sahibinin Geleceği Ülke", "branch": "Şube", "appointmentCategory": "<PERSON><PERSON><PERSON>", "applicationType": "Başvuru Türü", "numberOfApplicants": "Başvuran Sayısı", "visaCategory": "Vize <PERSON>", "descOk": "<PERSON><PERSON><PERSON> Kullanıcılarına iletilmiştir.", "mustBeEmailFormat": "E-posta adresi formatına uygun olmalıdır.", "otherType": "<PERSON><PERSON><PERSON>", "electricalType": "Elektrikli", "compulsoryTrafficInsurance": {"title101": "101 - <PERSON><PERSON><PERSON><PERSON>", "title102": "102 - <PERSON><PERSON>ı Trafik", "title203": "203 - <PERSON><PERSON><PERSON>", "title500": "500 - <PERSON><PERSON>", "title620": "620 - <PERSON><PERSON><PERSON>ı<PERSON>", "title608": "608 - Yabancı Sağlık Sigortası", "tckn": "TCKN/VKN/YKN", "insuranceTransactions": "Sigorta işlemleri", "plateNumber": "<PERSON><PERSON><PERSON> numa<PERSON>ı", "licenseRegistrationSerialNumber": "Ruhsat tescil sicil numarası", "additionalGuarantee": "Ek teminat", "fuelType": "Yakıt Tipi", "usage": "Kullanım Şekli", "modelYear": "Model Yılı", "brand": "<PERSON><PERSON>", "model": "Model", "engineNumber": "Motor Numarası", "chassisNumber": "<PERSON>asi <PERSON>", "getOffer": "Tek<PERSON><PERSON> Al", "PasaportNoYKNVKNTCKN": "Pasaport Numarası / YKN", "insuranceDuration": "<PERSON><PERSON><PERSON>", "hasCarPlate": "<PERSON><PERSON><PERSON> var", "placeToTravel": "Seyehat Edilecek Şehir", "vehicleTypes": "<PERSON><PERSON>", "openRequest": "Talep Açılsın", "doNotOpenRequest": "Talep Açılmasın", "authCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authMessageTitle": "Otorizasyon Mesajı", "auth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "Uyarı", "AuthMessageText": "Lütfen durumun Genel Müdürlüğe iletilmesi için Talep Oluşturunuz!"}, "houseInsurance": {"saveNewCustomer": "<PERSON><PERSON> mü<PERSON>i kaydet", "uavtQuery": "UAVT Sorgula", "uavtInquiry": "UAVT Sorgulama", "getRiskAddress": "<PERSON><PERSON><PERSON>", "riskAddress": "<PERSON><PERSON><PERSON>", "coverageLimit": "Teminat Limiti", "buildingStyle": "Yapı Tarzı", "buildingConstructionYear": "Bina İnşa Yılı", "buildingArea": "<PERSON><PERSON> (m2)", "buildingUsage": "<PERSON><PERSON>", "enterYearofConstruction": "İnşa yılı giriniz", "enterBuildingArea": "<PERSON>a <PERSON> Giriniz", "enteruavtCode": "UAVT Kodunu G<PERSON>z", "idleTime": "<PERSON><PERSON>", "enterNumberOfFloors": "<PERSON>", "numberOfFloorsExcludingBasement": "Bodrum Hariç <PERSON>ed<PERSON>", "floorCode": "Kat <PERSON>", "titleOfInsured": "Sigortalı Sıfatı", "legalProtectionLimit": "<PERSON><PERSON><PERSON><PERSON>", "selectProvince": "<PERSON><PERSON> Seç<PERSON>", "selectDistrict": "İlçe Seçiniz", "selectVillage": "<PERSON><PERSON><PERSON>", "selectNeighborhood": "<PERSON><PERSON><PERSON>", "selectStreet": "Cadde/Sokak/Bulvar/<PERSON><PERSON><PERSON>", "selectInteriorDoorNumber": "İç Kapı No Seçiniz", "selectExteriorDoorNumber": "Dış Kapı No Seçiniz", "objectCost": "<PERSON><PERSON><PERSON>", "buildingCost": "<PERSON><PERSON>"}, "foreignHealthInsurance": {"isExistingDisease": "Mevcut hastalık var mı?", "IsKvkkApproved": "KVKK onayı var mı?"}}}, "requestManagement": {"title": "<PERSON><PERSON>", "requestManagementListing": {"title": "<PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "requestReason": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "newRequest": "<PERSON><PERSON>", "requestsWaitingForMe": "<PERSON><PERSON> be<PERSON>en talepler", "requesting": "<PERSON><PERSON>", "policyType": "Poliçe Tipi", "subject": "<PERSON><PERSON>", "requestDate": "<PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "insuredNameSurname": "Sigortalı adı soyadı", "agency": "<PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON><PERSON> adı", "offerDetails": "<PERSON><PERSON><PERSON><PERSON>", "additionalInformation": "<PERSON><PERSON>", "viewOffer": "<PERSON><PERSON><PERSON><PERSON>", "additionalDocs": "<PERSON><PERSON>", "typeMessage": "<PERSON><PERSON>", "submitMessage": "<PERSON><PERSON>", "requestNumber": "<PERSON><PERSON>", "relatedUnit": "İlgili Birim", "requestId": "Talep Id", "fault": "<PERSON><PERSON>", "InsuranceQuote": "<PERSON><PERSON><PERSON>", "messaging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewRequest": "<PERSON><PERSON>", "messageHistory": "<PERSON><PERSON>", "sendNewMessage": "<PERSON><PERSON>", "requestProcesses": "Talep İşlemleri", "closeRequest": "<PERSON><PERSON>", "endRequest": "<PERSON>bi <PERSON>", "requestWillBeEnded": "Talep Sonlandırılacak!", "requestWillBeEndedPermanently": "Talep tekrar açılmamak üzere sonlandıralacak.", "fileHistory": "<PERSON><PERSON><PERSON> Geçmişi", "requestWillBeUpdated": "<PERSON><PERSON> du<PERSON>.", "requestReview": "<PERSON><PERSON>", "requestClosedDate": "<PERSON><PERSON>", "agencyCode": "<PERSON><PERSON>", "agencyName": "Acente Adı", "user": "Kullanıcı", "gmUser": "<PERSON><PERSON><PERSON><PERSON>", "requestUpdateError": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>p <PERSON>ü güncelleme için z<PERSON>lu al<PERSON>.", "exportToExcel": "Excel'e Aktar", "general": "<PERSON><PERSON>", "admin": "Admin", "messageGroup": "<PERSON><PERSON>", "includeClosedOffers": "Kapalı Talepleri Dahil Et"}, "requestManagementCreating": {"title": "Talep <PERSON>imi <PERSON>", "agentThatOpenRequest": "Talep A<PERSON>", "requestPerson": "<PERSON><PERSON>", "requestStatus": "<PERSON><PERSON>", "offerPolicyNum": "Teklif/Poliçe Numarası", "product": "<PERSON><PERSON><PERSON><PERSON>", "requestReason": "<PERSON><PERSON>", "detail": "Detay", "successCreateRequest": "Talebiniz başarıyla oluşturuldu", "updateStatus": "<PERSON><PERSON><PERSON>", "takeRequestOnMe": "Talebi Üzerime Al"}}, "messages": {"title": "<PERSON><PERSON><PERSON>", "deliveries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sender": "Mesajı Yazan", "type": "Mesaj/Duyuru <PERSON>", "summaryMessage": "<PERSON><PERSON>", "message": "<PERSON><PERSON>", "announcement": "<PERSON><PERSON><PERSON>", "messageBox": "<PERSON><PERSON>", "mainScreen": "<PERSON>", "action": {"add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "message": "<PERSON><PERSON>", "messageChannel": "Mesaj/Duyuru <PERSON>", "releaseDate": "<PERSON><PERSON><PERSON><PERSON>", "messageReleasePlace": "Mesaj/Duyuru <PERSON>ı Yer", "addPic": "<PERSON><PERSON><PERSON><PERSON>", "releaseRemoveDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "configs": {"title": "<PERSON><PERSON><PERSON>", "productManagement": {"title": "<PERSON><PERSON><PERSON><PERSON>", "productNo": "<PERSON><PERSON><PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON><PERSON>", "offerPrint": "<PERSON><PERSON><PERSON><PERSON>", "policyPrint": "Poliçe Basımı", "receiptPrint": "Makbuz Basımı", "b2b": "B2B", "b2c": "B2C", "releaseDate": "<PERSON><PERSON><PERSON><PERSON>", "endDate": "Bitiş Tarihi", "status": "Durum", "publishedProducts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "agencies": "Acentelar", "published": "Ya<PERSON><PERSON><PERSON>da", "notPublished": "<PERSON><PERSON><PERSON><PERSON>da <PERSON>", "action": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "registerOnProposal": "<PERSON><PERSON><PERSON>", "informingPrint": "Bilgilendirme Formu", "productViewingAgency": "Ürünleri Görüntüleyen Acenteler", "productViewingAgencyUsers": "Ürünleri Görüntüleyen Acente Kullanıcıları"}}, "unitManagement": {"title": "<PERSON><PERSON><PERSON>", "unitName": "<PERSON><PERSON><PERSON>", "unitMail": "Birim E-Postası", "unitTel": "Birim Telefonu", "isActive": "<PERSON><PERSON>m <PERSON>/Aktif", "action": {"add": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>"}}, "agencyManagement": {"title": "<PERSON><PERSON>", "agencyName": "Acente Adı", "agencyCode": "<PERSON><PERSON>", "agencySelection": "<PERSON><PERSON>", "addAgency": "<PERSON><PERSON>", "editAgency": "<PERSON><PERSON>", "selectedAgencies": "Seçilen <PERSON>"}}, "profile": {"title": "Profilim", "profileInformation": {"title": "<PERSON><PERSON>", "profilePhoto": "Profil Fotoğrafı", "upload": "<PERSON><PERSON><PERSON>"}, "changePassword": {"title": "<PERSON><PERSON><PERSON><PERSON>", "currentPassword": "Mevcut Şifreniz", "newPassword": "<PERSON><PERSON>", "confirmationPassword": "Yeni Şifre Te<PERSON>rı", "doNotMatchPassword": "Girdiğiniz şifreler eşleşmiyor!"}}, "auth": {"loginSignTitle": "<PERSON><PERSON><PERSON>", "loginSignTitleDesc": "Sigortacılık İşlemleri İçin Giriş Yapınız.", "logout": "<PERSON><PERSON><PERSON><PERSON>", "userinformations": "Kullanıcı Bilgileri", "forgotPasswordTitle": "Şif<PERSON><PERSON>", "forgotPasswordTitleDesc": "Lütfen Mail Adresinizi Giriniz", "activationMail": "Aktivasyon Maili Gönder", "doYouHaveAccount": "Kullanmış olduğunuz aktif bir hesabınız mı var?", "changePasswordTitle": "Şifre <PERSON>ırl<PERSON>", "changePasswordTitleDesc": "E-posta adresinize şifre sıfırlama kodu içeren bir e-posta gönderilmiştir. Yeni şifrenizi belirlemek için lütfen aşağıdaki bölümleri doldurun.", "changePassword": "<PERSON><PERSON><PERSON>"}, "error": {"invalid_username_or_password": "Geçersiz Kullanıcı Adı veya Şifre.", "licenseRegistrationSerialNumberValidation": "License registration number must be six digit number", "enterValue": "<PERSON><PERSON> <PERSON> giri<PERSON>", "passportNotValid": "Pasaport geçerli değil", "enterValidName": "Lütfen geçerli bir isim giri<PERSON>.", "enterValidSurName": "Lütfen geçerli bir soyisim giriniz.", "yknNotValid": "ykn geç<PERSON>li değil ", "reCaptchaFailed": "Robot kontrolü yapılırken bir hata oluştu.", "phoneNum10": "Telefon numarası en az 10 haneli olmalıdır!", "chassisNum17": "Şasi numarası en az 17 karakter uzunluğunda olmalıdır", "tcknoValid": "tckno ge<PERSON><PERSON><PERSON>", "creditCardNotNumValid": "Kredi kartı numarası geçerli değil", "informationFormPrintingFirstToBeDownloaded": "Bilgilendirme Formu indirilmeden Teklif/Poliçe ve Tahsilat Makbuzu hem indirilememeli hem de mail olarak iletilememelidir.", "filterRuleOfferlisting": "Lütfen Durum alanı  ile birlikte bir kriter daha belirley<PERSON>z."}}