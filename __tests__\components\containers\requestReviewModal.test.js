import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { Form } from "antd";
import RequestReviewModal from "@/components/containers/requestReviewModal";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { useResponseHandler } from "@/components/tools";
import {
  getPortalOfferRequestReasons,
  getPortalOfferRequestUnits,
} from "@/services/requests";
import { postPrintDocument } from "@/services/insurance";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";

jest.mock("@/components/contexts/modalLoginContext");
jest.mock("@/components/contexts/userContext");
jest.mock("@/components/tools");
jest.mock("@/services/requests");
jest.mock("@/services/insurance");

const mockOpen = jest.fn();
window.open = mockOpen;

jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    Form: {
      ...antd.Form,
      useForm: () => [
        {
          getFieldsValue: () => ({}),
          setFieldsValue: jest.fn(),
          validateFields: jest.fn(),
          resetFields: jest.fn(),
        },
      ],
    },
  };
});

const mockDict = {
  public: {
    error: "Error",
    required: "Required",
    requiredField: "Required Field",
    status: "Status",
  },
  requestManagement: {
    requestManagementListing: {
      requestNumber: "Request Number",
      unit: "Unit",
      requestDate: "Request Date",
      requestClosedDate: "Request Closed Date",
      subject: "Subject",
    },
    requestManagementCreating: {
      requestReason: "Request Reason",
      product: "Product",
      offerPolicyNum: "Policy Number",
      detail: "Detail",
      updateStatus: "Update Status",
    },
  },
};

const mockSelectedData = {
  id: "123",
  unit: "Test Unit",
  requestReason: "Test Reason",
  productName: "Test Product",
  policyNo: "12345",
  requestDate: "2024-03-20",
  subject: "Test Subject",
  detail: "Test Detail",
  status: "OPEN",
  productNo: "1",
};

describe("RequestReviewModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Dil: ${lang}`, () => {
      let dict, mockForm;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockForm = Form.useForm()[0];

        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: "1" } });
        useResponseHandler.mockReturnValue({
          handleResponse: jest.fn(),
          handleError: jest.fn(),
        });

        getPortalOfferRequestUnits.mockResolvedValue({
          status: "SUCCESS",
          data: ["Unit 1", "Unit 2"],
        });

        getPortalOfferRequestReasons.mockResolvedValue({
          status: "SUCCESS",
          data: ["Reason 1", "Reason 2"],
        });
      });

      afterEach(() => {
        jest.clearAllMocks();
      });

      test("modal:Başlığı ve içeriği doğru şekilde render edilmeli", async () => {
        await act(async () => {
          render(
            <Form>
              <RequestReviewModal
                dict={dict}
                lang={lang}
                selectedData={mockSelectedData}
                form={mockForm}
              />
            </Form>
          );
        });

        expect(screen.getByText(mockSelectedData.id)).toBeInTheDocument();
        expect(
          screen.getByText(mockSelectedData.productName)
        ).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.policyNo)).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.subject)).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.detail)).toBeInTheDocument();
      });

      test("modal:Birim ve talep nedeni seçimleri doğru şekilde yüklenmeli", async () => {
        await act(async () => {
          render(
            <Form>
              <RequestReviewModal
                dict={dict}
                lang={lang}
                selectedData={mockSelectedData}
                form={mockForm}
              />
            </Form>
          );
        });

        await waitFor(() => {
          expect(getPortalOfferRequestUnits).toHaveBeenCalledWith(lang, {
            productNo: mockSelectedData.productNo,
          });
        });
      });

      test("modal:Belge yazdırma işlemi doğru şekilde çalışmalı", async () => {
        const mockPrintResponse = {
          status: "SUCCESS",
          data: {
            certificate: btoa("test-pdf-content"),
          },
        };

        postPrintDocument.mockResolvedValue(mockPrintResponse);
        global.URL.createObjectURL = jest.fn();

        await act(async () => {
          render(
            <Form>
              <RequestReviewModal
                dict={dict}
                lang={lang}
                selectedData={mockSelectedData}
                form={mockForm}
              />
            </Form>
          );
        });

        const policyLink = screen.getByText(mockSelectedData.policyNo);
        await act(async () => {
          fireEvent.click(policyLink);
        });

        await waitFor(() => {
          expect(postPrintDocument).toHaveBeenCalledWith(lang, {
            productNo: mockSelectedData.productNo,
            policyNumber: mockSelectedData.policyNo,
            printType: 1,
          });
          expect(mockOpen).toHaveBeenCalled();
        });
      });

      test("modal:API çağrıları başarısız olduğunda hata durumu gösterilmeli", async () => {
        const mockError = new Error("API Error");
        getPortalOfferRequestUnits.mockRejectedValue(mockError);

        const { handleError } = useResponseHandler();

        await act(async () => {
          render(
            <Form>
              <RequestReviewModal
                dict={dict}
                lang={lang}
                selectedData={mockSelectedData}
                form={mockForm}
              />
            </Form>
          );
        });

        await waitFor(() => {
          expect(handleError).toHaveBeenCalledWith(
            mockError,
            dict.public.error
          );
        });
      });

      test("modal:API başarısız yanıt döndüğünde units array'i temizlenmeli", async () => {
        getPortalOfferRequestUnits.mockResolvedValue({
          status: "ERROR",
          data: null,
        });

        await act(async () => {
          render(
            <Form>
              <RequestReviewModal
                dict={dict}
                lang={lang}
                selectedData={mockSelectedData}
                form={mockForm}
              />
            </Form>
          );
        });

        await waitFor(() => {
          expect(getPortalOfferRequestUnits).toHaveBeenCalledWith(lang, {
            productNo: mockSelectedData.productNo,
          });
        });

        const unitSelect = screen.getByTestId("unit-select");
        const options = unitSelect.querySelectorAll("option");
        expect(options.length).toBe(0);
      });
    });
  });
});
