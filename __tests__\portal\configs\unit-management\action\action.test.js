import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  getUnitManagementSingle,
  postUnitManagement,
  putUnitManagement,
} from "@/services/unitManagement";
import { useRouter, useSearchParams } from "next/navigation";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import Action from "@/app/[lang]/portal/configs/unit-management/action/action";

// Mock tanımlamaları
jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/unitManagement", () => ({
  getUnitManagementSingle: jest.fn(),
  postUnitManagement: jest.fn(),
  putUnitManagement: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

describe("Action", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter, mockSearchParams;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        mockRouter = { push: jest.fn() };
        useRouter.mockReturnValue(mockRouter);
        mockSearchParams = new URLSearchParams();
        useSearchParams.mockReturnValue(mockSearchParams);

        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("form:Text alanlar doğru olmalı.", () => {
        const texts = [
          dict.configs.unitManagement.action.add,
          dict.configs.unitManagement.unitName,
          dict.configs.unitManagement.unitMail,
          dict.configs.unitManagement.unitTel,
          dict.configs.unitManagement.isActive,
          dict.public.back,
          dict.public.save,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("form:Form bileşenleri doğru şekilde render edilmeli.", () => {
        expect(screen.getByTestId("unitName")).toBeInTheDocument();
        expect(screen.getByTestId("unitMail")).toBeInTheDocument();
        expect(screen.getByTestId("unitTel")).toBeInTheDocument();
        expect(screen.getByTestId("isActive")).toBeInTheDocument();
        expect(screen.getByTestId("backButton")).toBeInTheDocument();
        expect(screen.getByTestId("saveButton")).toBeInTheDocument();
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region API kontrolü
      test("api:Edit modunda ilk yükleme sırasında API fonksiyonu çağrılmalı.", async () => {
        mockSearchParams.set("id", "1");
        getUnitManagementSingle.mockResolvedValueOnce({
          status: "SUCCESS",
          data: {
            id: 1,
            unitName: "Test Unit",
            unitMail: "<EMAIL>",
            unitTel: "1234567890",
            isActive: 1,
          },
        });

        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        await waitFor(() => {
          expect(getUnitManagementSingle).toHaveBeenCalledWith(lang, "1");
        });
      });
      // #endregion API kontrolü

      // #region Form işlevselliği kontrolü

      test("form:Yeni birim ekleme işlemi başarılı olmalı.", async () => {
        postUnitManagement.mockResolvedValueOnce({
          status: "SUCCESS",
        });

        // Form.Item'ları seç
        const unitNameFormItems = screen.getAllByTestId("unitName");
        const unitMailFormItems = screen.getAllByTestId("unitMail");
        const unitTelFormItems = screen.getAllByTestId("unitTel");
        const isActiveFormItems = screen.getAllByTestId("isActive");

        // İlk Form.Item'ları seç (boş olanlar)
        const unitNameFormItem = unitNameFormItems[0];
        const unitMailFormItem = unitMailFormItems[0];
        const unitTelFormItem = unitTelFormItems[0];
        const isActiveFormItem = isActiveFormItems[0];

        // Input'ları seç
        const unitNameInput = unitNameFormItem.querySelector("input");
        const unitMailInput = unitMailFormItem.querySelector("input");
        const unitTelInput = unitTelFormItem.querySelector("input");
        const isActiveSwitch = isActiveFormItem.querySelector("button");

        await act(async () => {
          fireEvent.change(unitNameInput, { target: { value: "Test Unit" } });
          fireEvent.change(unitMailInput, {
            target: { value: "<EMAIL>" },
          });
          fireEvent.change(unitTelInput, { target: { value: "1234567890" } });
          fireEvent.click(isActiveSwitch);
        });

        await act(async () => {
          const saveButtons = screen.getAllByTestId("saveButton");
          fireEvent.click(saveButtons[0]);
        });

        await waitFor(() => {
          expect(postUnitManagement).toHaveBeenCalledWith(
            lang,
            expect.objectContaining({
              id: 0,
              unitName: "Test Unit",
              unitMail: "<EMAIL>",
              unitTel: "1234567890",
              isActive: 1,
            })
          );
          expect(mockRouter.push).toHaveBeenCalledWith(
            `/${lang}/portal/configs/unit-management/`
          );
        });
      });

      test("form:Geri butonu doğru sayfaya yönlendirmeli.", async () => {
        await act(async () => {
          const backButtons = screen.getAllByTestId("backButton");
          fireEvent.click(backButtons[0]);
        });
        expect(mockRouter.push).toHaveBeenCalledWith(
          `/${lang}/portal/configs/unit-management/`
        );
      });

      test("form:Zorunlu alanlar boş bırakıldığında hata mesajı gösterilmeli.", async () => {
        await act(async () => {
          const saveButtons = screen.getAllByTestId("saveButton");
          fireEvent.click(saveButtons[0]);
        });

        // Form hata mesajlarını kontrol et
        const errorInputs = screen.getAllByTestId(/unitName|unitMail|unitTel/);
        expect(errorInputs.length).toBeGreaterThan(0);
        errorInputs.forEach((formItem) => {
          const input = formItem.querySelector("input");
          expect(input).toHaveAttribute("aria-invalid", "true");
        });
      });
      // #endregion Form işlevselliği kontrolü
    });
  });
});
