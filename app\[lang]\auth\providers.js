"use client";

import { ConfigProvider } from "antd";
import { createCache, extractStyle, StyleProvider } from "@ant-design/cssinjs";
import { useMemo, useRef } from "react";
import { useServerInsertedHTML, usePathname } from "next/navigation";
import { AppProgressBar as ProgressBar } from "next-nprogress-bar";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";

export function ThemeProviders({ children, fontFamily }) {
  const cache = useMemo(() => createCache(), []);
  const isServerInserted = useRef(false);
  const pathname = usePathname();
  const language = pathname.split("/")[1];
  useServerInsertedHTML(() => {
    if (isServerInserted.current) {
      return;
    }
    isServerInserted.current = true;
    return (
      <style
        id="auth"
        dangerouslySetInnerHTML={{ __html: extractStyle(cache, true) }}
      />
    );
  });

  return (
    <StyleProvider hashPriority="high" cache={cache}>
      <ConfigProvider
        theme={{
          token: {
            fontFamily: fontFamily,
            colorPrimary: "#84754e",
            controlHeight: 46,
            borderRadius: 8,
            colorBgBase: "#F9FAFB",
            colorBorder: "#D1D5DB",
          },
          components: {
            Button: {
              fontWeight: 700,
              lineHeight: 1.25,
              fontSize: 16,
              controlHeight: 46,
              controlOutlineWidth: 0,
              controlOutline: 0,
            },
            Input: {
              controlOutlineWidth: 0,
              controlOutline: 0,
            },
          },
        }}
      >
        <GoogleReCaptchaProvider
          language={language}
          reCaptchaKey={process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHAV3_SITE_KEY}
        >
          {children}
        </GoogleReCaptchaProvider>
        <ProgressBar
          height="2px"
          color="#84754e"
          options={{ showSpinner: false }}
          shallowRouting
        />
      </ConfigProvider>
    </StyleProvider>
  );
}
