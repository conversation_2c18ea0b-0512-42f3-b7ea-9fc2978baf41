import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  cleanup,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import {
  postAddUpdateCrossProductSettings,
  getCrossProductSettings,
} from "@/services/productManagement";
import CrossProductSettingsModal from "@/app/[lang]/portal/configs/product-management/crossProductSettingsModal";

// Mock dependencies
jest.mock("@/services/productManagement", () => ({
  postAddUpdateCrossProductSettings: jest.fn(),
  getCrossProductSettings: jest.fn(),
}));

jest.mock("@/components/tools", () => ({
  useResponseHandler: jest.fn(() => ({
    handleResponse: jest.fn(),
    handleError: jest.fn(),
  })),
  getLocale: { en: "en", tr: "tr" },
  getSpinIndicator: <div data-testid="spinner">Loading...</div>,
}));

describe("CrossProductSettingsModal", () => {
  const mockSetIsModalOpen = jest.fn();
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();

  // Mock useResponseHandler
  const mockUseResponseHandler =
    require("@/components/tools").useResponseHandler;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseResponseHandler.mockReturnValue({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    });
  });

  afterEach(() => {
    cleanup();
  });

  const createDefaultProps = (dict) => ({
    dict,
    lang: "en",
    isModalOpen: true,
    setIsModalOpen: mockSetIsModalOpen,
    selectedData: {
      productNo: "PROD001",
      productName: "Test Product",
    },
    products: [
      { key: "PROD001", value: "Test Product" },
      { key: "PROD002", value: "Product Two" },
      { key: "PROD003", value: "Product Three" },
    ],
  });

  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, defaultProps;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        defaultProps = createDefaultProps(dict);
      });

      describe("Modal Rendering", () => {
        test("should render modal when isModalOpen is true", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          expect(
            screen.getByText("PROD001 - Test Product")
          ).toBeInTheDocument();
          expect(
            screen.getByText(dict.configs.productManagement.setupCrossSelling)
          ).toBeInTheDocument();
          expect(
            screen.getByText(dict.configs.productManagement.productsToCrossSell)
          ).toBeInTheDocument();
        });

        test("should not render modal content when isModalOpen is false", () => {
          const closedModalProps = {
            ...defaultProps,
            isModalOpen: false,
          };

          render(<CrossProductSettingsModal {...closedModalProps} />);

          expect(
            screen.queryByText("PROD001 - Test Product")
          ).not.toBeInTheDocument();
        });

        test("should render close button and handle click", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          const closeButton = screen.getByRole("img", { name: /close/i });
          fireEvent.click(closeButton);

          expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
        });
      });

      describe("Form Components", () => {
        test("should render cross sale switch", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          expect(screen.getByTestId("crossSale")).toBeInTheDocument();
        });

        test("should render product checkboxes excluding selected product", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          // Should render PROD002 and PROD003 but not PROD001 (selected product)
          expect(screen.getByTestId("PROD002")).toBeInTheDocument();
          expect(screen.getByTestId("PROD003")).toBeInTheDocument();
          expect(screen.queryByTestId("PROD001")).not.toBeInTheDocument();
        });

        test("should render save button", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          expect(screen.getByTestId("saveButton")).toBeInTheDocument();
          expect(screen.getByText(dict.public.save)).toBeInTheDocument();
        });
      });

      describe("API Integration", () => {
        test("should call getCrossProductSettings on mount", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          expect(getCrossProductSettings).toHaveBeenCalledWith(lang, "PROD001");
        });

        test("should populate form with existing cross product data", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: ["PROD002", "PROD003"],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          // await waitFor(() => {
          //   // Switch should be enabled when data exists
          //   const switchElement = screen.getByTestId("crossSale");
          //   expect(switchElement.querySelector("input")).toBeChecked();
          // });
        });
      });

      describe("Form Functionality", () => {
        test("should enable checkboxes when switch is turned on", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          const switchElement = screen
            .getByTestId("crossSale")
            .querySelector("button");

          await act(async () => {
            fireEvent.click(switchElement);
          });

          // Checkboxes should be enabled
          const checkbox1 = screen
            .getByTestId("PROD002")
            .querySelector("input");
          const checkbox2 = screen
            .getByTestId("PROD003")
            .querySelector("input");

          expect(checkbox1).not.toBeDisabled();
          expect(checkbox2).not.toBeDisabled();
        });

        // test("should disable checkboxes when switch is turned off", async () => {
        //   getCrossProductSettings.mockResolvedValue({
        //     status: "SUCCESS",
        //     data: ["PROD002"],
        //   });

        //   await act(async () => {
        //     render(<CrossProductSettingsModal {...defaultProps} />);
        //   });

        //   const switchElement = screen
        //     .getByTestId("crossSale")
        //     .querySelector("button");

        //   await act(async () => {
        //     fireEvent.click(switchElement);
        //   });

        //   // Checkboxes should be disabled
        //   const checkbox1 = screen
        //     .getByTestId("PROD002")
        //     .querySelector("input");
        //   const checkbox2 = screen
        //     .getByTestId("PROD003")
        //     .querySelector("input");

        //   expect(checkbox1).toBeDisabled();
        //   expect(checkbox2).toBeDisabled();
        // });

        test("should handle form submission successfully", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });
          postAddUpdateCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
          });

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          // Enable switch and select products
          const switchElement = screen
            .getByTestId("crossSale")
            .querySelector("button");
          await act(async () => {
            fireEvent.click(switchElement);
          });

          const checkbox1 = screen
            .getByTestId("PROD002")
            .querySelector("input");
          await act(async () => {
            fireEvent.click(checkbox1);
          });

          const saveButton = screen.getByTestId("saveButton");
          await act(async () => {
            fireEvent.click(saveButton);
          });

          // await waitFor(() => {
          //   expect(postAddUpdateCrossProductSettings).toHaveBeenCalledWith(
          //     lang,
          //     {
          //       products: ["PROD002"],
          //       productNo: "PROD001",
          //     }
          //   );
          //   expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
          // });
        });

        test("should handle form submission error", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });
          const mockError = new Error("API Error");
          postAddUpdateCrossProductSettings.mockRejectedValue(mockError);

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          const saveButton = screen.getByTestId("saveButton");
          await act(async () => {
            fireEvent.click(saveButton);
          });

          await waitFor(() => {
            expect(mockHandleError).toHaveBeenCalledWith(
              mockError,
              dict.public.error
            );
          });
        });

        test("should show loading spinner during API calls", async () => {
          getCrossProductSettings.mockImplementation(
            () =>
              new Promise((resolve) =>
                setTimeout(() => resolve({ status: "SUCCESS", data: [] }), 100)
              )
          );

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          //expect(screen.getByTestId("spinner")).toBeInTheDocument();
        });

        test("should handle getCrossProductSettings error", async () => {
          const mockError = new Error("Get API Error");
          getCrossProductSettings.mockRejectedValue(mockError);

          await act(async () => {
            render(<CrossProductSettingsModal {...defaultProps} />);
          });

          await waitFor(() => {
            expect(mockHandleError).toHaveBeenCalledWith(
              mockError,
              dict.public.error
            );
          });
        });
      });

      describe("Data Processing", () => {
        test("should filter out selected product from products list", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          const customProps = {
            ...defaultProps,
            products: [
              { key: "PROD001", value: "Selected Product" },
              { key: "PROD002", value: "Product Two" },
              { key: "PROD003", value: "Product Three" },
            ],
            selectedData: {
              productNo: "PROD001",
              productName: "Selected Product",
            },
          };

          await act(async () => {
            render(<CrossProductSettingsModal {...customProps} />);
          });

          // Should not render the selected product checkbox
          expect(screen.queryByTestId("PROD001")).not.toBeInTheDocument();
          expect(screen.getByTestId("PROD002")).toBeInTheDocument();
          expect(screen.getByTestId("PROD003")).toBeInTheDocument();
        });

        test("should handle empty products list", async () => {
          getCrossProductSettings.mockResolvedValue({
            status: "SUCCESS",
            data: [],
          });

          const customProps = {
            ...defaultProps,
            products: [{ key: "PROD001", value: "Only Product" }],
            selectedData: {
              productNo: "PROD001",
              productName: "Only Product",
            },
          };

          await act(async () => {
            render(<CrossProductSettingsModal {...customProps} />);
          });

          // Should render the modal but no product checkboxes
          expect(
            screen.getByText(dict.configs.productManagement.productsToCrossSell)
          ).toBeInTheDocument();
          expect(screen.queryByTestId("PROD001")).not.toBeInTheDocument();
        });
      });
    });
  });
});
