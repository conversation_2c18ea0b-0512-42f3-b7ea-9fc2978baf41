import { fireEvent, render, waitFor } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { postUsersForgotPassword } from "@/services/identityConnect";
import ForgotPasswordForm from "@/app/[lang]/auth/forgot-password/forgotPasswordForm";

jest.mock("@/services/identityConnect", () => ({
  postUsersForgotPassword: jest.fn(),
}));

jest.mock("react-google-recaptcha-v3", () => ({
  useGoogleReCaptcha: () => ({
    executeRecaptcha: jest.fn(() => Promise.resolve("recaptcha-token")),
  }),
}));

describe("ForgotPasswordForm", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, forgotPasswordForm;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        forgotPasswordForm = render(
          <ForgotPasswordForm dict={dict} lang={lang} />
        );
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("Sayfa başlık içermeli.", () => {
        const { container } = forgotPasswordForm;
        const heading = container.querySelector("h1");
        expect(heading).toBeInTheDocument();
        expect(heading.textContent).toBe(dict.auth.forgotPasswordTitle);
      });

      test("input:email olmalı ve placeholder içermeli.", () => {
        const { getByPlaceholderText } = forgotPasswordForm;
        const email = getByPlaceholderText(dict.public.username);
        expect(email).toBeInTheDocument();
      });

      test(`button:submit olmalı ve başlık içermeli. ${lang}`, () => {
        const { getByTestId } = forgotPasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        expect(buttonSubmit).toBeInTheDocument();
        expect(buttonSubmit.textContent).toBe(dict.auth.activationMail);
      });

      test(`link:authLogin olmalı, doğru url ve başlık içermeli.`, () => {
        const { getByTestId } = forgotPasswordForm;
        const linkAuthLogin = getByTestId("linkAuthLogin");
        expect(linkAuthLogin).toBeInTheDocument();
        expect(linkAuthLogin).toHaveAttribute("href", `/${lang}/auth/login`);
        expect(linkAuthLogin.textContent).toBe(dict.auth.loginSignTitle);
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region Form validasyon kontrolü
      test(`form:email alanı boş geçilemez olmalı ve hata mesajı doğrulanmalı.`, async () => {
        const { getByTestId } = forgotPasswordForm;
        const buttonSubmit = getByTestId("buttonSubmit");
        fireEvent.click(buttonSubmit);
        await waitFor(() => {
          const email = getByTestId("email");
          expect(email).toBeInTheDocument();
          expect(email.textContent).toBe(dict.public.requiredField);
        });
      });

      test("form:Geçerli email girildiğinde ChangePasswordForm görünmeli.", async () => {
        postUsersForgotPassword.mockResolvedValueOnce({ code: "SUCCESS" });
        const { getByPlaceholderText, getByTestId, findByText } =
          forgotPasswordForm;
        fireEvent.change(getByPlaceholderText(dict.public.username), {
          target: { value: "<EMAIL>" },
        });
        fireEvent.click(getByTestId("buttonSubmit"));
        await waitFor(
          async () => {
            const title = await findByText(dict.auth.changePasswordTitle);
            expect(title).toBeInTheDocument();
          },
          { timeout: 3000 }
        );
      });

      test("form:Geçersiz email pattern'ı hata vermeli.", async () => {
        const { getByPlaceholderText, getByTestId, findByText } =
          forgotPasswordForm;
        fireEvent.change(getByPlaceholderText(dict.public.username), {
          target: { value: "invalid-mail" },
        });
        fireEvent.click(getByTestId("buttonSubmit"));
        const errorMessage = await findByText(dict.public.requiredField);
        expect(errorMessage).toBeInTheDocument();
      });
      // #endregion Form validasyon kontrolü
    });
  });
});
