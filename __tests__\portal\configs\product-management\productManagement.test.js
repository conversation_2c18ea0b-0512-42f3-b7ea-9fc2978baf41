import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { getProductManagement } from "@/services/productManagement";
import { getActiveProducts } from "@/services/product";
import { useRouter } from "next/navigation";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import ProductManagement from "@/app/[lang]/portal/configs/product-management/productManagement";

jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/productManagement", () => ({
  getProductManagement: jest.fn(),
}));

jest.mock("@/services/product", () => ({
  getActiveProducts: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

describe("ProductManagement", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter;

      // #region Sayfa render olmadan önceki tanımlamalar
      beforeAll(async () => {
        dict = await getDictionary(lang);
        const originalQuerySelector = document.querySelectorAll;
        document.querySelectorAll = function (selector) {
          if (selector.includes(".ant-select-item-option-selected:not")) {
            return [];
          }
          return originalQuerySelector.call(this, selector);
        };
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        getProductManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 2,
        });
        getActiveProducts.mockResolvedValue({ status: "SUCCESS", data: [] });
        mockRouter = { push: jest.fn() };
        useRouter.mockReturnValue(mockRouter);

        await act(async () => {
          render(<ProductManagement dict={dict} lang={lang} />);
        });
      });
      //#endregion Sayfa render olmadan önceki tanımlamalar

      // #region Sayfa bileşenlerinin kontrolü
      test("form:Text alanlar doğru olmalı.", () => {
        const texts = [
          dict.configs.productManagement.productNo,
          dict.configs.productManagement.productName,
          dict.configs.productManagement.offerPrint,
          dict.configs.productManagement.policyPrint,
          dict.configs.productManagement.receiptPrint,
          dict.configs.productManagement.b2b,
          dict.configs.productManagement.b2c,
          dict.configs.productManagement.releaseDate,
          dict.configs.productManagement.endDate,
          dict.configs.productManagement.status,
          dict.public.action,
          dict.public.add,
          dict.public.clean,
          dict.public.filter,
          dict.public.startDate,
          dict.public.endDate,
          dict.public.products,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("form:Bileşenler filtre bloğunda bulunmalı.", async () => {
        expect(screen.getByTestId("productNo")).toBeInTheDocument();
        expect(screen.getByTestId("begDate")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("b2b")).toBeInTheDocument();
        expect(screen.getByTestId("b2c")).toBeInTheDocument();
        expect(screen.getByTestId("publishedProducts")).toBeInTheDocument();
        expect(screen.getByTestId("addButton")).toBeInTheDocument();
        expect(screen.getByTestId("resetButton")).toBeInTheDocument();
        expect(screen.getByTestId("filterButton")).toBeInTheDocument();
      });
      // #endregion Sayfa bileşenlerinin kontrolü

      // #region API kontrolü
      test("api:İlk yükleme sırasında API fonksiyonları çağırılmalı.", async () => {
        await waitFor(() => {
          expect(getProductManagement).toHaveBeenCalled();
          expect(getActiveProducts).toHaveBeenCalled();
        });
      });
      // #endregion API kontrolü

      // #region Form işlevselliği kontrolü
      test("filter:Filtre formu submit edildiğinde ürünleri filtrelemeli.", async () => {
        getProductManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 0,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.filter));
        });
        await waitFor(() => {
          expect(getProductManagement).toHaveBeenCalledTimes(2);
        });
      });

      test("form:Filtre butonu form değerlerini doğru şekilde göndermeli.", async () => {
        const { getByTestId } = screen;
        const filterButton = getByTestId("filterButton");
        const b2bSwitch = getByTestId("b2b");
        await act(async () => {
          fireEvent.click(b2bSwitch.querySelector("button"));
        });
        await act(async () => {
          fireEvent.click(filterButton);
        });
        await waitFor(() => {
          expect(getProductManagement).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
              b2b: 1,
              b2c: 0,
              publishedProducts: 0,
            })
          );
        });
      });

      test("table:Tabloya veri yüklenmeli.", async () => {
        jest.clearAllMocks();
        const data = Array.from({ length: 10 }, (_, index) => ({
          productNo: index,
          name: "Product" + index,
        }));
        getProductManagement.mockResolvedValueOnce({
          status: "SUCCESS",
          data: data,
          totalNumberOfRecords: 10,
        });
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.filter));
        });
        await waitFor(() => {
          expect(getProductManagement).toHaveBeenCalledTimes(1);
        });
      });

      test("link:Yeni ekle butonu doğru sayfaya yönlendirmeli.", async () => {
        await act(async () => {
          fireEvent.click(screen.getByText(dict.public.add));
        });
        expect(mockRouter.push).toHaveBeenCalledWith(
          `/${lang}/portal/configs/product-management/action/`
        );
      });

      test("form:Reset butonu form alanlarını temizlemeli.", async () => {
        const { getByTestId } = screen;
        const resetButton = getByTestId("resetButton");
        const b2bSwitch = getByTestId("b2b");

        await act(async () => {
          fireEvent.click(b2bSwitch.querySelector("button"));
        });

        await act(async () => {
          fireEvent.click(resetButton);
        });

        const switchButton = b2bSwitch.querySelector("button");
        expect(switchButton).toHaveAttribute("aria-checked", "false");
      });
      // #endregion Form işlevselliği kontrolü
    });
  });
});
