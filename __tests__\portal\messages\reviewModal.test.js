import { render, screen, fireEvent } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import ReviewModal from "@/app/[lang]/portal/messages/reviewModal";

describe("ReviewModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;
      let mockSetIsModalOpen;
      let mockSelectedData;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockSetIsModalOpen = jest.fn();
        mockSelectedData = {
          userName: "Test User",
          type: "Test Type",
          broadcastDate: "2024-03-20",
          broadcastChannel: "Test Channel",
          title: "Test Title",
          content: "Test Content",
        };
      });

      // #region Modal bileşenlerinin kontrolü
      test("modal:Başlığı ve içeriği doğru şekilde render edilmeli.", () => {
        render(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );

        expect(
          screen.getByText(
            dict.requestManagement.requestManagementListing.reviewRequest
          )
        ).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.userName)).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.type)).toBeInTheDocument();
        expect(
          screen.getByText(mockSelectedData.broadcastChannel)
        ).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.title)).toBeInTheDocument();
        expect(screen.getByText(mockSelectedData.content)).toBeInTheDocument();
      });

      test("modal:Kapama butonu çalışmalı.", () => {
        render(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );

        const closeButton = screen.getByRole("img", { name: "close" });
        fireEvent.click(closeButton);
        expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
      });

      test("modal:Açık/kapalı durumu doğru şekilde kontrol edilmeli.", () => {
        const { rerender } = render(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );
        expect(screen.getByRole("dialog")).toBeInTheDocument();

        rerender(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={false}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );
        expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      });

      test("modal:Tarih formatı doğru şekilde gösterilmeli.", () => {
        render(
          <ReviewModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
          />
        );

        // Tarih alanının varlığını kontrol et
        const dateLabel = screen.getByText(dict.messages.action.releaseDate);
        expect(dateLabel).toBeInTheDocument();

        // Tarih içeriğinin boş olmadığını kontrol et
        const dateCell = dateLabel
          .closest("tr")
          .querySelector(".ant-descriptions-item-content");
        expect(dateCell).toBeInTheDocument();
        expect(dateCell.textContent).not.toBe("");
      });
      // #endregion Modal bileşenlerinin kontrolü
    });
  });
});
