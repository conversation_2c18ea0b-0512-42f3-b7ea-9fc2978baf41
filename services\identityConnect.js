"use server";

import { cookies } from "next/headers";
import {
  isSiteVerified,
  getAcceptLanguage,
  getToken,
  validResponse,
  encode,
  logError,
} from "@/components/ssrTools";

const postConnectToken = async (values, lang) => {
  let isWebsiteVerified = false;
  if (
    process.env.APP_ENV !== "prod" &&
    values?.username === "<EMAIL>"
  ) {
    console.log("burda");
    isWebsiteVerified = true;
  } else {
    isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);
  }

  if (isWebsiteVerified) {
    let fResponse;
    const formData = new URLSearchParams();
    formData.append("client_id", process.env.IDENTITY_CLIENT_ID);
    formData.append("client_secret", process.env.IDENTITY_CLIENT_SECRET);
    formData.append("grant_type", process.env.IDENTITY_GRANT_TYPE);
    formData.append("username", values.username);
    formData.append("password", values.password);
    formData.append("scope", process.env.IDENTITY_SCOPE);

    const request = {
      url: `${process.env.IDENTITY_API_URL}connect/token`,
      options: {
        cache: "no-store",
        method: "POST",
        headers: {
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: formData,
      },
    };

    try {
      fResponse = await fetch(request.url, request.options);
      const response = await fResponse.json();
      if (response?.error) {
        response.error = true;
        return response;
      } else {
        cookies().set("token", response?.access_token);
        const base64Token = response?.access_token.split(".")[1];
        const decodedToken = base64Token
          ? JSON.parse(
              Buffer.from(
                base64Token.replace("-", "+").replace("_", "/"),
                "base64"
              ).toString("utf-8")
            )
          : null;
        const user = {
          name: decodedToken?.name,
          email: decodedToken?.email,
        };
        const encodedUser = {};
        for (const key in user) {
          if (user.hasOwnProperty(key)) {
            const encodedKey = encode(key); // Anahtarı kodla
            const encodedValue = encode(user[key]); // Değeri kodla
            encodedUser[encodedKey] = encodedValue;
          }
        }
        const cookieValue = JSON.stringify(encodedUser);
        cookies().set("user", cookieValue);
        return response;
      }
    } catch (err) {
      logError("postConnectToken", "fetchFailed", request, fResponse, err);
      throw err;
    }
  } else {
    const filteredValues = { ...values };
    delete filteredValues.password;
    logError(
      "postConnectToken",
      "reCaptchaFailed",
      filteredValues,
      undefined,
      undefined
    );
    return {
      status: "ERROR",
      error: true,
      error_description: "reCaptchaFailed",
    };
  }
};

const setLogout = async (lang) => {
  const res = await postUserLogOut(lang);
  cookies().set("token", "");
  cookies().set("user", "");
  return res;
};

const postUserLogOut = async (lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/admin-user/logout`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Content-Type": "application/json",
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postUserLogOut", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postConfirmEmail = async (values) => {
  let fResponse;
  const request = {
    url: `${process.env.IDENTITY_API_URL}ConfirmEmail`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    return fResponse?.json();
  } catch (err) {
    logError("postConfirmEmail", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postUsersChangePassword = async (values, lang) => {
  const isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    let fResponse;
    const request = {
      url: `${process.env.API_URL}offers/admin-user/change-password`,
      options: {
        cache: "no-store",
        method: "POST",
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      },
    };

    try {
      fResponse = await fetch(request.url, request.options);
      const vResponse = await validResponse(fResponse);
      return JSON.parse(vResponse);
    } catch (err) {
      logError(
        "postUsersChangePassword",
        "fetchFailed",
        request,
        fResponse,
        err
      );
      throw err;
    }
  } else {
    logError(
      "postUsersChangePassword",
      "reCaptchaFailed",
      values,
      undefined,
      undefined
    );
    return { status: "ERROR", message: "reCaptchaFailed" };
  }
};

const postUsersForgotPassword = async (values, lang) => {
  const isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    let fResponse;
    const request = {
      url: `${process.env.API_URL}offers/admin-user/forgot-password`,
      options: {
        cache: "no-store",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      },
    };

    try {
      fResponse = await fetch(request.url, request.options);
      return fResponse?.json();
    } catch (err) {
      logError(
        "postUsersForgotPassword",
        "fetchFailed",
        request,
        fResponse,
        err
      );
      throw err;
    }
  } else {
    logError(
      "postUsersForgotPassword",
      "reCaptchaFailed",
      values,
      undefined,
      undefined
    );
    return { code: "ERROR", message: "reCaptchaFailed" };
  }
};

const postUsersResetPassword = async (values, lang) => {
  const isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    let fResponse;
    const request = {
      url: `${process.env.API_URL}offers/admin-user/reset-password`,
      options: {
        cache: "no-store",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      },
    };

    try {
      fResponse = await fetch(request.url, request.options);
      return fResponse?.json();
    } catch (err) {
      logError(
        "postUsersResetPassword",
        "fetchFailed",
        request,
        fResponse,
        err
      );
      throw err;
    }
  } else {
    logError(
      "postUsersResetPassword",
      "reCaptchaFailed",
      values,
      undefined,
      undefined
    );
    return { code: "ERROR", message: "reCaptchaFailed" };
  }
};

export {
  postConnectToken,
  setLogout,
  postConfirmEmail,
  postUsersChangePassword,
  postUsersForgotPassword,
  postUsersResetPassword,
};
