"use server";

import { cookies } from "next/headers";
import {
  isSiteVerified,
  getAcceptLanguage,
  getToken,
  validResponse,
  encode,
} from "@/components/ssrTools";
import logger from "@/components/logger";

const postConnectToken = async (values, lang) => {
  logger.info({
    messageTemplate: "postConnectToken",
    properties: {
      username: values?.username,
    },
  });
  let isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);
  if (isWebsiteVerified) {
    const formData = new URLSearchParams();
    formData.append("client_id", process.env.IDENTITY_CLIENT_ID);
    formData.append("client_secret", process.env.IDENTITY_CLIENT_SECRET);
    formData.append("grant_type", process.env.IDENTITY_GRANT_TYPE);
    formData.append("username", values.username);
    formData.append("password", values.password);
    formData.append("scope", process.env.IDENTITY_SCOPE);

    try {
      const res = await fetch(`${process.env.IDENTITY_API_URL}connect/token`, {
        cache: "no-store",
        method: "POST",
        headers: {
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: formData,
      });
      const response = await res.json();
      if (response?.error) {
        response.error = true;
        return response;
      } else {
        cookies().set("token", response?.access_token);
        const base64Token = response?.access_token.split(".")[1];
        const decodedToken = base64Token
          ? JSON.parse(
              Buffer.from(
                base64Token.replace("-", "+").replace("_", "/"),
                "base64"
              ).toString("utf-8")
            )
          : null;
        const user = {
          name: decodedToken?.name,
          email: decodedToken?.email,
        };
        const encodedUser = {};
        for (const key in user) {
          if (user.hasOwnProperty(key)) {
            const encodedKey = encode(key); // Anahtarı kodla
            const encodedValue = encode(user[key]); // Değeri kodla
            encodedUser[encodedKey] = encodedValue;
          }
        }
        const cookieValue = JSON.stringify(encodedUser);
        cookies().set("user", cookieValue);
        return response;
      }
    } catch (err) {
      logger.error({
        messageTemplate: "fetchFailed",
        properties: {
          username: values?.username,
        },
        err,
      });
      throw err;
    }
  } else {
    logger.error({
      messageTemplate: "reCaptchaFailed",
      properties: {
        username: values?.username,
      },
    });
    throw new Error("reCaptchaFailed");
  }
};

const setLogout = async (lang) => {
  const res = await postUserLogOut(lang);
  cookies().set("token", "");
  cookies().set("user", "");
  return res;
};

const postUserLogOut = async (lang) => {
  let res = await fetch(`${process.env.API_URL}offers/admin-user/logout`, {
    cache: "no-store",
    method: "POST",
    headers: {
      Authorization: `Bearer ${getToken()}`,
      "Content-Type": "application/json",
      "Accept-Language": getAcceptLanguage(lang),
    },
    body: JSON.stringify(),
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

const postConfirmEmail = async (values) => {
  const res = await fetch(`${process.env.IDENTITY_API_URL}ConfirmEmail`, {
    cache: "no-store",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });
  return res?.json();
};

const postUsersChangePassword = async (values, lang) => {
  let isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    let res = await fetch(
      `${process.env.API_URL}offers/admin-user/change-password`,
      {
        cache: "no-store",
        method: "POST",
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      }
    );
    res = await validResponse(res);
    return JSON.parse(res);
  }
};

const postUsersForgotPassword = async (values, lang) => {
  let isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    const res = await fetch(
      `${process.env.API_URL}offers/admin-user/forgot-password`,
      {
        cache: "no-store",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      }
    );
    return res?.json();
  } else {
    throw new Error("reCaptchaFailed");
  }
};

const postUsersResetPassword = async (values, lang) => {
  let isWebsiteVerified = await isSiteVerified(values?.recaptchaToken);

  if (isWebsiteVerified) {
    const res = await fetch(
      `${process.env.API_URL}offers/admin-user/reset-password`,
      {
        cache: "no-store",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept-Language": getAcceptLanguage(lang),
        },
        body: JSON.stringify(values),
      }
    );
    return res?.json();
  } else {
    throw new Error("reCaptchaFailed");
  }
};

export {
  postConnectToken,
  setLogout,
  postConfirmEmail,
  postUsersChangePassword,
  postUsersForgotPassword,
  postUsersResetPassword,
};
