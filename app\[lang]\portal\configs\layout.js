import { getDictionary } from "@/dictionaries";
import SectionHeaderComponent from "@/components/containers/sectionHeader";
import DataTabs from "./dataTabs";

export default async function ConfigsLayout({ children, params }) {
  const lang = params.lang;
  const dict = await getDictionary(lang);

  return (
    <>
      <SectionHeaderComponent
        title={dict.configs.title}
        dict={dict}
        lang={lang}
      />
      <DataTabs dict={dict} lang={lang}></DataTabs>
      <div className="p-4">{children}</div>
    </>
  );
}
