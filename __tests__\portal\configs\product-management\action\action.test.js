import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  cleanup,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import {
  getProductManagementSingle,
  postProductManagement,
  putProductManagement,
} from "@/services/productManagement";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import Action from "@/app/[lang]/portal/configs/product-management/action/action";

//#region Mock Tanımlamaları
jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/productManagement", () => ({
  getProductManagementSingle: jest.fn(),
  postProductManagement: jest.fn(),
  putProductManagement: jest.fn(),
}));

// Next.js navigation hook'ları için mock tanımlamaları
const mockPush = jest.fn();
const mockGet = jest.fn();

jest.mock("next/navigation", () => ({
  useRouter: () => ({ push: mockPush }),
  useSearchParams: () => ({ get: mockGet }),
}));
//#endregion

//#region Test Veri Sabitleri
const mockProductData = {
  id: 1,
  productName: "Test Product",
  productNo: "123",
  b2b: true,
  b2c: false,
  registerOnProposal: true,
  offerPrint: true,
  policyPrint: false,
  receiptPrint: true,
  informingPrint: false,
};

const mockSuccessResponse = {
  status: "SUCCESS",
  data: { id: 1 },
};

const mockUserData = {
  id: 1,
  name: "Test User",
};
//#endregion

//#region Test Yardımcı Fonksiyonları
const setupFormInputs = () => {
  const form = screen.getByTestId("productForm");
  const productNameInput = screen
    .getByTestId("productName")
    .querySelector("input");
  const productNoInput = screen.getByTestId("productNo").querySelector("input");
  return { form, productNameInput, productNoInput };
};

const fillAndSubmitForm = async (productName, productNo) => {
  const { form, productNameInput, productNoInput } = setupFormInputs();

  await act(async () => {
    fireEvent.change(productNameInput, { target: { value: productName } });
    fireEvent.change(productNoInput, { target: { value: productNo } });
    fireEvent.submit(form);
  });
};
//#endregion

describe("Product Management Action Component", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;

      // #region Sayfa render olmadan önceki tanımlamalar
      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        cleanup();
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: mockUserData });
        mockGet.mockReturnValue(null);

        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });
      });
      //#endregion

    // #region Sayfa bileşenlerinin kontrolü
      test("form:Text alanlar doğru olmalı.", () => {
        const requiredLabels = [
          dict.configs.productManagement.productName,
          dict.configs.productManagement.productNo,
          dict.public.startDate,
          dict.public.endDate,
          dict.configs.productManagement.b2b,
          dict.configs.productManagement.b2c,
          dict.configs.productManagement.action.registerOnProposal,
          dict.configs.productManagement.offerPrint,
          dict.configs.productManagement.policyPrint,
          dict.configs.productManagement.receiptPrint,
          dict.configs.productManagement.action.informingPrint,
          dict.configs.productManagement.action.add,
          dict.public.back,
          dict.public.save,
        ];

        requiredLabels.forEach((label) => {
          expect(screen.getByText(label)).toBeInTheDocument();
        });
      });

      test("form:Bileşenler form bloğunda bulunmalı.", () => {
        const formElements = [
          "productName",
          "productNo",
          "startDate",
          "endDate",
          "b2b",
          "b2c",
          "registerOnProposal",
          "offerPrint",
          "policyPrint",
          "receiptPrint",
          "informingPrint",
          "backButton",
          "saveButton",
        ];

        formElements.forEach((elementId) => {
          expect(screen.getByTestId(elementId)).toBeInTheDocument();
        });
      });
      //#endregion

      //#region Form Fonksiyonellik Testleri

      test("form:Yeni ekleme formu doğru başlık ile render edilmeli.", () => {
        expect(
          screen.getByText(dict.configs.productManagement.action.add)
        ).toBeInTheDocument();
      });

      test("form:Düzenleme formu doğru başlık ve verilerle render edilmeli.", async () => {
        cleanup();
        mockGet.mockReturnValue("1");
        getProductManagementSingle.mockResolvedValueOnce({
          status: "SUCCESS",
          data: mockProductData,
        });

        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        expect(
          screen.getByText(dict.configs.productManagement.action.edit)
        ).toBeInTheDocument();
      });

      test("api:Yeni ürün başarıyla eklenmeli.", async () => {
        postProductManagement.mockResolvedValueOnce(mockSuccessResponse);

        await fillAndSubmitForm("Test Product", "123");

        await waitFor(() => {
          expect(postProductManagement).toHaveBeenCalled();
          expect(mockPush).toHaveBeenCalledWith(
            `/${lang}/portal/configs/product-management/`
          );
        });
      });

      test("api:Mevcut ürün başarıyla güncellenmeli.", async () => {
        cleanup();
        mockGet.mockReturnValue("1");
        getProductManagementSingle.mockResolvedValueOnce({
          status: "SUCCESS",
          data: mockProductData,
        });
        putProductManagement.mockResolvedValueOnce(mockSuccessResponse);

        await act(async () => {
          render(<Action dict={dict} lang={lang} />);
        });

        await fillAndSubmitForm("Updated Product", "456");

        await waitFor(() => {
          expect(putProductManagement).toHaveBeenCalled();
          expect(mockPush).toHaveBeenCalledWith(
            `/${lang}/portal/configs/product-management/`
          );
        });
      });

      test("route:Geri butonu doğru sayfaya yönlendirmeli.", async () => {
        const backButton = screen.getByTestId("backButton");
        await act(async () => {
          fireEvent.click(backButton);
        });
        expect(mockPush).toHaveBeenCalledWith(
          `/${lang}/portal/configs/product-management/`
        );
      });
      //#endregion
    });
  });
});
