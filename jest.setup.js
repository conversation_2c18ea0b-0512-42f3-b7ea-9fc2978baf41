import "@testing-library/jest-dom";

if (typeof window !== "undefined" && !window.matchMedia) {
  window.matchMedia = (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => false,
  });
}

jest.mock("next/navigation", () => ({
  useRouter: () => ({ push: jest.fn() }),
  usePathname: () => "/",
  useSearchParams: () => ({
    get: jest.fn(),
    toString: jest.fn(() => ""),
  }),
}));

jest.mock("antd", () => {
  const actualAntd = jest.requireActual("antd");
  return {
    ...actualAntd,
    Select: (props) => (
      <select
        data-testid="mock-select"
        onChange={(e) => props.onChange?.(e.target.value)}
      >
        {(props.options || []).map((option) => (
          <option key={option.value} value={option.value}>
            {option.label?.props?.children?.[1] || option.label}
          </option>
        ))}
      </select>
    ),
    App: {
      useApp: () => ({
        message: {
          error: jest.fn(),
          success: jest.fn(),
          warning: jest.fn(),
          info: jest.fn(),
        },
        modal: {
          error: jest.fn().mockImplementation(() => Promise.resolve()),
        },
      }),
    },
    Switch: ({ checked, onChange }) => (
      <div className="ant-switch">
        <button
          type="button"
          role="switch"
          aria-checked={checked ? "true" : "false"}
          onClick={() => onChange?.(!checked)}
        />
      </div>
    ),
  };
});

jest.mock("@/components/logger", () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));
