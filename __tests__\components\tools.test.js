import {
  getLocale,
  getDateTimeFormat,
  getDateFormat,
  getEmailPattern,
  getIPAddressPattern,
  getLanguageOptions,
  getPasswordPattern,
  getLocalISOString,
  getStringToImgUrl,
  getStringToArrayBuffer,
  getBase64,
  handleDownloadFile,
  handleJustNumber,
  handleJustIPAddress,
  validatePassportNumber,
  validateLicenseNumber,
  validateYKN,
  formatDate,
  formatDateSimpleFormat,
  replaceUndefinedWithNull,
  handleLocaleUpperCase,
} from "@/components/tools";

// Mock window.URL.createObjectURL ve window.URL.revokeObjectURL
global.URL.createObjectURL = jest.fn();
global.URL.revokeObjectURL = jest.fn();

// Mock document.createElement
document.createElement = jest.fn(() => ({
  click: jest.fn(),
  style: {},
  setAttribute: jest.fn(),
}));

// Mock document.body.appendChild ve removeChild
const originalAppendChild = document.body.appendChild;
const originalRemoveChild = document.body.removeChild;
document.body.appendChild = jest.fn();
document.body.removeChild = jest.fn();

describe("tools", () => {
  // #region Dil ve format işlemleri
  describe("getLocale", () => {
    test("Dil seçenekleri doğru şekilde dönmeli", () => {
      const result = getLocale;
      expect(result).toHaveProperty("en");
      expect(result).toHaveProperty("tr");
    });
  });

  describe("getDateTimeFormat", () => {
    test("Tarih saat formatları doğru şekilde dönmeli", () => {
      const result = getDateTimeFormat;
      expect(result).toHaveProperty("en", "DD/MM/YYYY HH:mm:ss");
      expect(result).toHaveProperty("tr", "DD.MM.YYYY HH:mm:ss");
    });
  });

  describe("getDateFormat", () => {
    test("Tarih formatları doğru şekilde dönmeli", () => {
      const result = getDateFormat;
      expect(result).toHaveProperty("en", "DD/MM/YYYY");
      expect(result).toHaveProperty("tr", "DD.MM.YYYY");
    });
  });

  describe("getLanguageOptions", () => {
    test("Dil seçenekleri doğru şekilde dönmeli", () => {
      const result = getLanguageOptions;
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty("langCode", "tr");
      expect(result[0]).toHaveProperty("name", "Türkçe");
      expect(result[1]).toHaveProperty("langCode", "en");
      expect(result[1]).toHaveProperty("name", "English");
    });
  });
  // #endregion Dil ve format işlemleri

  // #region Pattern işlemleri
  describe("getEmailPattern", () => {
    test("Email pattern doğru şekilde dönmeli", () => {
      const result = getEmailPattern;
      expect(result.test("<EMAIL>")).toBe(true);
      expect(result.test("invalid-email")).toBe(false);
    });
  });

  describe("getIPAddressPattern", () => {
    test("IP adresi pattern doğru şekilde dönmeli", () => {
      const result = getIPAddressPattern;
      expect(result.test("***********")).toBe(true);
      expect(result.test("invalid-ip")).toBe(false);
    });
  });

  describe("getPasswordPattern", () => {
    test("Şifre pattern doğru şekilde dönmeli", () => {
      const result = getPasswordPattern;
      expect(result.test("Test123!")).toBe(true);
      expect(result.test("weak")).toBe(false);
    });
  });
  // #endregion Pattern işlemleri

  // #region Tarih işlemleri
  describe("getLocalISOString", () => {
    test("Tarih doğru formatta dönmeli", () => {
      const date = new Date("2023-01-01T00:00:00Z");
      const result = getLocalISOString(date);
      expect(result).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}[+-]\d{2}:\d{2}$/
      );
    });
  });

  describe("formatDate", () => {
    test("Tarih doğru formatta dönmeli", () => {
      const date = new Date("2023-01-01T00:00:00Z");
      const result = formatDate(date, "tr");
      expect(result).toMatch(/^\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2}$/);
    });

    test("Tarih saat olmadan doğru formatta dönmeli", () => {
      const date = new Date("2023-01-01T00:00:00Z");
      const result = formatDate(date, "tr", false);
      expect(result).toMatch(/^\d{2}\.\d{2}\.\d{4}$/);
    });
  });

  describe("formatDateSimpleFormat", () => {
    test("Tarih basit formatta dönmeli", () => {
      const date = new Date("2023-01-01T00:00:00Z");
      const result = formatDateSimpleFormat(date);
      expect(result).toBe("2023-01-01");
    });
  });
  // #endregion Tarih işlemleri

  // #region Dosya işlemleri
  describe("getStringToArrayBuffer", () => {
    test("String array buffer'a dönüştürülmeli", () => {
      const result = getStringToArrayBuffer("test");
      expect(result).toBeInstanceOf(ArrayBuffer);
    });
  });

  describe("getStringToImgUrl", () => {
    test("String img url'e dönüştürülmeli", () => {
      const result = getStringToImgUrl("test");
      expect(global.URL.createObjectURL).toHaveBeenCalled();
    });
  });

  describe("getBase64", () => {
    test("Dosya base64'e dönüştürülmeli", async () => {
      const file = new File(["test"], "test.txt", { type: "text/plain" });
      const result = await getBase64(file);
      expect(result).toMatch(/^data:text\/plain;base64,/);
    });
  });

  describe("handleDownloadFile", () => {
    test("Dosya indirme işlemi doğru çalışmalı", () => {
      const res = new Blob(["test"], { type: "application/octet-stream" });
      const fileName = "test.txt";
      handleDownloadFile(res, fileName);
      expect(document.createElement).toHaveBeenCalledWith("a");
      expect(document.body.appendChild).toHaveBeenCalled();
      expect(document.body.removeChild).toHaveBeenCalled();
    });
  });
  // #endregion Dosya işlemleri

  // #region Validasyon işlemleri
  describe("validatePassportNumber", () => {
    test("Geçerli pasaport numarası doğrulanmalı", async () => {
      const result = await validatePassportNumber("A123456", "Hata", "Hata");
      expect(result).toBe(undefined);
    });

    test("Özel karakter içeren pasaport numarası hata vermeli", async () => {
      await expect(
        validatePassportNumber("A123!@#", "Hata", "Özel Karakter Hatası")
      ).rejects.toEqual("Özel Karakter Hatası");
    });
  });

  describe("validateLicenseNumber", () => {
    test("Geçerli lisans numarası doğrulanmalı", async () => {
      const result = await validateLicenseNumber(null, "AB123456");
      expect(result).toBe(undefined);
    });

    test("Geçersiz lisans numarası hata vermeli", async () => {
      await expect(validateLicenseNumber(null, "123")).rejects.toThrow();
    });
  });

  describe("validateYKN", () => {
    test("Geçerli YKN doğrulanmalı", async () => {
      const result = await validateYKN("123456");
      expect(result).toBe(undefined);
    });

    test("Geçersiz YKN hata vermeli", async () => {
      await expect(validateYKN("123")).rejects.toThrow();
    });
  });
  // #endregion Validasyon işlemleri

  // #region Diğer işlemler
  describe("handleJustNumber", () => {
    test("Sadece sayı girişine izin vermeli", () => {
      const event = { target: { value: "123abc" } };
      handleJustNumber(event);
      expect(event.target.value).toBe("123");
    });
  });

  describe("handleJustIPAddress", () => {
    test("Sadece IP adresi karakterlerine izin vermeli", () => {
      const event = { target: { value: "***********abc" } };
      handleJustIPAddress(event);
      expect(event.target.value).toBe("***********");
    });
  });

  describe("replaceUndefinedWithNull", () => {
    test("Undefined değerler null'a dönüştürülmeli", () => {
      const obj = { a: undefined, b: "test" };
      const result = replaceUndefinedWithNull(obj);
      expect(result).toEqual({ a: null, b: "test" });
    });
  });

  describe("handleLocaleUpperCase", () => {
    test("Metin locale'e göre büyük harfe dönüştürülmeli", () => {
      const result = handleLocaleUpperCase("test", "tr");
      expect(result).toBe("TEST");
    });
  });
  // #endregion Diğer işlemler
});
