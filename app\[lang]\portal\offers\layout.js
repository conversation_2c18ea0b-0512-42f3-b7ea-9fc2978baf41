import { getDictionary } from "@/dictionaries";
import SectionHeaderComponent from "@/components/containers/sectionHeader";

export default async function OffersLayout({ children, params }) {
  const lang = params.lang;
  const dict = await getDictionary(lang);

  return (
    <>
      <SectionHeaderComponent
        title={dict.offers.title}
        dict={dict}
        lang={lang}
      />
      <div className="p-4">{children}</div>
    </>
  );
}
