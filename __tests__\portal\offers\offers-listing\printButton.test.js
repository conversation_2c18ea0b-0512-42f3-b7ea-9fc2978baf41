import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { postPrintDocument } from "@/services/insurance";
import { getPolicySetInformationFormCheckPolicy } from "@/services/policy";
import PrintButton from "@/app/[lang]/portal/offers/offers-listing/printButton";

// Mock tanımlamaları
jest.mock("@/services/insurance", () => ({
  postPrintDocument: jest.fn(),
}));

jest.mock("@/services/policy", () => ({
  getPolicySetInformationFormCheckPolicy: jest.fn(),
}));

describe("PrintButton", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockSetFormLoading, mockSetOffersList;
      const mockValue = {
        productNo: "123",
        policyNo: "456",
        informationFormReadCheck: true,
      };

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        mockSetFormLoading = jest.fn();
        mockSetOffersList = jest.fn();
        jest.clearAllMocks();
      });

      // #region Bileşen render kontrolü
      test("button:Buton doğru şekilde render edilmeli.", () => {
        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={1}
            isdisabled={false}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        expect(screen.getByText("Test Label")).toBeInTheDocument();
        expect(screen.getByText("Icon")).toBeInTheDocument();
      });

      test("button:Disabled durumu doğru şekilde uygulanmalı.", () => {
        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={1}
            isdisabled={true}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        const link = screen.getByText("Test Label").closest("a");
        expect(link).toHaveClass("opacity-50");
      });
      // #endregion

      // #region API çağrıları kontrolü
      test("api:PrintType 1 için doğru API çağrısı yapılmalı.", async () => {
        postPrintDocument.mockResolvedValueOnce({
          status: "SUCCESS",
          data: { certificate: "test" },
        });

        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={1}
            isdisabled={false}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        await fireEvent.click(screen.getByText("Test Label").closest("a"));

        await waitFor(() => {
          expect(postPrintDocument).toHaveBeenCalledWith(lang, {
            productNo: "123",
            policyNumber: "456",
            printType: 1,
          });
        });
      });

      test("api:PrintType 7 için doğru API çağrısı yapılmalı.", async () => {
        postPrintDocument.mockResolvedValueOnce({
          status: "SUCCESS",
          data: { certificate: "test" },
        });

        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={7}
            isdisabled={false}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        await fireEvent.click(screen.getByText("Test Label").closest("a"));

        await waitFor(() => {
          expect(postPrintDocument).toHaveBeenCalledWith(lang, {
            productNo: "123",
            policyNumber: "456",
            printType: 7,
          });
        });
      });

      // #endregion

      // #region Hata durumları kontrolü
      //25.06.2025 tarhinde kaldırıldı
      // test("error:InformationFormReadCheck false ise hata mesajı gösterilmeli.", async () => {
      //   render(
      //     <PrintButton
      //       label="Test Label"
      //       icon={<span>Icon</span>}
      //       printType={1}
      //       isdisabled={false}
      //       value={{ ...mockValue, informationFormReadCheck: false }}
      //       dict={dict}
      //       lang={lang}
      //       setFormLoading={mockSetFormLoading}
      //       setOffersList={mockSetOffersList}
      //       offersList={[]}
      //     />
      //   );

      //   fireEvent.click(screen.getByText("Test Label").closest("a"));
      // });

      test("error:API hata durumunda handleError çağrılmalı.", async () => {
        postPrintDocument.mockRejectedValueOnce(new Error("API Error"));

        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={1}
            isdisabled={false}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        await fireEvent.click(screen.getByText("Test Label").closest("a"));

        await waitFor(() => {
          expect(mockSetFormLoading).toHaveBeenCalledWith(false);
        });
      });
      // #endregion

      // #region Loading durumu kontrolü
      test("loading:API çağrısı sırasında loading durumu doğru yönetilmeli.", async () => {
        postPrintDocument.mockResolvedValueOnce({
          status: "SUCCESS",
          data: { certificate: "test" },
        });

        render(
          <PrintButton
            label="Test Label"
            icon={<span>Icon</span>}
            printType={1}
            isdisabled={false}
            value={mockValue}
            dict={dict}
            lang={lang}
            setFormLoading={mockSetFormLoading}
            setOffersList={mockSetOffersList}
            offersList={[]}
          />
        );

        await fireEvent.click(screen.getByText("Test Label").closest("a"));

        expect(mockSetFormLoading).toHaveBeenCalledWith(true);
        await waitFor(() => {
          expect(mockSetFormLoading).toHaveBeenCalledWith(false);
        });
      });
      // #endregion
    });
  });
});
