"use client";

import {
  But<PERSON>,
  Divider,
  Form,
  Modal,
  Popconfirm,
  Upload,
  Spin,
  Input,
  Select,
  Affix,
} from "antd";
import { CloseOutlined, ExportOutlined } from "@ant-design/icons";
import {
  getRequestDetail,
  postAddRequestDetail,
  putUpdateRequestStatus,
  getRequestDownloadFile,
  getRequestStatus,
} from "@/services/requests";
import {
  IconCheck,
  IconPaperAirplane,
  IconPaperClip,
  IconUser,
} from "@/components/icons";
import Favicon from "@/public/favicon.ico";
import Image from "next/image";
import { useUser } from "@/components/contexts/userContext";
import { useState, useEffect } from "react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import {
  useResponseHandler,
  getSpinIndicator,
  handleDownloadFile,
  formatDate,
} from "@/components/tools";
import RequestReviewModal from "@/components/containers/requestReviewModal";
import Link from "next/link";
import dayjs from "dayjs";

export default function MessagingModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
  getOffersList,
  updatedFilteredpagination,
  currentPageNumber,
  defaultModel,
}) {
  const { user } = useUser();
  const { reloadByNewToken } = useModalLogin();
  const { handleError, handleResponse } = useResponseHandler();
  const [formLoading, setFormLoading] = useState(false);
  const [file, setFile] = useState([]);
  const [sendDisabled, setSendDisabled] = useState(true);
  const [form] = Form.useForm();
  const [formReview] = Form.useForm();
  const [requestdetail, setRequestDetail] = useState({});
  const [modifiedDetails, setModifiedDetails] = useState([]);
  const [closedRequest, setClosedRequest] = useState(false);
  const [requestStatus, setRequestStatus] = useState([]);
  const [messageGroups, setMessageGruops] = useState([
    {
      label: dict.requestManagement.requestManagementListing.general,
      value: 0,
    },
    { label: dict.requestManagement.requestManagementListing.admin, value: 1 },
  ]);

  const { TextArea } = Input;
  const onFinish = async (values) => {
    const formData = new FormData();
    values?.files?.fileList?.forEach((fileItem) => {
      formData.append(
        "File",
        fileItem?.originFileObj,
        encodeURIComponent(fileItem?.name)
      );
    });

    const model = {
      TalepNo: selectedData?.id,
      Comment: values.message.trim() ?? "",
      CommentDate: dayjs(new Date()).format("YYYY-MM-DDTHH:mm:ss"),
      //CommentUser: user?.name,
      File: formData ?? null,
      PolicyNo: selectedData.policyNo ?? "",
      RequestReason: selectedData.requestReason ?? null,
      //Unit: selectedData?.unit ?? null,
      Subject: selectedData?.subject ?? null,
      ProductName: selectedData?.productName ?? "",
      //UserName: selectedData?.userName,
      messageGroupId: values.messageGroupId ?? 0,
      AgencyName: "Emaa Sigorta A.Ş.", //!!! TODO :  buraya sonradan bir agencyName gelecek
    };
    await addNewMessage(model);
    await getRequestDetailFunc(lang, selectedData?.id);
  };

  const getRequestDetailFunc = async (lang, id) => {
    try {
      setFormLoading(true);
      const res = await getRequestDetail(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setRequestDetail(res?.data);
          if (res?.data?.status === "TALEP_KAPANDI") {
            setClosedRequest(true);
          } else {
            setClosedRequest(false);
          }
        } else {
          setRequestDetail([]);
        }
      } else {
        setRequestDetail([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const getStatusFunc = async () => {
    try {
      setFormLoading(true);
      const res = await getRequestStatus(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setRequestStatus(res?.data);
      } else {
        setRequestStatus([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setRequestStatus([]);
    } finally {
      setFormLoading(false);
    }
  };

  const addNewMessage = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await postAddRequestDetail(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          form.resetFields();
          setFile([]);
          setSendDisabled(true);
          return res.data;
        }
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const updateRequestStatuFunc = async (lang, val) => {
    try {
      setFormLoading(true);
      const res = await putUpdateRequestStatus(lang, val);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        await getOffersList(defaultModel);
        form.resetFields();
        setIsModalOpen(false);
        if (res?.data) {
          return res.data;
        }
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const downloadRequestFileFunc = async (lang, requestId, file) => {
    try {
      setFormLoading(true);
      const res = await getRequestDownloadFile(lang, requestId, file.fileId);

      if (res !== null) {
        const buffer = Buffer.from(res, "base64");
        const fileName = decodeURIComponent(file.fileName);
        handleDownloadFile(buffer, fileName);
      } else {
        const error = { message: dict.public.error };
        handleError(error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  useEffect(() => {
    const startFetching = async () => {
      setFormLoading(true);
      try {
        await Promise.all([
          getRequestDetailFunc(lang, selectedData?.id),
          getStatusFunc(),
        ]);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);
  useEffect(() => {
    const details = Array.isArray(requestdetail.details)
      ? [...requestdetail.details]
      : [];
    const messages = Array.isArray(requestdetail.messages)
      ? [...requestdetail.messages]
      : [];

    if (requestdetail) {
      let filteredDetails = details.map((detail) => {
        const relatedFiles = messages.filter(
          (item) => item.messageId === detail.detailId
        );

        if (relatedFiles.length > 0) {
          return { ...detail, files: relatedFiles };
        } else {
          return { ...detail, files: [] };
        }
      });

      const unmatchFiles = messages.filter((item) => item.messageId === 0);
      if (unmatchFiles.length > 0) {
        filteredDetails = filteredDetails
          ? [{ files: unmatchFiles }, ...filteredDetails]
          : [{ files: unmatchFiles }];
      }

      setModifiedDetails([...filteredDetails]);
    }
  }, [requestdetail]);

  const Messages = ({
    userName,
    isEmaa,
    createdDate,
    message,
    files,
    messagegroupId,
  }) => {
    return (
      <div className="flex flex-col rounded-lg border p-2">
        <div className="flex items-center justify-between gap-2">
          <div className="inline-flex items-center gap-2 font-semibold">
            {isEmaa ? (
              <Image
                src={Favicon.src}
                alt="EmaaPicture"
                width={32}
                height={32}
                className="rounded-full"
                preview={"false"}
              />
            ) : (
              <IconUser className={"h-6 w-6"} />
            )}
            <p>{userName}</p>/
            <p>
              {
                <>
                  {" "}
                  {
                    dict.requestManagement.requestManagementListing.messageGroup
                  }{" "}
                  : {messagegroupId == 1 ? "Admin" : "Genel"}
                </>
              }
            </p>
          </div>

          <div className="text-xs text-gray-600">
            {formatDate(`${createdDate?.replace("+00:00", "-03:00")}`, lang)}
          </div>
        </div>
        <Divider className="!my-2"></Divider>
        <div
          className="flex justify-between text-justify"
          data-testid="messageTest"
        >
          <p> {message} </p>
        </div>
        {files &&
          files.length > 0 &&
          files.map((file, index) => (
            <Link
              key={index}
              className="mt-2 w-fit"
              data-testid="downloadFileLink"
              href={""}
              onClick={async () =>
                await downloadRequestFileFunc(lang, selectedData.id, file)
              }
            >
              {decodeURIComponent(file.fileName)}
              <ExportOutlined className="ml-2" />
            </Link>
          ))}
      </div>
    );
  };

  const handleBeforeUpload = (file) => {
    setFile((prevFiles) => [...prevFiles, file]);
    return false;
  };

  const handleRemove = (fileToRemove) => {
    setFile((prevFiles) =>
      prevFiles.filter((file) => file.uid !== fileToRemove.uid)
    );
  };

  const onChangeMessage = (e) => {
    if (e.target.value.length > 0) {
      setSendDisabled(false);
    }
    if (e.target.value.trim() == "") {
      setSendDisabled(true);
    }
  };
  const confirmUpdateRequest = async () => {
    try {
      // Validate fields
      await formReview.validateFields();
      // check status  is number
      if (isNaN(formReview.getFieldValue("status"))) {
        const modStatu = requestStatus.find(
          (item) => item.value === formReview.getFieldValue("status")
        );
        formReview.setFieldValue("status", modStatu.code);
      }
      const model = {
        offerId: selectedData?.id,
        statusCode: formReview.getFieldValue("status"),
        requestReason: formReview.getFieldValue("requestReason"),
        unit: formReview.getFieldValue("unit"),
      };
      await updateRequestStatuFunc(lang, model);
    } catch (err) {
      handleError(
        {
          message:
            dict.requestManagement.requestManagementListing.requestUpdateError,
        },
        dict.public.error
      );

      return;
    }
  };

  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1024}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {dict.requestManagement.requestManagementListing.requestReview}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              data-testid="closeButton"
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        {requestStatus && requestStatus.length > 0 && (
          <Form form={formReview}>
            <RequestReviewModal
              dict={dict}
              lang={lang}
              selectedData={selectedData}
              requestStatus={requestStatus}
              sendDisabled={closedRequest}
              closureDate={requestdetail?.closureDate}
              form={formReview}
              getOffersList={getOffersList}
              updatedFilteredpagination={updatedFilteredpagination}
              defaultModel={defaultModel}
              currentPageNumber={currentPageNumber}
            />
          </Form>
        )}
        <div className="flex flex-col gap-2 px-6 pt-6">
          <h3 className="ml-1 text-lg font-bold">
            {dict.requestManagement.requestManagementListing.messageHistory}
          </h3>
          <div className="flex h-96 flex-col gap-2 overflow-y-scroll">
            {modifiedDetails &&
              modifiedDetails?.length > 0 &&
              modifiedDetails?.map((value, index) => {
                return (
                  <Messages
                    key={index}
                    userName={
                      value?.commentUser ?? requestdetail.userName ?? ""
                    }
                    isEmaa={value.isEmaa}
                    createdDate={
                      value?.commentDate ?? requestdetail?.requestDate
                    }
                    message={value?.comment}
                    files={value?.files}
                    messagegroupId={value?.messageGroupId}
                  />
                );
              })}
          </div>
          <h3 className="ml-1 mt-4 text-lg font-bold">
            {dict.requestManagement.requestManagementListing.sendNewMessage}
          </h3>
          <Form
            className="rounded-lg border bg-gray-50"
            form={form}
            onFinish={onFinish}
            disabled={closedRequest}
          >
            <Form.Item name={"message"} className="!mb-2">
              <TextArea
                data-testid="messageInput"
                showCount
                maxLength={1000}
                onChange={onChangeMessage}
                placeholder={
                  dict.requestManagement.requestManagementListing.typeMessage
                }
                className="!rounded-b-none !rounded-t-lg !border-none !bg-gray-50"
                style={{
                  height: 64,
                  resize: "none",
                }}
              />
            </Form.Item>
            <Divider className="!my-4" />
            <div className="flex flex-wrap justify-between gap-2 p-2">
              <Form.Item name={"files"} className="!mb-0">
                <Upload
                  accept=".pdf, .jpg, .png, .jpeg"
                  beforeUpload={handleBeforeUpload}
                  onRemove={handleRemove}
                  fileList={file}
                  multiple={true}
                  onChange={(info) => {
                    const updatedList = info.fileList.map((fileItem) => ({
                      ...fileItem,
                      originFileObj: fileItem.originFileObj || fileItem,
                    }));
                    setFile(updatedList);
                  }}
                >
                  <Button className="!border-none !bg-gray-200">
                    <IconPaperClip className={"h-5 w-5"} />
                  </Button>
                </Upload>
              </Form.Item>

              <div className="flex w-2/3 flex-wrap items-center justify-end gap-2">
                <Form.Item
                  label={
                    dict.requestManagement.requestManagementListing.messageGroup
                  }
                  className="!mb-0 w-1/2"
                  name={"messageGroupId"}
                  data-testid="messageGroupId"
                  rules={[
                    {
                      required: true,
                      message: `${dict.public.requiredField}`,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label.toLowerCase() ?? "").includes(
                        input.toLocaleLowerCase()
                      )
                    }
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                    options={messageGroups}
                  />
                </Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  disabled={sendDisabled}
                  data-testid="sendMessage"
                >
                  <IconPaperAirplane className={"h-5 w-5"} />
                </Button>
              </div>
            </div>
          </Form>
        </div>
        {process.env.NEXT_PUBLIC_USER_TYPE === "PORTAL" && (
          <div className="sticky bottom-0 left-0 z-10 bg-white px-6 py-3 shadow-[0_-2px_8px_rgba(0,0,0,0.1)]">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                {
                  dict.requestManagement.requestManagementListing
                    .requestProcesses
                }
              </h3>
              <Popconfirm
                title={
                  dict.requestManagement.requestManagementListing
                    .requestWillBeUpdated
                }
                onConfirm={confirmUpdateRequest}
                okText={dict.public.yes}
                cancelText={dict.public.no}
                okButtonProps={{ "data-testid": "confirmYesButton" }}
                cancelButtonProps={{ "data-testid": "confirmNoButton" }}
              >
                <Button
                  danger
                  disabled={closedRequest}
                  data-testid="saveAndExit"
                >
                  <IconCheck className="mr-1 h-5 w-5" />
                  {dict.public.saveAndExit}
                </Button>
              </Popconfirm>
            </div>
          </div>
        )}
      </Spin>
    </Modal>
  );
}
