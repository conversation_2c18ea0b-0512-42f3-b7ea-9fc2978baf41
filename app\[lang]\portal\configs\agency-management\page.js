"use server";

import { getDictionary } from "@/dictionaries";
import AgencyManagement from "./agencyManagement";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.configs.agencyManagement.title} | ${dict.configs.title} | ${dict.public.title}`,
  };
}

export default async function AgencyManagementPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <AgencyManagement dict={dict} lang={lang} />;
}
