import { render } from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import DataTabs from "@/app/[lang]/portal/configs/dataTabs";

jest.mock("next/navigation", () => ({
  usePathname: jest.fn(),
}));

describe("DataTabs", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, dataTabs;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(() => {
        dataTabs = render(<DataTabs dict={dict} lang={lang} />);
      });

      test("Sekmeler doğru başlıkları içermeli.", () => {
        const { getByText } = dataTabs;
        expect(
          getByText(dict.configs.productManagement.title)
        ).toBeInTheDocument();
        expect(
          getByText(dict.configs.unitManagement.title)
        ).toBeInTheDocument();
        expect(
          getByText(dict.configs.agencyManagement.title)
        ).toBeInTheDocument();
      });

      test("link:Ürün Yönetimi sekmesi doğru URL'yi içermeli.", () => {
        const { getByText } = dataTabs;
        const productManagementTab = getByText(
          dict.configs.productManagement.title
        ).closest("a");
        expect(productManagementTab).toHaveAttribute(
          "href",
          `/${lang}/portal/configs/product-management`
        );
      });

      test("link:Birim Yönetimi sekmesi doğru URL'yi içermeli.", () => {
        const { getByText } = dataTabs;
        const unitManagementTab = getByText(
          dict.configs.unitManagement.title
        ).closest("a");
        expect(unitManagementTab).toHaveAttribute(
          "href",
          `/${lang}/portal/configs/unit-management`
        );
      });
      test("link:Acente Yönetimi sekmesi doğru URL'yi içermeli.", () => {
        const { getByText } = dataTabs;
        const unitManagementTab = getByText(
          dict.configs.agencyManagement.title
        ).closest("a");
        expect(unitManagementTab).toHaveAttribute(
          "href",
          `/${lang}/portal/configs/agency-management`
        );
      });

      test("tabs:Aktif sekme doğru olmalı.", () => {
        const { container } = dataTabs;
        const activeTab = container.querySelector(".ant-tabs-tab-active");
        expect(activeTab).toHaveTextContent(
          dict.configs.productManagement.title
        );
      });
    });
  });
});
