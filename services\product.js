"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const getActiveProducts = async (lang) => {
  let res = await fetch(`${process.env.API_URL}offers/active-products`, {
    cache: "no-store",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
      "Accept-Language": getAcceptLanguage(lang),
    },
  });
  res = await validResponse(res);
  return JSON.parse(res);
};

export { getActiveProducts };
