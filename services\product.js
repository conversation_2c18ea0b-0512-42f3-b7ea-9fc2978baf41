"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const getActiveProducts = async (lang) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/active-products`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getActiveProducts", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

export { getActiveProducts };
