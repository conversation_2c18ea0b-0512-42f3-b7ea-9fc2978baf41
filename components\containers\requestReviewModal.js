"use client";
import { Descriptions, Spin, Select, Form, Button } from "antd";
import { ExportOutlined } from "@ant-design/icons";
import { postPrintDocument } from "@/services/insurance";
import {
  getPortalOfferRequestReasons,
  getPortalOfferRequestUnits,
  putUpdateRequestInCharge,
} from "@/services/requests";
import { useModalLogin } from "../contexts/modalLoginContext";
import { useUser } from "../contexts/userContext";
import {
  useResponseHandler,
  getStringToArrayBuffer,
  formatDate,
  getSpinIndicator,
} from "@/components/tools";
import { useState, useEffect } from "react";
import Link from "next/link";
export default function RequestReviewModal({
  dict,
  lang,
  selectedData,
  requestStatus,
  sendDisabled,
  closureDate,
  form,
  getOffersList,
  defaultModel,
  currentPageNumber,
  updatedFilteredpagination,
}) {
  const { handleResponse, handleError } = useResponseHandler();
  const { reloadByNewToken } = useModalLogin();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const formValues = Form.useWatch([], form);
  const selectedStatu =
    requestStatus && requestStatus.find((a) => a.code == formValues?.status);
  const [units, setUnits] = useState([]);
  const [reasons, setReasons] = useState([]);
  const [isFirstRender, setIsFirstRender] = useState(true);

  useEffect(() => {
    const startFetching = async () => {
      try {
        setFormLoading(true);
        await getUnits({
          productNo: selectedData?.productNo,
        });
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
        if (isFirstRender) {
          form &&
            form.setFieldsValue({
              unit: selectedData?.unit,
              requestReason: selectedData?.requestReason,
            });
        }
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  useEffect(() => {
    const startFetching = async () => {
      try {
        setFormLoading(true);
        await getRequestReasons({
          productNo: isNaN(selectedData) ? false : selectedData?.productNo,
          unitName: formValues?.unit,
        });
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
        if (isFirstRender) {
          setIsFirstRender(false);
        }
      }
    };

    if (formValues?.unit && selectedData.productNo) startFetching();
  }, [lang, user, reloadByNewToken, formValues?.unit]);
  const getPrintDocument = async (value) => {
    try {
      const tmp = {
        productNo: value?.productNo?.toString(),
        policyNumber: value?.policyNo?.toString(),
        printType: value?.printType,
      };
      setFormLoading(true);
      const res = await postPrintDocument(lang, tmp);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        const firstDecodedString = atob(res?.data?.certificate);
        const arrayBuffer = getStringToArrayBuffer(firstDecodedString);
        const blob = new Blob([arrayBuffer], {
          type: "application/pdf",
        });
        const blobURL = URL.createObjectURL(blob);
        window.open(blobURL, "_blank");
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const getUnits = async (val) => {
    try {
      const res = await getPortalOfferRequestUnits(lang, val);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setUnits(res?.data);
      } else {
        setUnits([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };
  const updateInchargeFunc = async (id) => {
    try {
      setFormLoading(true);
      const res = await putUpdateRequestInCharge(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        const lastFiter = updatedFilteredpagination
          ? updatedFilteredpagination
          : {
              ...defaultModel,
              pagination: {
                ...defaultModel.pagination,
                pageNumber: currentPageNumber,
              },
            };
        await getOffersList(lastFiter);
      } else {
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const getRequestReasons = async (val) => {
    try {
      const res = await getPortalOfferRequestReasons(lang, val);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setReasons(res?.data);
        if (!isFirstRender) {
          if (res.data.length == 1) {
            form.setFieldsValue({
              requestReason: res.data[0],
            });
          } else {
            form.resetFields(["requestReason"]);
          }
        }
      } else {
        setReasons([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    }
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      <div className="flex flex-col px-6 pb-1 pt-4">
        <Descriptions
          className="review-desc"
          bordered
          column={{
            xs: 1,
            md: 2,
          }}
          size="small"
        >
          <Descriptions.Item
            label={
              dict.requestManagement.requestManagementListing.requestNumber
            }
          >
            {selectedData?.id}
          </Descriptions.Item>
          {selectedData?.unit && (
            <Descriptions.Item
              label={dict.requestManagement.requestManagementListing.unit}
            >
              <Form.Item
                name={"unit"}
                className="mb-0 w-48"
                rules={[{ required: true, message: dict.public.required }]}
                data-testid="unit-select"
              >
                {form ? (
                  <Select
                    disabled={sendDisabled}
                    className="!mt-3 w-full"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label.toLowerCase() ?? "").includes(
                        input.toLocaleLowerCase()
                      )
                    }
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                    options={units?.map((item, i) => {
                      return {
                        value: item,
                        label: `${item}`,
                      };
                    })}
                  />
                ) : (
                  <p className="mt-4"> {selectedData?.unit}</p>
                )}
              </Form.Item>
            </Descriptions.Item>
          )}
          <Descriptions.Item
            label={
              dict.requestManagement.requestManagementCreating.requestReason
            }
          >
            <Form.Item
              name={"requestReason"}
              className="mb-0 w-48"
              rules={[{ required: true, message: dict.public.requiredField }]}
            >
              {form ? (
                <Select
                  disabled={sendDisabled}
                  className="!mt-3 w-full"
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={reasons?.map((item, i) => {
                    return {
                      value: item,
                      label: `${item}`,
                    };
                  })}
                />
              ) : (
                <p className="mt-4"> {selectedData?.requestReason}</p>
              )}
            </Form.Item>
          </Descriptions.Item>
          {selectedData?.productName && (
            <Descriptions.Item
              label={dict.requestManagement.requestManagementCreating.product}
            >
              {selectedData?.productName}
            </Descriptions.Item>
          )}
          {selectedData?.policyNo && (
            <Descriptions.Item
              label={
                dict.requestManagement.requestManagementCreating.offerPolicyNum
              }
            >
              {process.env.NEXT_PUBLIC_USER_TYPE === "PORTAL" ? (
                <Link
                  href={""}
                  onClick={() =>
                    getPrintDocument({
                      productNo: selectedData?.productNo,
                      policyNo: selectedData?.policyNo,
                      printType: 1,
                    })
                  }
                >
                  {selectedData?.policyNo ?? ""}
                  <ExportOutlined className="ml-2" />
                </Link>
              ) : (
                <>{selectedData?.policyNo ?? ""}</>
              )}
            </Descriptions.Item>
          )}
          <Descriptions.Item
            label={dict.requestManagement.requestManagementListing.requestDate}
          >
            {formatDate(selectedData?.requestDate, lang, true)}
          </Descriptions.Item>
          {sendDisabled ? (
            <Descriptions.Item
              span={{ md: 3 }}
              label={
                dict.requestManagement.requestManagementListing
                  .requestClosedDate
              }
            >
              {formatDate(closureDate, lang, true)}
            </Descriptions.Item>
          ) : null}
          <Descriptions.Item
            label={dict.requestManagement.requestManagementListing.subject}
            span={{ lg: 2 }}
          >
            {selectedData?.subject}
          </Descriptions.Item>
          <Descriptions.Item label={dict.public.status}>
            {(selectedStatu && selectedStatu?.value) ??
              selectedData?.status ??
              ""}
          </Descriptions.Item>
          <Descriptions.Item
            label={dict.requestManagement.requestManagementCreating.detail}
            span={{ lg: 2 }}
          >
            {selectedData?.detail}
          </Descriptions.Item>
          {requestStatus && (
            <>
              <Descriptions.Item
                label={
                  dict.requestManagement.requestManagementCreating.updateStatus
                }
                span={{ lg: 2 }}
              >
                <Form.Item
                  name={"status"}
                  rules={[
                    { required: true, message: dict.public.requiredField },
                  ]}
                  initialValue={selectedData?.status}
                  className="!mt-4"
                >
                  <Select
                    disabled={sendDisabled}
                    className="w-full"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label.toLowerCase() ?? "").includes(
                        input.toLocaleLowerCase()
                      )
                    }
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                    options={requestStatus?.map((item) => {
                      return {
                        value: item.code,
                        label: `${item.value}`,
                      };
                    })}
                  />
                </Form.Item>
              </Descriptions.Item>
              <Descriptions.Item span={{ lg: 1 }} label={""}>
                <Button
                  disabled={sendDisabled}
                  type="primary"
                  className="w-full"
                  onClick={() => updateInchargeFunc(selectedData?.id)}
                >
                  {
                    dict.requestManagement.requestManagementCreating
                      .takeRequestOnMe
                  }
                </Button>
              </Descriptions.Item>
            </>
          )}
        </Descriptions>
      </div>
    </Spin>
  );
}
