"use server";

import { getDictionary } from "@/dictionaries";
import EmailValidation from "./emailValidation";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.public.validation} | ${dict.public.title}`,
  };
}

export default async function Validation({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <EmailValidation dict={dict} lang={lang}></EmailValidation>;
}
