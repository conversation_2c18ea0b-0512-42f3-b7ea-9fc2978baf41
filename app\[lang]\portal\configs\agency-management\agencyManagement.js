"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import AgencyAddEditModal from "./actionModalAgencyManagement";
import {
  formatDate,
  getLocalISOString,
  getSpinIndicator,
  useResponseHandler,
  replaceUndefinedWithNull,
} from "@/components/tools";
import {
  Button,
  Form,
  Select,
  Spin,
  Table,
  Input,
  Popconfirm,
  Dropdown,
  Space,
} from "antd";
import { useEffect, useState } from "react";
import { IconPlus, IconArrowDown } from "@/components/icons";
import {
  ClearOutlined,
  FilterOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { getDateFormat } from "@/components/tools";

import {
  postPaginatedAgencyDefinitions,
  deleteAgencyDefinition,
} from "@/services/agencyManagement";
import { getActiveProducts } from "@/services/product";
import { getBroadcastChannel } from "@/services/messages";
import Link from "next/link";

export default function AgencyManagement({ dict, lang }) {
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const [paginatedList, setPaginatedList] = useState([]);
  const [updatedFilteredpagination, setUpdatedFilteredPagination] =
    useState(null);
  const [products, setProducts] = useState([]);
  const [broadcastChannel, setBroadcastChannel] = useState([]);
  const [form] = Form.useForm();
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [selectedData, setSelectedData] = useState(null);

  const defaultModel = {
    productNo: null,
    agencyCode: null,
    agencyName: null,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "id",
      ascending: true,
    },
  };
  const [requestBody, setRequestBody] = useState(defaultModel);
  const columns = [
    {
      title: `${dict.configs.productManagement.productNo}`,
      key: "productNo",
      dataIndex: "productNo",
    },
    {
      title: `${dict.configs.agencyManagement.agencyCode}`,
      key: "channel",
      dataIndex: "channel",
    },
    {
      title: `${dict.configs.agencyManagement.agencyName}`,
      key: "agencyName",
      dataIndex: "agencyName",
    },

    {
      title: `${dict.public.startDate}`,
      key: "begDate",
      render: (product) => (
        <>{product.begDate ? formatDate(product.begDate, lang, false) : "-"}</>
      ),
    },
    {
      title: `${dict.public.endDate}`,
      key: "endDate",
      render: (product) => (
        <>{product.endDate ? formatDate(product.endDate, lang, false) : "-"}</>
      ),
    },

    {
      title: `${dict.public.action}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: "100px",
      render: (value, a) => {
        const items = [
          {
            label: (
              <Link
                href={""}
                className="flex items-center gap-2"
                onClick={() => {
                  setIsEdit(true);
                  setSelectedData(value);
                  setIsModalOpen(true);
                }}
              >
                <EditOutlined className="!text-lg !text-blue-500" />
                {dict.public.edit}
              </Link>
            ),
            key: "0",
          },

          {
            label: (
              <Popconfirm
                title={dict.public.warning}
                description={dict.public.sureDelete}
                onConfirm={() => {
                  deleteAgencyItemFunc(value.id);
                }}
                okText={dict.public.yes}
                cancelText={dict.public.no}
                data-testid="popConfirm"
                okButtonProps={{ "data-testid": "confirmYesButton" }}
                cancelButtonProps={{ "data-testid": "confirmNoButton" }}
              >
                <div className="flex items-center gap-2">
                  <DeleteOutlined
                    className="!text-lg !text-red-500"
                    data-testid="deleteIcon"
                  />
                  {dict.public.delete}
                </div>
              </Popconfirm>
            ),
            key: "1",
          },
        ];
        return (
          <div className="relative flex flex-wrap justify-center gap-1">
            <Dropdown
              onOpenChange={(a) => {}}
              menu={{ items }}
              trigger={["click"]}
              className="!h-10 !w-10"
            >
              <Button>
                <span>
                  <Space>
                    <IconArrowDown
                      className={"inline-block h-5 w-5 align-text-top"}
                    />
                  </Space>
                </span>
              </Button>
            </Dropdown>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    const startFetching = async () => {
      try {
        setFormLoading(true);
        await Promise.all([getProductsList(lang), getAgencyList(lang)]);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getProductsList = async () => {
    setFormLoading(true);
    try {
      const res = await getActiveProducts(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setProducts(res?.data);
        form.setFieldsValue({
          productNo: res?.data.length > 0 && res?.data[0]?.key,
        });

        requestBody.productNo = res?.data.length > 0 && res?.data[0]?.key;
        await getAgencyManagementPaginatedList(requestBody);
      } else {
        setProducts([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const deleteAgencyItemFunc = async (id) => {
    setFormLoading(true);
    try {
      const res = await deleteAgencyDefinition(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        await getAgencyManagementPaginatedList(requestBody);
      } else {
        handleError(res?.status, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };
  const getAgencyList = async () => {
    setFormLoading(true);
    try {
      const res = await getBroadcastChannel(lang);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        setBroadcastChannel(res?.data);
      } else {
        setBroadcastChannel([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  // #endregion table için kodlar
  const getAgencyManagementPaginatedList = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await postPaginatedAgencyDefinitions(lang, requestBody);
      handleResponse(res);

      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setPaginatedList(res?.data);
        } else {
          setPaginatedList([]);
        }

        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setPaginatedList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setPaginatedList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };
  // #region filtre kodlar

  const onFinish = async (values) => {
    const updatedBody = replaceUndefinedWithNull({
      ...defaultModel,
      ...values,
    });
    getAgencyManagementPaginatedList(updatedBody);
    setCurrentPageNumber(1);
    setUpdatedFilteredPagination(updatedBody);
  };

  const handleReset = () => {
    form.resetFields(["agencyCode", "agencyName"]);
  };
  // #endregion filtre ve rapor için kodlar
  return (
    <>
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <AgencyAddEditModal
          dict={dict}
          lang={lang}
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          selectedData={selectedData}
          isEdit={isEdit}
          broadcastChannel={broadcastChannel}
          requestBody={requestBody}
          getAgencyManagementPaginatedList={getAgencyManagementPaginatedList}
        />
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl bg-white p-6 drop-shadow-md">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="productNo"
                data-testid="productNo"
                className="!shrink !grow !basis-52"
                label={dict.public.products}
                rules={[
                  {
                    required: true,

                    message: `${dict.public.requiredField}`,
                  },
                ]}
              >
                <Select
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={products?.map((product) => {
                    return {
                      value: product.key,
                      label: `${product.key} - ${product.value}`,
                    };
                  })}
                />
              </Form.Item>
              <Form.Item
                name="agencyCode"
                data-testid="agencyCode"
                className="!shrink !grow !basis-52"
                label={dict.configs.agencyManagement.agencyCode}
              >
                <Input className="w-full" />
              </Form.Item>
              <Form.Item
                name="agencyName"
                data-testid="AgencyName"
                className="!shrink !grow !basis-52"
                label={dict.configs.agencyManagement.agencyName}
              >
                <Input className="w-full" />
              </Form.Item>
            </div>
          </div>
          <div className="mt-5 flex flex-wrap justify-end gap-2">
            <Button
              data-testid="addButton"
              type="primary"
              onClick={() => {
                setIsEdit(false);
                setSelectedData({
                  productNo: form.getFieldValue("productNo"),
                });
                setIsModalOpen(true);
              }}
            >
              <IconPlus className={"inline-block h-5 w-5 align-text-top"} />
              <span className="ml-1">{dict.public.add}</span>
            </Button>
            <div className="flex flex-1 flex-wrap justify-end gap-2">
              <Button
                data-testid="resetButton"
                type="primary"
                onClick={() => {
                  handleReset();
                }}
                className="!bg-gray-500 !bg-opacity-75"
                icon={<ClearOutlined />}
              >
                {dict.public.clean}
              </Button>
              <Button
                data-testid="filterButton"
                type="primary"
                className="!bg-dark-gray"
                htmlType="submit"
                icon={<FilterOutlined />}
              >
                {dict.public.filter}
              </Button>
            </div>
          </div>
        </Form>
        <div className="mt-5">
          <Table
            className="grid-table"
            columns={columns}
            dataSource={paginatedList}
            rowKey={"id"}
            scroll={{
              x: 1000,
            }}
            pagination={{
              current: currentPageNumber,
              total: totalRecords,
              defaultPageSize: 10,
              showSizeChanger: false,
              responsive: true,
              onChange: (pageNumber, pageSize) => {
                const updatedBody = {
                  ...requestBody,
                  pagination: {
                    pageNumber: pageNumber,
                    pageSize: pageSize,
                    orderBy: "id",
                    ascending: false,
                  },
                };
                setCurrentPageNumber(pageNumber);
                getAgencyManagementPaginatedList(updatedBody);
              },
            }}
          />
        </div>
      </Spin>
    </>
  );
}
