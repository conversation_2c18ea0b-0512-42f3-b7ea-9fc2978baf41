import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { getActiveProducts } from "@/services/product";
import { postPaginatedPolicies } from "@/services/policy";
import { useRouter } from "next/navigation";
import { getLocale } from "@/components/tools";
import { getDictionary } from "@/dictionaries";
import OffersListing from "@/app/[lang]/portal/offers/offers-listing/offersListing";

// Mock tanımlamaları

jest.mock("react-google-recaptcha-v3", () => ({
  useGoogleReCaptcha: () => ({
    executeRecaptcha: jest.fn(() => Promise.resolve("test-token")),
  }),
}));

jest.mock("@/services/identityConnect", () => ({
  postConnectToken: jest.fn(),
}));
jest.mock("@/components/contexts/modalLoginContext", () => ({
  useModalLogin: jest.fn(),
}));

jest.mock("@/components/contexts/userContext", () => ({
  useUser: jest.fn(),
}));

jest.mock("@/services/product", () => ({
  getActiveProducts: jest.fn(),
}));

jest.mock("@/services/policy", () => ({
  postPaginatedPolicies: jest.fn(),
}));

jest.mock("@/services/insurance", () => ({
  postGetQueryPolicyDetail: jest.fn(),
}));

jest.mock("@/services/policyHistory", () => ({
  getPolicyHistories: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

describe("OffersListing", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockRouter;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        useModalLogin.mockReturnValue({ reloadByNewToken: jest.fn() });
        useUser.mockReturnValue({ user: { id: 1, name: "Test User" } });
        getActiveProducts.mockResolvedValue({
          status: "SUCCESS",
          data: [
            { key: "1", value: "Product 1" },
            { key: "2", value: "Product 2" },
          ],
        });
        postPaginatedPolicies.mockResolvedValue({
          status: "SUCCESS",
          data: [],
          totalNumberOfRecords: 0,
        });
        mockRouter = { push: jest.fn() };
        useRouter.mockReturnValue(mockRouter);

        await act(async () => {
          render(<OffersListing dict={dict} lang={lang} />);
        });
      });

      // #region Sayfa bileşenlerinin kontrolü
      test("form:Text alanlar doğru olmalı.", () => {
        const texts = [
          dict.offers.title,
          dict.requestManagement.requestManagementCreating.offerPolicyNum,
          dict.public.products,
          dict.offers.offersListing.idType,
          dict.public.startDate,
          dict.public.endDate,
          dict.offers.offersListing.insuredName,
          dict.offers.offersListing.insuredSurname,
          dict.offers.offersListing.company,
          dict.public.plateNumber,
          dict.offers.offersListing.agentName,
          dict.offers.offersListing.agentCode,
          dict.public.clean,
          dict.public.view,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(text);
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      test("form:Bileşenler filtre bloğunda bulunmalı.", async () => {
        expect(screen.getByTestId("policyStatus")).toBeInTheDocument();
        expect(screen.getByTestId("policyNo")).toBeInTheDocument();
        expect(screen.getByTestId("productNo")).toBeInTheDocument();
        expect(screen.getByTestId("civilRegistiration")).toBeInTheDocument();
        expect(screen.getByTestId("begDate")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("name")).toBeInTheDocument();
        expect(screen.getByTestId("surname")).toBeInTheDocument();
        expect(screen.getByTestId("title")).toBeInTheDocument();
        expect(screen.getByTestId("plate")).toBeInTheDocument();
        expect(screen.getByTestId("agencyName")).toBeInTheDocument();
        expect(screen.getByTestId("agentCode")).toBeInTheDocument();
        expect(screen.getByTestId("resetButton")).toBeInTheDocument();
        expect(screen.getByTestId("filterButton")).toBeInTheDocument();
      });
      // #endregion

      // #region Form işlevselliği kontrolü

      test("form:Filtre butonu form değerlerini doğru şekilde göndermeli.", async () => {
        const { getByTestId } = screen;
        const policyStatusSelect =
          getByTestId("policyStatus").querySelector("select");
        const policyNoInput = getByTestId("policyNo").querySelector("input");
        const filterButton = getByTestId("filterButton");

        // Form değerlerini set et
        await act(async () => {
          fireEvent.change(policyStatusSelect, { target: { value: "1" } });
          fireEvent.change(policyNoInput, { target: { value: "123456" } });
        });

        // Form submit
        await act(async () => {
          fireEvent.click(filterButton);
        });

        // API çağrısını kontrol et
        await waitFor(() => {
          expect(postPaginatedPolicies).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
              policyStatus: 1,
              policyNo: "123456",
              pagination: {
                pageNumber: 1,
                pageSize: 10,
                orderBy: "id",
                ascending: false,
              },
            })
          );
        });
      });

      test("form: Reset butonu form alanlarını temizlemeli.", async () => {
        const policyStatusSelect = screen
          .getByTestId("policyStatus")
          .querySelector("select");
        const policyNoInput = screen
          .getByTestId("policyNo")
          .querySelector("input");
        const resetButton = screen.getByTestId("resetButton");

        // Form değerlerini set et
        await act(async () => {
          fireEvent.change(policyStatusSelect, { target: { value: "T" } });
          fireEvent.change(policyNoInput, { target: { value: "123456" } });
        });

        // Reset butonuna tıkla
        await act(async () => {
          fireEvent.click(resetButton);
        });

        // Form değerlerinin sıfırlandığını kontrol et
        await waitFor(() => {
          expect(window.__form__.getFieldValue("policyStatus")).toBe("");
          expect(window.__form__.getFieldValue("policyNo")).toBe("");
        });
      });

      test("form:ID tipi seçildiğinde ilgili input alanı görünmeli.", async () => {
        const { getByTestId } = screen;
        const idTypeSelect =
          getByTestId("civilRegistiration").querySelector("select");

        await act(async () => {
          fireEvent.change(idTypeSelect, { target: { value: "1" } });
        });

        const tcknInput = getByTestId("civilRegistirationNumber").querySelector(
          "input"
        );
        expect(tcknInput).toBeInTheDocument();
        expect(tcknInput.maxLength).toBe(11);
      });

      // #endregion
    });
  });
});
