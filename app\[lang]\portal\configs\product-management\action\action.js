"use client";

import { <PERSON>, Spin, Input, But<PERSON>, Switch, DatePicker, Select } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getSpinIndicator, useResponse<PERSON>and<PERSON> } from "@/components/tools";
import { useUser } from "@/components/contexts/userContext";
import { getDateFormat } from "@/components/tools";
import { IconArrowSmallLeft } from "@/components/icons";
import {
  getProductManagementSingle,
  postProductManagement,
  putProductManagement,
} from "@/services/productManagement";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

export default function Action({ dict, lang }) {
  const [form] = Form.useForm();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const router = useRouter();
  const id = useSearchParams().get("id");
  const actionStatus = id === null ? "add" : "edit";
  dayjs.extend(utc);

  useEffect(() => {
    const startFetching = async () => {
      setFormLoading(true);
      try {
        if (id) await getProductManagement(id);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };

    if (user) startFetching();
  }, [lang, user, actionStatus]);

  const calculateUserGroup = (b2b = false, b2c = false) => {
    return [null, 0, 1, 2][b2c * 2 + b2b];
  };
  const toStr = (value) => {
    return value ? "E" : "H";
  };
  const toBoolean = (value) => {
    return value === "E";
  };
  const onFinish = async (values) => {
    values.registerOnProposal = toStr(values.registerOnProposal);
    values.offerPrint = toStr(values.offerPrint);
    values.policyPrint = toStr(values.policyPrint);
    values.receiptPrint = toStr(values.receiptPrint);
    values.informingPrint = toStr(values.informingPrint);
    values.userGroup = calculateUserGroup(values.b2b, values.b2c);

    values.startDate = dayjs
      .utc(
        dayjs(values?.startDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();

    values.endDate = dayjs
      .utc(
        dayjs(values?.endDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();

    if (actionStatus == "edit") {
      updateProductManagement(values);
    } else {
      addProductManagement(values);
    }
  };

  const addProductManagement = async (values) => {
    setFormLoading(true);
    try {
      const res = await postProductManagement(lang, values);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        router.push(`/${lang}/portal/configs/product-management/`);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const updateProductManagement = async (values) => {
    setFormLoading(true);
    try {
      const res = await putProductManagement(lang, values);
      handleResponse(res, dict.public.success);
      if (res?.status === "SUCCESS") {
        router.push(`/${lang}/portal/configs/product-management/`);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const getProductManagement = async (id) => {
    setFormLoading(true);
    try {
      let res = await getProductManagementSingle(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        let data = res?.data;

        data.b2b = data.userGroup == 0 || data.userGroup == 2;
        data.b2c = data.userGroup == 1 || data.userGroup == 2;
        if (data.startDate != null) data.startDate = dayjs(data.startDate);
        if (data.endDate != null) data.endDate = dayjs(data.endDate);

        data.registerOnProposal = toBoolean(data.registerOnProposal);
        data.offerPrint = toBoolean(data.offerPrint);
        data.policyPrint = toBoolean(data.policyPrint);
        data.receiptPrint = toBoolean(data.receiptPrint);
        data.informingPrint = toBoolean(data.informingPrint);
        form.setFieldsValue(data);
      } else {
        handleError(res?.error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Spin indicator={getSpinIndicator} spinning={formLoading}>
      <Form
        form={form}
        layout="vertical"
        className="w-full"
        onFinish={onFinish}
        data-testid="productForm"
      >
        <div className="rounded-xl bg-white p-6 pb-0 drop-shadow-md">
          <h1 className="mb-4 text-2xl font-medium">
            {actionStatus === "add" ? (
              <span>{dict.configs.productManagement.action.add}</span>
            ) : (
              <span>{dict.configs.productManagement.action.edit}</span>
            )}
          </h1>
          <div className="flex flex-wrap gap-x-6">
            <Form.Item name="id" hidden="true">
              <Input />
            </Form.Item>
            <Form.Item
              name="productName"
              data-testid="productName"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.productName}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="productNo"
              data-testid="productNo"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.productNo}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="startDate"
              data-testid="startDate"
              className="!shrink !grow !basis-52"
              label={dict.public.startDate}
            >
              <DatePicker className="w-full" format={getDateFormat[lang]} />
            </Form.Item>
            <Form.Item
              name="endDate"
              data-testid="endDate"
              className="!shrink !grow !basis-52"
              label={dict.public.endDate}
            >
              <DatePicker className="w-full" format={getDateFormat[lang]} />
            </Form.Item>
          </div>

          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="b2b"
              data-testid="b2b"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.b2b}
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name="b2c"
              data-testid="b2c"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.b2c}
            >
              <Switch />
            </Form.Item>
            <div className="!shrink !grow !basis-52"></div>
            <div className="!shrink !grow !basis-52"></div>
          </div>
        </div>

        {/* 
        TODO - Analiz Ekibi Üzerine Çalışacakmış.
        {actionStatus === "edit" && (
          <div className="mt-4 rounded-xl bg-white p-6 pb-0 drop-shadow-md">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="productViewingAgency"
                className="!shrink !grow !basis-52"
                label={
                  dict.configs.productManagement.action.productViewingAgency
                }
              >
                <Select className="">
                  <Select.Option value="izmirSgr">İzmir Sigorta</Select.Option>
                  <Select.Option value="bucaSgr">Buca Sigorta</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="productViewingAgencyUsers"
                className="!shrink !grow !basis-52"
                label={
                  dict.configs.productManagement.action
                    .productViewingAgencyUsers
                }
              >
                <Select className="">
                  <Select.Option value="user1">Artuhan Aslankaya</Select.Option>
                  <Select.Option value="user2">Hüseyin Erdoğan</Select.Option>
                </Select>
              </Form.Item>
            </div>
          </div>
        )} */}

        <div className="mt-4 rounded-xl bg-white p-6 pb-0 drop-shadow-md">
          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="registerOnProposal"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.action.registerOnProposal}
              data-testid="registerOnProposal"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name="offerPrint"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.offerPrint}
              data-testid="offerPrint"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name="policyPrint"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.policyPrint}
              data-testid="policyPrint"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name="receiptPrint"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.receiptPrint}
              data-testid="receiptPrint"
            >
              <Switch />
            </Form.Item>
          </div>
          <div className="flex flex-wrap gap-x-6">
            <Form.Item
              name="informingPrint"
              className="!shrink !grow !basis-52"
              label={dict.configs.productManagement.action.informingPrint}
              data-testid="informingPrint"
            >
              <Switch />
            </Form.Item>
          </div>
        </div>
        <div className="mt-6 flex justify-between">
          <Button
            type="primary"
            onClick={() => {
              router.push(`/${lang}/portal/configs/product-management/`);
            }}
            className="!flex items-center gap-1 !bg-gray-500 !bg-opacity-75"
            data-testid="backButton"
          >
            <IconArrowSmallLeft className={"h-4 w-4"} />
            {dict.public.back}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            data-testid="saveButton"
          >
            {dict.public.save}
          </Button>
        </div>
      </Form>
    </Spin>
  );
}
