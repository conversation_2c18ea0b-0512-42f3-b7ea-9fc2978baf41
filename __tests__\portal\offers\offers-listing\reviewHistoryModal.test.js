import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import ReviewHistoryModal from "@/app/[lang]/portal/offers/offers-listing/reviewHistoryModal";

// Antd Table warning'ini bastırmak için console.error mock'u
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (args[0].includes("rowKey")) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

describe("ReviewHistoryModal", () => {
  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict;
      let mockSetIsModalOpen;
      let mockSelectedData;
      let mockPolicyHistory;

      beforeAll(async () => {
        dict = await getDictionary(lang);
      });

      beforeEach(async () => {
        jest.clearAllMocks();
        mockSetIsModalOpen = jest.fn();
        mockSelectedData = {
          policyNo: "321456",
          insured: "John Doe",
        };
        mockPolicyHistory = [
          {
            policyNo: "123654",
            lastUserName: "Test User",
            entryDate: "2024-03-20T10:00:00",
            policyStatus: "T",
            grossPremium: "1000",
            explanation: "Test explanation",
          },
          {
            policyNo: "123456",
            lastUserName: "Test User 2",
            entryDate: "2024-03-21T11:00:00",
            policyStatus: "P",
            grossPremium: "2000",
            explanation: "Test explanation 2",
          },
        ];
      });

      // #region Modal bileşenlerinin kontrolü
      test("modal:Başlık ve temel bilgiler doğru şekilde render edilmeli.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        expect(screen.getByText("321456 - John Doe")).toBeInTheDocument();
        expect(screen.getByText("Test User")).toBeInTheDocument();
        expect(screen.getByText("Test explanation")).toBeInTheDocument();
      });

      test("modal:Close butonu çalışmalı.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        const closeButton = screen.getByRole("img", { name: "close" });
        fireEvent.click(closeButton);
        expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
      });

      test("modal:Tablo sütunları doğru şekilde render edilmeli.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        // Tablo başlıklarının varlığını kontrol et
        expect(
          screen.getByText(dict.offers.offersListing.transactionExecutor)
        ).toBeInTheDocument();
        expect(
          screen.getByText(dict.offers.offersListing.transactionDate)
        ).toBeInTheDocument();
        expect(
          screen.getByText(dict.offers.offersListing.status)
        ).toBeInTheDocument();
        expect(
          screen.getByText(dict.offers.offersListing.grossPremium)
        ).toBeInTheDocument();
        expect(screen.getByText(dict.public.description)).toBeInTheDocument();
      });

      test("modal:Tablo verileri doğru şekilde formatlanmalı.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        // Para formatlamasını kontrol et
        expect(screen.getByText("₺ 1000")).toBeInTheDocument();
        expect(screen.getByText("₺ 2000")).toBeInTheDocument();

        // Status formatlamasını kontrol et
        expect(
          screen.getByText(dict.offers.offersListing.quotation)
        ).toBeInTheDocument();
        expect(
          screen.getByText(dict.offers.offersListing.policy)
        ).toBeInTheDocument();
      });

      test("modal:Tablo scroll özelliği doğru şekilde ayarlanmalı.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        const tableContent = screen.getByRole("table").parentElement;
        expect(tableContent).toHaveStyle({
          overflowX: "auto",
          overflowY: "hidden",
        });
      });

      test("modal:Modal kapalıyken içerik render edilmemeli.", () => {
        render(
          <ReviewHistoryModal
            dict={dict}
            lang={lang}
            isModalOpen={false}
            setIsModalOpen={mockSetIsModalOpen}
            selectedData={mockSelectedData}
            policyHistory={mockPolicyHistory}
          />
        );

        expect(screen.queryByText("321456 - John Doe")).not.toBeInTheDocument();
        expect(screen.queryByRole("table")).not.toBeInTheDocument();
      });
      // #endregion Modal bileşenlerinin kontrolü
    });
  });
});
