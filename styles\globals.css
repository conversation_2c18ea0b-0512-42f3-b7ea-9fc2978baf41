@tailwind base;
@tailwind components;
@tailwind utilities;

/* #region Modal açıldığında owerflow yapılmasını engellemek için, ekleyip siliyoruz modallarda */
.self-overflow-hidden {
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  touch-action: none;
  -ms-touch-action: none;

  @media (min-width: 1024px) {
    overflow: initial;
    -webkit-overflow-scrolling: initial;
    touch-action: initial;
    -ms-touch-action: initial;
  }
}
/* #endregion Modal açıldığında owerflow yapılmasını engellemek için, ekleyip siliyoruz modallarda */

/* #region AntDesign a özel bazı iyileştirme kodları */
@media (max-width: 576px) {
  .ant-picker-datetime-panel {
    flex-direction: column !important;
  }

  .ant-transfer {
    flex-direction: column !important;
  }
}

.ant-transfer-list-header-selected {
  display: none;
}

.ant-transfer-list-header-title {
  text-align: left !important;
}

/*Sol footer'ı saklamaya yarıyor */
.no-left-footer div:nth-child(1) > .ant-transfer-list-footer {
  display: none;
}

.uploadItemWithDate .ant-upload-list-item-container {
  height: 148px !important;
}
/* #endregion AntDesign a özel bazı iyileştirme kodları */

/* #region portal gri background ve sarı baloncuk kodları */
.yellow-bubble {
  width: 574px;
  height: 574px;
  border-radius: 574px;
  background: #84754e1c;
  filter: blur(100px);
  position: absolute;
}

.left-top-yellow {
  left: -283px;
  top: -86px;
}

.right-bottom-yellow {
  right: -313px;
  bottom: -86px;
}
/* #endregion portal gri background ve sarı baloncuk kodları */

/* #region Özel scroll bar kodları */
::-webkit-scrollbar {
  width: 2px;
  height: 2px;
  padding-left: 5px;
}

::-webkit-scrollbar-track {
  background: transparent;
  padding-left: 5px;
}

::-webkit-scrollbar-thumb {
  background: #84754e;
  background-clip: padding-box;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@media (min-width: 768px) {
  ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
}

@media (min-width: 992px) {
  ::-webkit-scrollbar {
    height: 7px;
  }
}
/* #endregion Özel Scroll Bar Kodları */

/* #region Özel ant design tabı */
.slf-tabs-card .ant-tabs-nav {
  margin: 0 !important;
}

.slf-tabs-card .ant-tabs-nav::before {
  display: none;
}

.slf-tabs-card .ant-tabs-nav-list {
  flex: 1;
  background: #fff;
}

.slf-tabs-card .ant-tabs-tab {
  padding: 0px !important;
}

.slf-tabs-card .ant-tabs-tab .ant-tabs-tab-btn a {
  color: #000000e0 !important;
  display: flex;
  padding: 6px 12px;
  align-items: center;
  user-select: none;
  border: 4px solid #fff;
}

.slf-tabs-card .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn a {
  color: #84754e !important;
}

.slf-tabs-card .ant-tabs-nav-operations {
  display: none !important;
}
/* #endregion Özel ant design tabı */

/* #region Özel ant design step ve grid table */
.corporate-profile-steps .ant-steps-item-icon {
  display: none !important;
}

.corporate-profile-steps .ant-steps-item::before {
  height: 0 !important;
}

.grid-table .ant-table-thead > tr > th,
.grid-table .ant-table-thead > tr > td {
  background: #ebebeb !important;
  color: #2e4152 !important;
  font-weight: 600 !important;
}

.grid-table .ant-table-tbody > tr > td {
  color: #2e4152 !important;
  font-weight: 600 !important;
}

.grid-table .ant-table-tbody > tr:last-child > *:first-child {
  border-end-start-radius: 10px !important;
}

.grid-table .ant-table-tbody > tr:last-child > *:last-child {
  border-end-end-radius: 10px !important;
}

.grid-table .ant-table-tbody {
  background: #fff !important;
}

.grid-table .ant-table {
  background: transparent !important;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.04);
  border-radius: 10px !important;
}

.expanded-table .ant-table-thead > tr:first-child > *:first-child {
  border-start-start-radius: 0 !important;
}

.expanded-table .ant-table-thead > tr:first-child > *:last-child {
  border-start-end-radius: 0 !important;
}

.expanded-table .ant-table-tbody > tr:last-child > *:first-child {
  border-end-start-radius: 0 !important;
}

.customize-modal .ant-modal-content {
  padding: 0 0 10px 0 !important;
  background-color: #fff !important;
}

.ant-btn-disabled {
  background-color: #ccc !important;
}
/* #endregion Özel ant design step ve grid table */

/* #region Özel ant design form label */
.ant-form-item .ant-form-item-label > label {
  font-weight: 500;
}
/* #endregion Özel ant design form label */

/* #region Başvuru formunda portfolyo */
.annual-portfolio-table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 16px;
  border-bottom: 1px solid #6a6a6a48;
  padding-bottom: 8px;
  padding-top: 8px;
}

.annual-portfolio-table-row:last-child {
  border-bottom: none;
}

.portfolio-weight-table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 16px;
  border-bottom: 1px solid #6a6a6a48;
  padding-bottom: 8px;
  padding-top: 8px;
}

.portfolio-weight-table-row:last-child {
  border-bottom: none;
}
/* #endregion Başvuru formunda portfolyo */

/* #region Formlara özel kodlar */
.carPlate .ant-input::placeholder {
  color: #000000 !important;
}
.carPlate .ant-input-group-wrapper-disabled .ant-input-group-addon {
  background-color: #f9fafb !important;
}
/* #endregion Formlara özel kodlar */

/* #region Diğer genel bir grubu olmayanlar */
.ant-modal-header {
  background: #ebebeb !important;
}
.review-desc .ant-descriptions-view .ant-descriptions-item {
  margin-bottom: 8px !important; /* Adjust this value as needed */
}
.grecaptcha-badge {
  visibility: hidden;
}
/* #endregion Diğer genel bir grubu olmayanlar */
