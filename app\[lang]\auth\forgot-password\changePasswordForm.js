"use client";

import { Form, Input, Button, message } from "antd";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { postUsersResetPassword } from "@/services/identityConnect";
import { getEmailPattern, getPasswordPattern } from "@/components/tools";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import Link from "next/link";

export default function ChangePasswordForm({ dict, lang, mailAddress }) {
  const [form] = Form.useForm();
  const [loadingAction, setLoadingAction] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const router = useRouter();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const showMessage = (type, message) => {
    messageApi.open({
      type: type,
      content: message,
    });
  };

  const isError = (message) => {
    setLoadingAction(false);
    showMessage("error", dict.error[message] || message);
  };

  const isSuccess = () => {
    showMessage("success", dict.public.success);
  };

  const onFinish = async (values) => {
    try {
      setLoadingAction(true);
      values.recaptchaToken = await executeRecaptcha("post");
      const res = await postUsersResetPassword(values, lang);
      if (res?.code !== "SUCCESS") {
        isError(res?.message);
      } else {
        isSuccess();
        setTimeout(() => {
          router.push(`/${lang}/auth/login/`);
          setLoadingAction(false);
        }, 2000);
      }
    } catch (error) {
      if (error?.message != undefined) isError(error?.message);
    }
  };

  return (
    <>
      {contextHolder}
      <Form form={form} onFinish={onFinish} layout="vertical" className="!mt-6">
        <Form.Item
          name="email"
          data-testid="email"
          rules={[
            {
              required: true,
              pattern: getEmailPattern,
              message: `${dict.public.requiredField}`,
            },
          ]}
          initialValue={mailAddress}
        >
          <Input placeholder={dict.public.username} disabled />
        </Form.Item>
        <Form.Item
          name="code"
          data-testid="code"
          rules={[
            {
              required: true,
              message: `${dict.public.requiredField}`,
            },
          ]}
        >
          <Input autoFocus placeholder={dict.public.code} maxLength={8} />
        </Form.Item>
        <Form.Item
          name="password"
          data-testid="password"
          rules={[
            {
              required: true,
              pattern: getPasswordPattern,
              message: `${dict.public.passwordPattern}`,
            },
          ]}
        >
          <Input.Password placeholder={dict.public.password} maxLength={32} />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          data-testid="confirmPassword"
          rules={[
            {
              required: true,
              pattern: getPasswordPattern,
              message: `${dict.public.passwordPattern}`,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }

                return Promise.reject(
                  new Error(dict.profile.changePassword.doNotMatchPassword)
                );
              },
            }),
          ]}
        >
          <Input.Password
            placeholder={dict.public.confirmPassword}
            maxLength={32}
          />
        </Form.Item>
        <Form.Item className="!mb-0">
          <Button
            data-testid="buttonSubmit"
            type="primary"
            className="w-full"
            htmlType="submit"
            loading={loadingAction}
          >
            {dict.auth.changePassword}
          </Button>
        </Form.Item>
      </Form>
      <div className="my-6 flex flex-col items-center justify-center">
        <span className="text-center text-sm text-gray-600 text-opacity-80">
          {dict.auth.doYouHaveAccount}
        </span>
        <Link
          data-testid="linkAuthLogin"
          className="font-bold text-primary-color"
          href={`/${lang}/auth/login/`}
        >
          {dict.auth.loginSignTitle}
        </Link>
      </div>
    </>
  );
}
