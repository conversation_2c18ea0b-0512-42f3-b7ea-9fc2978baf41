"use client";

import { App } from "antd";
import { getStringToArrayBuffer, useResponseHandler } from "@/components/tools";
import { postPrintDocument } from "@/services/insurance";
import { getPolicySetInformationFormCheckPolicy } from "@/services/policy";
import Link from "next/link";
export default function PrintButton({
  label,
  icon,
  printType,
  isdisabled,
  value,
  dict,
  lang,
  setFormLoading,
  setOffersList,
  offersList,
}) {
  const { handleResponse, handleError } = useResponseHandler();
  const { message } = App.useApp();

  const getPrintDocument = async (value, printType) => {
    try {
      const tmp = {
        productNo: value?.productNo?.toString(),
        policyNumber: value?.policyNo?.toString(),
        printType: printType,
      };
      setFormLoading(true);
      const res = await postPrintDocument(lang, tmp);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        const firstDecodedString = atob(res?.data?.certificate);
        const arrayBuffer = getStringToArrayBuffer(firstDecodedString);
        const blob = new Blob([arrayBuffer], {
          type: "application/pdf",
        });
        const blobURL = URL.createObjectURL(blob);
        window.open(blobURL, "_blank");
        //25.06.2025 tarhinde kaldırıldı
        // if (printType == 7) {
        //   try {
        //     const res = await getPolicySetInformationFormCheckPolicy(
        //       lang,
        //       value?.policyNo?.toString()
        //     );
        //     handleResponse(res);
        //     if (res?.status === "SUCCESS") {
        //       let k = offersList.map((a) => {
        //         return a?.policyNo == value?.policyNo
        //           ? { ...a, informationFormReadCheck: true }
        //           : { ...a };
        //       });
        //       setOffersList(k);
        //     }
        //   } catch (error) {
        //     handleError(error, dict.public.error);
        //   }
        // }
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Link
      href={""}
      disabled={isdisabled}
      className={`${isdisabled ? "opacity-50" : ""}`}
      onClick={async (e) => {
        if (!isdisabled) {
          getPrintDocument(value, printType);
          //25.06.2025 tarhinde kaldırıldı
          // if (printType == 1 || printType == 2) {
          //   if (value?.informationFormReadCheck) {
          //     let m = await getPrintDocument(value, printType);
          //   } else {
          //     message.error(
          //       dict.error.informationFormPrintingFirstToBeDownloaded
          //     );
          //   }
          // } else if (printType == 7) {
          //   getPrintDocument(value, printType);
          // }
        }
      }}
    >
      <div className="flex items-center gap-2">
        {icon}
        {label}
      </div>
    </Link>
  );
}
