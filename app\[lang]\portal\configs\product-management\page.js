"use server";

import { getDictionary } from "@/dictionaries";
import ProductManagement from "./productManagement";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.configs.productManagement.title} | ${dict.configs.title} | ${dict.public.title}`,
  };
}

export default async function ProductManagementPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <ProductManagement dict={dict} lang={lang} />;
}
