"use client";

import { useModalLogin } from "@/components/contexts/modalLoginContext";
import { useUser } from "@/components/contexts/userContext";
import { getSpinIndicator, useResponseHandler } from "@/components/tools";
import { Button, Form, Spin, Table, Tooltip, Switch, Input } from "antd";
import { useEffect, useState } from "react";
import { IconCheck, IconClose, IconPlus } from "@/components/icons";
import { ClearOutlined, FilterOutlined, EyeOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { getUnitManagement } from "@/services/unitManagement";

export default function UnitManagement({ dict, lang }) {
  const { reloadByNewToken } = useModalLogin();
  const { handleResponse, handleError } = useResponseHandler();
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const [unitsList, setUnitsList] = useState([]);
  const [form] = Form.useForm();
  if (typeof window !== "undefined") {
    window.__form__ = form;
  }
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalRecords, setTotalRecords] = useState();
  const router = useRouter();
  const defaultModel = {
    id: 0,
    unitName: null,
    unitMail: null,
    unitTel: null,
    isActive: 0,
    pagination: {
      pageNumber: 1,
      pageSize: 10,
      orderBy: "Id",
      ascending: true,
    },
  };
  const [requestBody, setRequestBody] = useState(defaultModel);

  useEffect(() => {
    const startFetching = async () => {
      try {
        getUnitManagementList(requestBody);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
      }
    };

    if (user) startFetching();
  }, [lang, user, reloadByNewToken]);

  const getUnitManagementList = async (requestBody) => {
    try {
      setFormLoading(true);
      const res = await getUnitManagement(lang, requestBody);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          setUnitsList(res?.data);
        } else {
          setUnitsList([]);
        }
        setTotalRecords(res?.totalNumberOfRecords);
      } else {
        setUnitsList([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
      setUnitsList([]);
    } finally {
      setRequestBody(requestBody);
      setFormLoading(false);
    }
  };

  // #region table için kodlar
  const getCheckOrCloseIcon = (isActive) => {
    return isActive == 1 ? (
      <IconCheck className={"h-6 w-6 text-green-500"} />
    ) : (
      <IconClose className={"h-6 w-6 text-red-500"} />
    );
  };
  const columns = [
    {
      title: `${dict.configs.unitManagement.unitName}`,
      key: "unitName",
      dataIndex: "unitName",
    },
    {
      title: `${dict.configs.unitManagement.unitMail}`,
      key: "unitMail",
      dataIndex: "unitMail",
    },
    {
      title: `${dict.configs.unitManagement.unitTel}`,
      key: "unitTel",
      dataIndex: "unitTel",
    },
    {
      title: `${dict.configs.unitManagement.isActive}`,
      key: "isActive",
      render: (unit) => <>{getCheckOrCloseIcon(unit?.isActive)}</>,
    },
    {
      title: `${dict.public.action}`,
      key: "action",
      align: "center",
      fixed: "right",
      width: 100,
      render: (value) => (
        <div className="flex flex-wrap justify-center gap-1">
          <Tooltip placement="top" title={dict.public.review}>
            <Button
              onClick={() => {
                router.push(
                  `/${lang}/portal/configs/unit-management/action/?id=${value?.id}`
                );
              }}
              size="small"
              type="text"
              className="!flex items-center justify-center !border-none !px-1.5"
              icon={<EyeOutlined className="!text-lg !text-blue-500" />}
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  // #endregion table için kodlar

  // #region filtre kodlar
  const onFinish = async (values) => {
    const updatedBody = {
      ...requestBody,
      ...values,
    };
    getUnitManagementList(updatedBody);
  };

  const handleReset = () => {
    form.resetFields();
    form.setFieldsValue({
      unitName: "",
      unitMail: "",
    });
    getUnitManagementList(defaultModel);
  };
  // #endregion filtre ve rapor için kodlar

  return (
    <>
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl bg-white p-6 drop-shadow-md">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="unitName"
                data-testid="unitName"
                className="!shrink !grow !basis-52"
                label={dict.configs.unitManagement.unitName}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="unitMail"
                data-testid="unitMail"
                className="!shrink !grow !basis-52"
                label={dict.configs.unitManagement.unitMail}
              >
                <Input />
              </Form.Item>
            </div>
          </div>
          <div className="mt-5 flex flex-wrap justify-end gap-2">
            <Button
              type="primary"
              onClick={() => {
                router.push(`/${lang}/portal/configs/unit-management/action/`);
              }}
              data-testid="addButton"
            >
              <IconPlus className={"inline-block h-5 w-5 align-text-top"} />
              <span className="ml-1">{dict.public.add}</span>
            </Button>
            <div className="flex flex-1 flex-wrap justify-end gap-2">
              <Button
                type="primary"
                onClick={handleReset}
                className="!bg-gray-500 !bg-opacity-75"
                icon={<ClearOutlined />}
                data-testid="resetButton"
              >
                {dict.public.clean}
              </Button>
              <Button
                type="primary"
                className="!bg-dark-gray"
                htmlType="submit"
                icon={<FilterOutlined />}
                data-testid="filterButton"
              >
                {dict.public.filter}
              </Button>
            </div>
          </div>
        </Form>
        <div className="mt-5">
          <Table
            className="grid-table"
            columns={columns}
            dataSource={unitsList}
            rowKey={"id"}
            scroll={{
              x: 1000,
            }}
            pagination={{
              current: currentPageNumber,
              total: totalRecords,
              defaultPageSize: 10,
              showSizeChanger: false,
              responsive: true,
              onChange: (pageNumber, pageSize) => {
                const updatedBody = {
                  ...requestBody,
                  pagination: {
                    pageNumber: pageNumber,
                    pageSize: pageSize,
                    orderBy: "id",
                    ascending: false,
                  },
                };
                setCurrentPageNumber(pageNumber);
                getUnitManagementList(updatedBody);
              },
            }}
          />
        </div>
      </Spin>
    </>
  );
}
