"use client";

import { Divider, Image, Modal, Spin } from "antd";
import { CloseOutlined, ExportOutlined } from "@ant-design/icons";
import { IconUser } from "@/components/icons";
import { useUser } from "@/components/contexts/userContext";
import { getRequestFiles, getRequestDownloadFile } from "@/services/requests";
import {
  useResponseHandler,
  getSpinIndicator,
  handleDownloadFile,
  formatDate,
} from "@/components/tools";
import { useState, useEffect } from "react";
import RequestReviewModal from "@/components/containers/requestReviewModal";
import Favicon from "@/public/favicon.ico";
import Link from "next/link";

export default function DocumentsModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
}) {
  const { user } = useUser();
  const [formLoading, setFormLoading] = useState(false);
  const [requestfiles, setRequestFiles] = useState([]);
  const { handleError, handleResponse } = useResponseHandler();

  const getRequestFilesFunc = async (lang, id) => {
    try {
      setFormLoading(true);
      const res = await getRequestFiles(lang, id);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        if (res?.data) {
          const sortedData = res?.data.sort((a, b) => a.id - b.id);
          setRequestFiles(sortedData);
        } else {
          setRequestFiles([]);
        }
      } else {
        setRequestFiles([]);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  const downloadRequestFileFunc = async (lang, requestId, file) => {
    try {
      setFormLoading(true);
      const res = await getRequestDownloadFile(lang, requestId, file.fileId);

      if (res !== null) {
        const buffer = Buffer.from(res, "base64");
        const fileName = decodeURIComponent(file.fileName);
        handleDownloadFile(buffer, fileName);
      } else {
        const error = { message: dict.public.error };
        handleError(error, dict.public.error);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  useEffect(() => {
    const startFetching = async () => {
      setFormLoading(true);
      try {
        await Promise.all([getRequestFilesFunc(lang, selectedData?.id)]);
      } catch (error) {
        handleError(error, dict.public.error);
      } finally {
        setFormLoading(false);
      }
    };
    user && startFetching();
  }, [lang, user]);

  const Documents = ({ userName, isEmaa, createdDate, fileLink, fileName }) => {
    return (
      <div className="flex flex-col rounded-lg border p-2">
        <div className="flex items-center justify-between gap-2">
          <div className="inline-flex items-center gap-2 font-semibold">
            {isEmaa ? (
              <Image
                src={Favicon.src}
                alt="EmaaPicture"
                width={32}
                height={32}
                className="rounded-full"
                preview={false}
              />
            ) : (
              <IconUser className={"h-6 w-6"} />
            )}
            {userName}
          </div>
          <div className="text-xs text-gray-600">
            {formatDate(`${createdDate.replace("+00:00", "-03:00")}`, lang)}
          </div>
        </div>
        <Divider className="!my-2"></Divider>
        {fileLink && (
          <Link
            className="mt-2 w-fit"
            href={""}
            onClick={async () =>
              await downloadRequestFileFunc(lang, selectedData.id, {
                fileId: fileLink.fileId,
                fileName: fileName,
              })
            }
          >
            {decodeURIComponent(fileName)}
            <ExportOutlined className="ml-2" />
          </Link>
        )}
      </div>
    );
  };

  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {dict.public.files}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <RequestReviewModal
          dict={dict}
          lang={lang}
          selectedData={selectedData}
        />
        <div className="flex flex-col gap-2 px-6 pt-6">
          <h3 className="ml-1 text-lg font-bold">
            {dict.requestManagement.requestManagementListing.fileHistory}
          </h3>
          <div className="flex h-96 flex-col gap-2 overflow-y-scroll">
            {requestfiles &&
              requestfiles?.length > 0 &&
              requestfiles.map((item, index) => (
                <Documents
                  key={index}
                  userName={item.createdBy}
                  isEmaa={item?.isEmaa}
                  createdDate={item.createdAt}
                  fileLink={{
                    fileId: item.id,
                    requestId: item.requestId,
                  }}
                  fileName={item.fileName}
                />
              ))}
          </div>
        </div>
      </Spin>
    </Modal>
  );
}
