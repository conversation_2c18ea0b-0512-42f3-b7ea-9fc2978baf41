#Creates a layer from node:alpine image.
FROM node:18 as base

# Set environment
ENV TZ=GMT-3

#Creates directories
RUN mkdir -p /srv/app

#Sets an environment variable
ENV PORT 3000

#Sets the working directory for any RUN, CMD, ENTRYPOINT, COPY, and ADD commands
WORKDIR /srv/app

#Copy new files or directories into the filesystem of the container
COPY package.json /srv/app

#Execute commands in a new layer on top of the current image and commit the results
RUN yarn install

#Informs container runtime that the container listens on the specified network ports at runtime
EXPOSE 3000

#Allows you to configure a container that will run as an executable
CMD ["yarn", "dev"]

FROM base AS staging

# Copy all files
COPY ./ ./

# Build app
RUN yarn build

CMD ["yarn", "start"]

FROM base AS production

# Copy all files
COPY .env ./.env

# Copy all files
COPY ./ ./

# Build app
RUN yarn build

CMD ["yarn", "start"]