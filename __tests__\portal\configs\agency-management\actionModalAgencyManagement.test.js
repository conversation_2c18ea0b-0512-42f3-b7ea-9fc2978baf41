import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  cleanup,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { getLocale } from "@/components/tools";
import { postAddUpdateAgencyDefinition } from "@/services/agencyManagement";
import AgencyAddEditModal from "@/app/[lang]/portal/configs/agency-management/actionModalAgencyManagement";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

// Mock dependencies
jest.mock("@/services/agencyManagement", () => ({
  postAddUpdateAgencyDefinition: jest.fn(),
}));

jest.mock("@/components/tools", () => ({
  useResponseHandler: jest.fn(() => ({
    handleResponse: jest.fn(),
    handleError: jest.fn(),
  })),
  getDateFormat: { en: "DD/MM/YYYY", tr: "DD.MM.YYYY" },
  getSpinIndicator: <div data-testid="spinner">Loading...</div>,
  formatDate: jest.fn(),
  getModifiedProductNoTitles: jest.fn(() => ({})),
}));

jest.mock("dayjs", () => {
  const originalDayjs = jest.requireActual("dayjs");
  const mockDayjs = jest.fn((date) => originalDayjs(date));
  mockDayjs.extend = jest.fn();
  mockDayjs.utc = jest.fn((date, format) => originalDayjs.utc(date, format));
  return mockDayjs;
});

describe("AgencyAddEditModal", () => {
  const mockSetIsModalOpen = jest.fn();
  const mockGetAgencyManagementPaginatedList = jest.fn();
  const mockHandleResponse = jest.fn();
  const mockHandleError = jest.fn();

  // Mock useResponseHandler
  const mockUseResponseHandler =
    require("@/components/tools").useResponseHandler;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseResponseHandler.mockReturnValue({
      handleResponse: mockHandleResponse,
      handleError: mockHandleError,
    });
    dayjs.extend(utc);
  });

  afterEach(() => {
    cleanup();
  });

  const createDefaultProps = (dict) => ({
    dict,
    lang: "en",
    isModalOpen: true,
    setIsModalOpen: mockSetIsModalOpen,
    selectedData: null,
    isEdit: false,
    broadcastChannel: [
      { key: "1", value: "Agency 1" },
      { key: "2", value: "Agency 2" },
    ],
    requestBody: {},
    getAgencyManagementPaginatedList: mockGetAgencyManagementPaginatedList,
  });

  Object.keys(getLocale).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, defaultProps;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        defaultProps = createDefaultProps(dict);
      });

      describe("Modal Rendering", () => {
        test("should render modal when isModalOpen is true", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          expect(
            screen.getByText(dict.configs.agencyManagement.addAgency)
          ).toBeInTheDocument();
          expect(screen.getByText(dict.public.startDate)).toBeInTheDocument();
          expect(screen.getByText(dict.public.endDate)).toBeInTheDocument();
          expect(
            screen.getByText(dict.configs.agencyManagement.agencySelection)
          ).toBeInTheDocument();
          expect(screen.getByText(dict.public.save)).toBeInTheDocument();
        });

        test("should not render modal content when isModalOpen is false", () => {
          render(<AgencyAddEditModal {...defaultProps} isModalOpen={false} />);

          expect(
            screen.queryByText(dict.configs.agencyManagement.addAgency)
          ).not.toBeInTheDocument();
        });

        test("should render edit mode title when isEdit is true", () => {
          const editProps = {
            ...defaultProps,
            isEdit: true,
            selectedData: {
              id: 1,
              channel: "1",
              begDate: "2023-01-01T00:00:00.000Z",
              endDate: "2023-12-31T00:00:00.000Z",
              productNo: "PROD001",
            },
          };

          render(<AgencyAddEditModal {...editProps} />);

          expect(
            screen.getByText(dict.configs.agencyManagement.editAgency)
          ).toBeInTheDocument();
        });

        test("should render close button and handle click", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          const closeButton = screen.getByRole("img", { name: /close/i });
          fireEvent.click(closeButton);

          expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
        });
      });

      describe("Form Components", () => {
        test("should render Transfer component in add mode", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          // Check for Transfer component titles
          expect(
            screen.getByText(dict.configs.productManagement.agencies)
          ).toBeInTheDocument();
          expect(
            screen.getByText(dict.configs.agencyManagement.selectedAgencies)
          ).toBeInTheDocument();
        });

        test("should render Select component in edit mode", () => {
          const editProps = {
            ...defaultProps,
            isEdit: true,
            selectedData: {
              id: 1,
              channel: "1",
              begDate: "2023-01-01T00:00:00.000Z",
              endDate: "2023-12-31T00:00:00.000Z",
              productNo: "PROD001",
            },
          };

          render(<AgencyAddEditModal {...editProps} />);

          // In edit mode, Select should be rendered instead of Transfer
          const selectElement = screen.getByTestId("mock-select");
          expect(selectElement).toBeInTheDocument();
        });

        test("should render date picker fields with correct labels", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          expect(screen.getByTestId("begDate")).toBeInTheDocument();
          expect(screen.getByTestId("endDate")).toBeInTheDocument();
        });

        test("should render agency code field", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        });

        test("should render save button", () => {
          render(<AgencyAddEditModal {...defaultProps} />);

          expect(screen.getByTestId("saveButton")).toBeInTheDocument();
        });
      });

      describe("Form Functionality", () => {
        test("should handle form submission successfully", async () => {
          postAddUpdateAgencyDefinition.mockResolvedValue({
            status: "SUCCESS",
          });

          render(<AgencyAddEditModal {...defaultProps} />);

          const saveButton = screen.getByTestId("saveButton");
          fireEvent.click(saveButton);

          await waitFor(() => {
            expect(postAddUpdateAgencyDefinition).toHaveBeenCalled();
            expect(mockHandleResponse).toHaveBeenCalled();
          });
        });

        test("should handle form submission error", async () => {
          const error = new Error("API Error");
          postAddUpdateAgencyDefinition.mockRejectedValue(error);

          render(<AgencyAddEditModal {...defaultProps} />);

          const saveButton = screen.getByTestId("saveButton");
          fireEvent.click(saveButton);

          await waitFor(() => {
            expect(mockHandleError).toHaveBeenCalledWith(
              error,
              dict.public.error
            );
          });
        });

        test("should show loading spinner during form submission", async () => {
          postAddUpdateAgencyDefinition.mockImplementation(
            () =>
              new Promise((resolve) =>
                setTimeout(() => resolve({ status: "SUCCESS" }), 100)
              )
          );

          render(<AgencyAddEditModal {...defaultProps} />);

          const saveButton = screen.getByTestId("saveButton");
          fireEvent.click(saveButton);

          // Check if loading state is shown
          expect(screen.getByTestId("spinner")).toBeInTheDocument();
        });

        test("should close modal after successful submission", async () => {
          postAddUpdateAgencyDefinition.mockResolvedValue({
            status: "SUCCESS",
          });

          render(<AgencyAddEditModal {...defaultProps} />);

          const saveButton = screen.getByTestId("saveButton");
          fireEvent.click(saveButton);

          await waitFor(() => {
            expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
            expect(mockGetAgencyManagementPaginatedList).toHaveBeenCalled();
          });
        });
      });

      describe("Edit Mode Behavior", () => {
        test("should populate form fields in edit mode", () => {
          const editProps = {
            ...defaultProps,
            isEdit: true,
            selectedData: {
              id: 1,
              channel: "1",
              begDate: "2023-01-01T00:00:00.000Z",
              endDate: "2023-12-31T00:00:00.000Z",
              productNo: "PROD001",
            },
          };

          render(<AgencyAddEditModal {...editProps} />);

          // Check that the form is rendered with edit mode components
          expect(screen.getByTestId("agencyCodeSelect")).toBeInTheDocument();
          expect(
            screen.getByText(dict.configs.agencyManagement.editAgency)
          ).toBeInTheDocument();
        });

        test("should disable agency select in edit mode", () => {
          const editProps = {
            ...defaultProps,
            isEdit: true,
            selectedData: {
              id: 1,
              channel: "1",
              begDate: "2023-01-01T00:00:00.000Z",
              endDate: "2023-12-31T00:00:00.000Z",
              productNo: "PROD001",
            },
          };

          render(<AgencyAddEditModal {...editProps} />);

          const selectElement = screen.getByTestId("mock-select");
          expect(selectElement).toBeInTheDocument();
        });
      });

      describe("Agency Data Processing", () => {
        test("should process broadcastChannel data correctly", () => {
          const customProps = {
            ...defaultProps,
            broadcastChannel: [
              { key: "AG1", value: "Agency One" },
              { key: "AG2", value: "Agency Two" },
              { key: "AG3", value: "Agency Three" },
            ],
          };

          render(<AgencyAddEditModal {...customProps} />);

          // The component should render without errors and process the agency data
          expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        });

        test("should handle empty broadcastChannel", () => {
          const customProps = {
            ...defaultProps,
            broadcastChannel: [],
          };

          render(<AgencyAddEditModal {...customProps} />);

          expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        });
      });
    });
  });
});
