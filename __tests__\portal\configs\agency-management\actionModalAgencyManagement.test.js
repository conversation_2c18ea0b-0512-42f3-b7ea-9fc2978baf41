import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  cleanup,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { postAddUpdateAgencyDefinition } from "@/services/agencyManagement";
import AgencyAddEditModal from "@/app/[lang]/portal/configs/agency-management/actionModalAgencyManagement";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

// Mock the services
jest.mock("@/services/agencyManagement", () => ({
  postAddUpdateAgencyDefinition: jest.fn(),
}));

// Mock the utility components and functions
jest.mock("@/components/tools", () => ({
  useResponseHandler: jest.fn(() => ({
    handleResponse: jest.fn(),
    handleError: jest.fn(),
  })),
  getDateFormat: { en: "MM/DD/YYYY", tr: "DD/MM/YYYY" },
  getSpinIndicator: <div>Spinner</div>,
  getModifiedProductNoTitles: jest.fn((dict) => ({
    101: dict.offers.offersCreating.compulsoryTrafficInsurance.title101,
    102: dict.offers.offersCreating.compulsoryTrafficInsurance.title102,
    500: dict.offers.offersCreating.compulsoryTrafficInsurance.title500,
    203: dict.offers.offersCreating.compulsoryTrafficInsurance.title203,
    620: dict.offers.offersCreating.compulsoryTrafficInsurance.title620,
    608: dict.offers.offersCreating.compulsoryTrafficInsurance.title608,
    // Add the key your test is actually using
    P001: "Mocked Product Title",
  })),
}));

describe("AgencyAddEditModal", () => {
  Object.keys({ en: "en", tr: "tr" }).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockGetAgencyManagementPaginatedList;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        dayjs.extend(utc);
      });

      beforeEach(() => {
        cleanup();
        jest.clearAllMocks();
        mockGetAgencyManagementPaginatedList = jest.fn();
        postAddUpdateAgencyDefinition.mockResolvedValue({ status: "SUCCESS" });
      });

      const renderComponent = (props = {}) => {
        return render(
          <AgencyAddEditModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={jest.fn()}
            selectedData={{ productNo: "P001", id: 1, channel: "CH001" }}
            isEdit={false}
            broadcastChannel={[{ key: "CH001", value: "Channel 1" }]}
            requestBody={{ productNo: "P001" }}
            getAgencyManagementPaginatedList={
              mockGetAgencyManagementPaginatedList
            }
            {...props}
          />
        );
      };

      test("form: Bileşenler modalda mevcut olmalıdır", async () => {
        await act(async () => renderComponent());
        expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        expect(screen.getByTestId("begDate")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("saveButton")).toBeInTheDocument();
      });

      test("form: Düzenleme modunda alanları doldurmalıdır", async () => {
        const selectedData = {
          productNo: "101",
          id: 1,
          channel: "CH001",
          begDate: "2025-07-09T00:00:00Z",
          endDate: "2025-12-31T00:00:00Z",
        };

        await act(async () => {
          renderComponent({
            isEdit: true,
            selectedData,
          });
        });

        await waitFor(() => {
          expect(screen.getByText("Channel 1")).toBeInTheDocument();
          const expectedBegDate = lang === "tr" ? "09/07/2025" : "07/09/2025";
          const expectedEndDate = lang === "tr" ? "31/12/2025" : "12/31/2025";
          expect(
            screen.getByTestId("begDate").querySelector("input")
          ).toHaveValue(expectedBegDate);
          expect(
            screen.getByTestId("endDate").querySelector("input")
          ).toHaveValue(expectedEndDate);
        });
      });

      // ✅ CORRECTED TEST USING fireEvent
      // test("form:Save button should submit form with correct values", async () => {
      //   const setIsModalOpen = jest.fn();

      //   await act(async () => {
      //     renderComponent({
      //       setIsModalOpen,
      //       selectedData: { productNo: "P001" },
      //       isEdit: false,
      //     });
      //   });

      //   // 1. Open the Select dropdown
      //   fireEvent.mouseDown(
      //     screen.getByTestId("agencyCode").querySelector(".ant-select-selector")
      //   );

      //   // 2. Wait for the option to appear and click it
      //   await waitFor(() => {
      //     fireEvent.click(screen.getByText("Channel 1"));
      //   });

      //   // 3. Enter a date into the DatePicker input
      //   const begDatePicker = screen
      //     .getByTestId("begDate")
      //     .querySelector("input");
      //   const dateToEnter = lang === "tr" ? "10/07/2025" : "07/10/2025";

      //   fireEvent.change(begDatePicker, { target: { value: dateToEnter } });
      //   // It's good practice to blur the input to ensure the component's state updates
      //   fireEvent.blur(begDatePicker);

      //   // 4. Click the save button
      //   fireEvent.click(screen.getByTestId("saveButton"));

      //   // 5. Assert that the submission handler was called with the correct data
      //   await waitFor(() => {
      //     expect(postAddUpdateAgencyDefinition).toHaveBeenCalledWith(
      //       lang,
      //       expect.objectContaining({
      //         agencyCode: "CH001",
      //         productNo: "P001",
      //         begDate: "2025-07-10T00:00:00.000Z",
      //         endDate: null,
      //         id: null,
      //       })
      //     );
      //     expect(mockGetAgencyManagementPaginatedList).toHaveBeenCalled();
      //     expect(setIsModalOpen).toHaveBeenCalledWith(false);
      //   });
      // });

      test("form: Düzenleme modunda değilken alanlar sıfırlanmalıdır", async () => {
        await act(async () => {
          renderComponent({
            selectedData: { productNo: "P001" },
            isEdit: false,
          });
        });
        const agencyCodeSelect = screen
          .getByTestId("agencyCode")
          .querySelector(".ant-select-selection-item");
        const begDatePicker = screen
          .getByTestId("begDate")
          .querySelector("input");
        const endDatePicker = screen
          .getByTestId("endDate")
          .querySelector("input");
        expect(agencyCodeSelect).toBeNull();
        expect(begDatePicker).toHaveValue("");
        expect(endDatePicker).toHaveValue("");
      });
    });
  });
});
