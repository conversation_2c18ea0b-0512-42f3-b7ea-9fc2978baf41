"use server";

import { getDictionary } from "@/dictionaries";
import OffersListing from "./offersListing";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.offers.offersListing.title} | ${dict.offers.title} | ${dict.public.title}`,
  };
}

export default async function OffersListingPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <OffersListing dict={dict} lang={lang} />;
}
