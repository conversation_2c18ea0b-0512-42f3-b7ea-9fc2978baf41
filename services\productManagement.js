"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const getProductManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/paginated-product-management`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const postProductManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/add-product-management`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const putProductManagement = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/update-product-management`,
    {
      cache: "no-store",
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const getProductManagementSingle = async (lang, id) => {
  let res = await fetch(
    `${process.env.API_URL}offers/${id}/settings/get-product-management`,
    {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

export {
  getProductManagement,
  postProductManagement,
  putProductManagement,
  getProductManagementSingle,
};
