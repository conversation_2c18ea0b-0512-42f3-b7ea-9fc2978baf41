"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const getProductManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/paginated-product-management`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("getProductManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const postProductManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/add-product-management`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("postProductManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const putProductManagement = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/update-product-management`,
    options: {
      cache: "no-store",
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("putProductManagement", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

const getProductManagementSingle = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${id}/settings/get-product-management`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "getProductManagementSingle",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

const getCrossProductSettings = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/${id}/settings/get-cross-product-settings`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "get-cross-product-settings",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};
const postAddUpdateCrossProductSettings = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/add-update-cross-product-settings`,
    options: {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      method: "POST",
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "postAddUpdateCrossProductSettings",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

export {
  getProductManagement,
  postProductManagement,
  putProductManagement,
  getProductManagementSingle,
  getCrossProductSettings,
  postAddUpdateCrossProductSettings,
};
