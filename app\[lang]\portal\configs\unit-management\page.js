"use server";

import { getDictionary } from "@/dictionaries";
import UnitManagement from "./unitManagement";

export async function generateMetadata({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return {
    title: `${dict.configs.unitManagement.title} | ${dict.configs.title} | ${dict.public.title}`,
  };
}

export default async function UnitManagementPage({ params: { lang } }) {
  const dict = await getDictionary(lang);
  return <UnitManagement dict={dict} lang={lang} />;
}
