"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
  logError,
} from "@/components/ssrTools";

const postPaginatedAgencyDefinitions = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/paginated-agency-definition`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "postPaginatedAgencyDefinitions",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

const postAddUpdateAgencyDefinition = async (lang, values) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/add-update-product-management`,
    options: {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError(
      "postAddUpdateAgencyDefinition",
      "fetchFailed",
      request,
      fResponse,
      err
    );
    throw err;
  }
};

const deleteAgencyDefinition = async (lang, id) => {
  let fResponse;
  const request = {
    url: `${process.env.API_URL}offers/settings/${id}/delete-product-management`,
    options: {
      cache: "no-store",
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    },
  };

  try {
    fResponse = await fetch(request.url, request.options);
    const vResponse = await validResponse(fResponse);
    return JSON.parse(vResponse);
  } catch (err) {
    logError("deleteAgencyDefinition", "fetchFailed", request, fResponse, err);
    throw err;
  }
};

export {
  postPaginatedAgencyDefinitions,
  deleteAgencyDefinition,
  postAddUpdateAgencyDefinition,
};
