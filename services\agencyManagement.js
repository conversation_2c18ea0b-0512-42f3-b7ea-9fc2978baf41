"use server";

import {
  getToken,
  getAcceptLanguage,
  validResponse,
} from "@/components/ssrTools";

const postPaginatedAgencyDefinitions = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/paginated-agency-definition`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const postAddUpdateAgencyDefinition = async (lang, values) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/add-update-product-management`,
    {
      cache: "no-store",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
      body: JSON.stringify(values),
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

const deleteAgencyDefinition = async (lang, id) => {
  let res = await fetch(
    `${process.env.API_URL}offers/settings/${id}/delete-product-management`,
    {
      cache: "no-store",
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
        "Accept-Language": getAcceptLanguage(lang),
      },
    }
  );
  res = await validResponse(res);
  return JSON.parse(res);
};

export {
  postPaginatedAgencyDefinitions,
  deleteAgencyDefinition,
  postAddUpdateAgencyDefinition,
};
