"use client";

import { Form, Input, Button, message } from "antd";
import { useState } from "react";
import { postUsersForgotPassword } from "@/services/identityConnect";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { getEmailPattern } from "@/components/tools";
import AuthLayout from "@/components/containers/authLayout";
import ChangePasswordForm from "./changePasswordForm";
import Link from "next/link";

export default function ForgotPasswordForm({ dict, lang }) {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [form] = Form.useForm();
  const [loadingAction, setLoadingAction] = useState(false);
  const [isChangePasswordFormShown, setIsChangePasswordFormShown] =
    useState(false);
  const [mailAddress, setMailAddress] = useState("");
  const [messageApi, contextHolder] = message.useMessage();

  const showMessage = (type, message) => {
    messageApi.open({
      type: type,
      content: message,
    });
  };

  const isError = (message) => {
    setLoadingAction(false);
    showMessage("error", dict.error[message] || message);
  };

  const isSuccess = () => {
    showMessage("success", dict.public.success);
  };

  const onFinish = async (values) => {
    try {
      setLoadingAction(true);
      values.recaptchaToken = await executeRecaptcha("post");
      const res = await postUsersForgotPassword(values, lang);
      if (res?.code == "SUCCESS" || res?.code == "RESOURCE_NOT_FOUND") {
        isSuccess();
        setTimeout(() => {
          setIsChangePasswordFormShown(true);
          setMailAddress(values.email);
          setLoadingAction(false);
        }, 2000);
      } else {
        isError(res?.message);
      }
    } catch (error) {
      if (error?.message != undefined) isError(error?.message);
    }
  };

  return (
    <>
      {contextHolder}
      <AuthLayout
        dict={dict}
        lang={lang}
        title={
          isChangePasswordFormShown
            ? dict.auth.changePasswordTitle
            : dict.auth.forgotPasswordTitle
        }
        description={
          isChangePasswordFormShown
            ? dict.auth.changePasswordTitleDesc
            : dict.auth.forgotPasswordTitleDesc
        }
        minHeight={"1100px"}
      >
        {!isChangePasswordFormShown ? (
          <>
            <Form
              form={form}
              onFinish={onFinish}
              layout="vertical"
              className="!mt-6"
            >
              <Form.Item
                name="email"
                data-testid="email"
                rules={[
                  {
                    required: true,
                    pattern: getEmailPattern,
                    message: `${dict.public.requiredField}`,
                  },
                ]}
              >
                <Input autoFocus placeholder={dict.public.username} />
              </Form.Item>
              <Form.Item className="!mb-0">
                <Button
                  data-testid="buttonSubmit"
                  type="primary"
                  className="w-full"
                  htmlType="submit"
                  loading={loadingAction}
                >
                  {dict.auth.activationMail}
                </Button>
              </Form.Item>
            </Form>
            <div className="my-6 flex flex-col items-center justify-center">
              <span className="text-center text-sm text-gray-600 text-opacity-80">
                {dict.auth.doYouHaveAccount}
              </span>
              <Link
                data-testid="linkAuthLogin"
                className="font-bold text-primary-color"
                href={`/${lang}/auth/login/`}
              >
                {dict.auth.loginSignTitle}
              </Link>
            </div>
          </>
        ) : (
          <ChangePasswordForm
            dict={dict}
            lang={lang}
            mailAddress={mailAddress}
          />
        )}
      </AuthLayout>
    </>
  );
}
